# Quote 浏览器扩展

一个专为法律数据库设计的智能文档采集浏览器扩展，支持自动识别、拦截、下载和处理法律文档。

## 🎯 核心功能

- **智能域名识别**: 自动识别威科先行等法律数据库网站
- **一键文档采集**: 注入"采集到Quote"按钮，一键完成文档采集流程
- **下载请求拦截**: 拦截并处理法律文档下载请求
- **文件自动处理**: 自动下载、解压、过滤和上传文档文件
- **统一身份认证**: 集成Quote统一认证系统

## 📚 文档指南

项目包含以下核心文档，详细说明各个功能模块：

### [01-法律数据库域名识别与下载请求拦截指南](./01-LEGAL_DATABASE_DETECTION_AND_INTERCEPTION.md)

- 如何识别可用的法律数据库域名（如 wkinfo）
- 如何在可用域名下拦截网站的下载请求
- 拦截规则配置和管理
- 扩展新数据库支持的方法

### [02-下载链接构造与文件处理流程](./02-DOWNLOAD_LINK_CONSTRUCTION.md)

- 在拦截到下载请求后，如何构造下载链接
- 完整的文件处理流程（下载→解压→上传）
- 进度管理和错误处理
- 性能优化策略

### [03-"采集到Quote"按钮工作逻辑详解](./03-QUOTE_BUTTON_WORKFLOW.md)

- 注入在页面中的"采集到Quote"按钮工作逻辑
- 按钮组件架构和状态管理
- 完整的采集工作流程
- 用户交互体验设计

### [04-Quote统一身份认证系统实现指南](./04-UNIFIED_AUTHENTICATION_SYSTEM.md)

- 项目如何实现统一身份认证
- SDK集成和配置方法
- 认证流程和状态管理
- 安全特性和自动刷新机制

### [环境变量配置指南](./ENV_VARIABLES_GUIDE.md)

- 环境变量的安全配置和管理
- 构建时注入 vs 运行时读取
- 开发和生产环境配置

## 🚀 快速开始

### 1. 环境准备

```bash
# 克隆项目
git clone <repository-url>
cd quote-browser-extension

# 安装依赖
npm install

# 配置环境变量
cp .env.example .env
# 编辑 .env 文件，填入实际的API地址和配置
```

### 2. 开发模式

```bash
# 启动开发模式（监听文件变化）
npm run dev

# 验证环境变量配置
npm run env:check
```

### 3. 生产构建

```bash
# 构建生产版本
npm run build

# 构建特定浏览器版本
npm run build:chrome
npm run build:firefox
npm run build:edge
```

### 4. 加载扩展

1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

## 🏗️ 项目架构

```
src/
├── background/           # 后台脚本
│   ├── services/        # 后台服务
│   └── simple-test.ts   # 文件处理逻辑
├── contentScript/       # 内容脚本
│   └── sites/          # 各网站适配
│       └── wkinfo/     # 威科先行适配
├── components/          # React组件
│   ├── auth/           # 认证相关组件
│   └── sidepanel/      # 侧边栏组件
├── network/            # 网络拦截器
├── services/           # 核心服务
└── config/             # 配置文件
```

## 🔧 技术栈

- **前端框架**: React 18 + TypeScript
- **构建工具**: Webpack 5
- **样式方案**: Tailwind CSS
- **认证系统**: @quote/auth-client SDK
- **文件处理**: JSZip
- **网络拦截**: Chrome declarativeNetRequest API

## 🌟 支持的网站

目前支持以下法律数据库：

### 威科先行 (WKInfo)

- `law.wkinfo.com.cn` - 新版威科先行
- `lawv4.wkinfo.com.cn` - 旧版威科先行
- `law.wkinf123.ffdtwsa.top` - 域名变体
- 代理服务器域名支持

## 🛠️ 开发指南

### 添加新的法律数据库支持

1. 在 `src/contentScript/sites/` 下创建新的站点目录
2. 实现域名检测逻辑
3. 配置下载按钮识别规则
4. 添加网络拦截规则
5. 测试完整的采集流程

### 调试技巧

```javascript
// 检查域名识别
console.log("当前域名:", window.location.hostname);

// 验证拦截规则
chrome.declarativeNetRequest.getDynamicRules().then(console.log);

// 检查存储的拦截URL
chrome.storage.local.get(["lastInterceptedDownloadUrl"]).then(console.log);
```