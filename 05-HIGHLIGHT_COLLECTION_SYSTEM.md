# 高亮采集系统需求分析文档

## 概述

本文档描述了一个新的文本高亮采集和知识管理系统的需求和实现逻辑。该系统与现有的法律文档采集系统并行运行，为用户提供快速的文本片段采集和知识管理功能。

## 功能需求

### 1. 高亮采集功能

#### 1.1 文本选择和工具栏显示
- **触发条件**: 用户在网页上选中文本
- **工具栏位置**: 出现在选中区域的右下角
- **工具栏内容**: 
  - 左侧: 高亮采集按钮（高亮采集icon）
  - 右侧: 不可点击的Quote icon

#### 1.2 高亮采集流程
1. 用户点击工具栏中的"高亮采集"按钮
2. 选中的文本被高亮（在DOM中添加高亮样式）
3. 内容被采集到临时知识库数组
4. 通知底部浮层更新采集数量
5. 工具栏自动消失

#### 1.3 高亮取消流程
1. 用户鼠标hover到已高亮的文本上
2. 在高亮文本右下角出现工具栏
3. 工具栏中的高亮采集按钮icon变为取消高亮icon
4. 用户点击取消高亮按钮
5. 移除该文本的高亮样式
6. 从临时知识库中移除对应内容
7. 通知浮层更新采集数量
8. 如果是最后一条高亮文本，通知浮层消失并清空知识库

### 2. 工具栏功能

#### 2.1 显示逻辑
- **选中文本时**: 显示高亮采集icon
- **hover高亮文本时**: 显示取消高亮icon
- **消失条件**: 
  - 用户进行页面滚动
  - 用户点击其他页面区域
  - 用户执行高亮/取消高亮操作

#### 2.2 工具栏布局
```
[高亮采集按钮] [Quote Icon]
```
- 左侧: 功能按钮（根据上下文显示不同icon）
- 右侧: 装饰性Quote icon（不可点击）

### 3. 通知浮层功能

#### 3.1 显示位置和样式
- **位置**: 固定在页面底部
- **参考**: 类似现有Quote按钮的注入方式和位置
- **显示时机**: 首次高亮采集后出现

#### 3.2 布局设计
```
[数量图形] 已采集到知识库 [清除按钮] [选择project按钮]
```

#### 3.3 数量显示规则
- **图形样式**: 
  - 1-9位数字: 圆形背景
  - 10-99位数字: 圆角矩形背景
  - 99+位数字: 圆角矩形背景，显示"99+"
- **文本**: "已采集到知识库"
- **按钮**: 清除按钮、选择project按钮

### 4. 清除功能

#### 4.1 清除流程
1. 用户点击通知浮层中的"清除"按钮
2. 弹出确认删除浮窗
3. 用户确认后:
   - 移除页面中所有由扩展添加的高亮样式
   - 关闭通知浮层
   - 清空临时知识库中的文本内容

#### 4.2 注意事项
- 仅清除本次采集的内容，不影响之前已存入项目的内容
- 需要用户确认操作，防止误删

### 5. 项目选择功能

#### 5.1 项目列表显示
- **触发**: 用户点击"选择project"按钮
- **位置**: 从按钮上方弹出
- **内容**: 
  - "all"项目选项
  - 用户自定义项目列表（从知识库获取）

#### 5.2 项目保存逻辑
1. 用户选择某个project选项
2. 当前页面所有高亮文本存入选定项目
3. 页面高亮文本保持不变
4. 可以重复选择不同项目进行保存

#### 5.3 项目切换逻辑
- 用户选择新项目时，自动将当前所有高亮文本转存到新项目
- 原项目中的副本保持不变（如果之前已保存）
- 页面高亮样式不受影响

## 用户交互流程

### 完整流程图
```
用户选中文本
    ↓
工具栏出现（右下角）
    ↓
点击"高亮采集"
    ↓
文本高亮 + 存入临时知识库
    ↓
通知浮层出现（底部）
    ↓
继续采集更多文本...
    ↓
选择操作：
    ├─ 清除：确认后移除所有高亮
    └─ 选择project：保存到指定项目
```

### 状态转换
- **初始状态**: 无高亮，无通知浮层
- **采集状态**: 有高亮，显示通知浮层
- **保存状态**: 高亮保持，通知浮层保持
- **清除状态**: 无高亮，无通知浮层

## 技术实现考虑

### 1. DOM操作
- **高亮样式**: 使用`<span class="highlight">`包装选中文本
- **样式隔离**: 使用特定的CSS类名避免冲突
- **事件监听**: 需要处理文本选择、hover、点击等事件

### 2. 数据存储
- **临时存储**: 使用内存数组存储本次采集内容
- **持久化存储**: 保存到指定项目后调用API存储
- **状态管理**: 需要管理高亮状态、采集数量等

### 3. 兼容性考虑
- **排除站点**: 威科先行、北大法宝、中国裁判文书网案例详情页
- **样式冲突**: 避免与网站原有样式冲突
- **性能优化**: 大量高亮文本时的性能考虑

### 4. 用户体验
- **响应速度**: 工具栏和浮层的快速响应
- **视觉反馈**: 清晰的高亮效果和按钮状态
- **操作确认**: 重要操作（如清除）需要确认

## 与现有系统的关系

### 1. 并行运行
- 新系统与现有法律文档采集系统独立运行
- 不会影响现有Quote按钮的功能
- 共享相同的项目管理和认证系统

### 2. 数据统一
- 使用相同的API端点和认证机制
- 项目列表从现有知识库获取
- 保存的内容格式保持一致

### 3. 界面协调
- 避免与现有UI组件冲突
- 在适配的法律数据库页面禁用新功能
- 保持整体视觉风格一致

## 开发优先级

### Phase 1: 基础功能
1. 文本选择检测和工具栏显示
2. 基本的高亮采集功能
3. 简单的通知浮层
4. 清除功能

### Phase 2: 完善功能
1. hover高亮文本的取消功能
2. 完整的通知浮层样式
3. 项目选择和保存功能
4. 错误处理和边界情况

### Phase 3: 优化和测试
1. 性能优化
2. 兼容性测试
3. 用户体验优化
4. 文档完善

## 风险和挑战

### 1. 技术风险
- DOM操作的稳定性和性能
- 与复杂网站的样式冲突
- 大量文本高亮时的内存管理

### 2. 用户体验风险
- 工具栏位置计算的准确性
- 不同网站的文本选择行为差异
- 移动设备的适配问题

### 3. 兼容性风险
- 特殊网站的DOM结构限制
- 现有扩展功能的冲突
- 浏览器兼容性问题

## 后续步骤

1. **技术方案设计**: 详细的技术架构和实现方案
2. **原型开发**: 创建可交互的原型验证核心功能
3. **分阶段开发**: 按照优先级逐步实现功能
4. **测试和优化**: 全面测试和性能优化
5. **文档完善**: 开发文档和用户指南

---

*文档创建时间: 2025-07-30*