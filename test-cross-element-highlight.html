<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨元素高亮测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #555;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        strong {
            color: #d63384;
        }
        em {
            color: #0d6efd;
            font-style: italic;
        }
        .nested {
            border: 1px solid #ccc;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>跨元素高亮功能测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>这个页面专门用于测试跨元素边界的文本高亮功能。请尝试选择跨越不同HTML元素的文本，观察高亮是否正常工作。</p>
        </div>

        <div class="test-case">
            <h3>测试案例1: 跨段落选择</h3>
            <p>这是第一段文本，包含一些重要信息。</p>
            <p>这是第二段文本，也包含重要内容。</p>
            <p><strong>测试方法</strong>：选择从"第一段文本"到"第二段文本"的跨段落文本。</p>
        </div>

        <div class="test-case">
            <h3>测试案例2: 跨内联元素选择</h3>
            <p>这段文本包含<strong>加粗文字</strong>和<em>斜体文字</em>以及普通文字。</p>
            <p><strong>测试方法</strong>：选择从"包含"到"普通文字"，跨越多个内联元素。</p>
        </div>

        <div class="test-case">
            <h3>测试案例3: 复杂嵌套结构</h3>
            <div class="nested">
                <span>外层文本</span>
                <div>
                    <span>内层文本1</span>
                    <strong>内层加粗</strong>
                    <span>内层文本2</span>
                </div>
                <span>外层结尾</span>
            </div>
            <p><strong>测试方法</strong>：选择从"外层文本"到"外层结尾"，跨越复杂的嵌套结构。</p>
        </div>

        <div class="test-case">
            <h3>测试案例4: 列表跨越</h3>
            <ul>
                <li>第一个列表项包含重要信息</li>
                <li>第二个列表项也很重要</li>
                <li>第三个列表项是结论</li>
            </ul>
            <p><strong>测试方法</strong>：选择从"第一个列表项"到"第三个列表项"的文本。</p>
        </div>

        <div class="test-case">
            <h3>测试案例5: 表格跨越</h3>
            <table border="1" style="border-collapse: collapse; width: 100%;">
                <tr>
                    <td>单元格1内容</td>
                    <td>单元格2内容</td>
                </tr>
                <tr>
                    <td>单元格3内容</td>
                    <td>单元格4内容</td>
                </tr>
            </table>
            <p><strong>测试方法</strong>：选择从"单元格1内容"到"单元格4内容"的跨表格文本。</p>
        </div>

        <div class="test-case">
            <h3>测试案例6: 混合内容</h3>
            <p>这是一个包含<a href="#">链接</a>、<code>代码</code>、<strong>加粗</strong>和<em>斜体</em>的复杂段落。</p>
            <blockquote>
                这是一个引用块，包含重要的引用内容。
            </blockquote>
            <p>引用后的普通段落文本。</p>
            <p><strong>测试方法</strong>：选择从"包含"到"普通段落文本"，跨越多种不同类型的元素。</p>
        </div>

        <h2>🔍 预期行为</h2>
        <ul>
            <li>✅ 选择跨元素文本时，应该能够成功创建高亮</li>
            <li>✅ 高亮应该覆盖所有选中的文本部分</li>
            <li>✅ 每个高亮部分都应该有相同的背景色</li>
            <li>✅ hover任何高亮部分都应该显示取消高亮的工具栏</li>
            <li>✅ 点击取消高亮应该移除所有相关的高亮部分</li>
            <li>✅ 文本不应该发生位移（已修复padding问题）</li>
        </ul>

        <h2>🐛 如果遇到问题</h2>
        <p>请打开浏览器开发者工具的Console标签，查看详细的调试信息：</p>
        <ul>
            <li>高亮创建过程的日志</li>
            <li>元素数量统计</li>
            <li>错误信息（如果有）</li>
        </ul>
    </div>

    <script>
        console.log('跨元素高亮测试页面加载完成');
        
        // 添加测试按钮
        setTimeout(() => {
            const button = document.createElement('button');
            button.textContent = '手动测试工具栏';
            button.style.position = 'fixed';
            button.style.top = '10px';
            button.style.right = '10px';
            button.style.zIndex = '999999';
            button.style.padding = '10px';
            button.style.backgroundColor = '#007bff';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '4px';
            button.onclick = () => {
                if (window.testHighlightSystem) {
                    window.testHighlightSystem();
                } else {
                    console.log('测试方法不存在');
                }
            };
            document.body.appendChild(button);
        }, 1000);
    </script>
</body>
</html>
