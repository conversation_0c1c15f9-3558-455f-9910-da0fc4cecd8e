/** @type {import('jest').Config} */
module.exports = {
  // 测试环境
  testEnvironment: "jsdom",

  // 根目录
  rootDir: ".",

  // 测试文件匹配模式
  testMatch: ["<rootDir>/tests/**/*.test.ts", "<rootDir>/tests/**/*.test.js"],

  // 模块文件扩展名
  moduleFileExtensions: ["ts", "tsx", "js", "jsx", "json"],

  // TypeScript 转换
  transform: {
    "^.+\\.(ts|tsx)$": [
      "ts-jest",
      {
        tsconfig: "tsconfig.json",
      },
    ],
  },

  // 模块名映射
  moduleNameMapping: {
    "^@/(.*)$": "<rootDir>/src/$1",
    "^@tests/(.*)$": "<rootDir>/tests/$1",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$":
      "<rootDir>/tests/__mocks__/fileMock.js",
  },

  // 设置文件
  setupFilesAfterEnv: ["<rootDir>/tests/setup.ts"],

  // 覆盖率配置
  collectCoverageFrom: [
    "src/**/*.{ts,tsx}",
    "!src/**/*.d.ts",
    "!src/manifest.json",
    "!src/**/*.test.{ts,tsx}",
    "!src/assets/**",
    "!src/styles/**",
  ],

  // 覆盖率阈值 - 只对测试的核心模块设置阈值
  coverageThreshold: {
    // 对核心工具类设置更高标准
    "src/utils/TextUtils.ts": {
      branches: 90,
      functions: 100,
      lines: 95,
      statements: 95,
    },
    // 对API服务设置合理标准
    "src/contentScript/services/AuthService.ts": {
      branches: 50,
      functions: 55,
      lines: 60,
      statements: 60,
    },
  },

  // 覆盖率报告格式
  coverageReporters: ["text", "lcov", "html"],

  // 全局变量
  globals: {
    "process.env": {
      NODE_ENV: "test",
      NEXT_PUBLIC_AUTH_SERVICE_API_URL: "https://test-auth.example.com",
      NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL:
        "https://test-redirect.example.com",
      API_BASE_URL: "https://test-api.example.com",
      NEXT_PUBLIC_DEFAULT_PROJECT_ID: "test-project-id",
    },
  },

  // 清除模拟
  clearMocks: true,

  // 详细输出
  verbose: true,

  // 测试超时
  testTimeout: 10000,
};
