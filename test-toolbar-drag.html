<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 200vh; /* 增加页面高度用于测试滚动 */
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .drag-demo {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-column {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
        }
        .cursor-demo {
            display: inline-block;
            padding: 8px 12px;
            border: 1px solid #007bff;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: move;
            user-select: none;
            margin: 5px;
        }
        .cursor-demo:active {
            cursor: grabbing;
            background: #e3f2fd;
            transform: scale(1.02);
        }
        .viewport-demo {
            position: relative;
            width: 300px;
            height: 200px;
            border: 2px solid #007bff;
            margin: 20px 0;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
        }
        .viewport-demo::before {
            content: "视口边界约束演示区域";
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            color: #666;
            font-size: 12px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 工具栏拖拽功能测试</h1>
        
        <div class="success-indicator">
            <strong>🎉 拖拽功能已实现！</strong> 工具栏现在支持通过Quote图标进行拖拽移动。
        </div>

        <div class="feature-highlight">
            <h3>🔧 拖拽功能特性</h3>
            <ol>
                <li><strong>移动光标</strong>：hover Quote图标时显示move光标</li>
                <li><strong>实时拖拽</strong>：拖拽时工具栏实时跟随鼠标</li>
                <li><strong>边界约束</strong>：工具栏不会被拖出视口范围</li>
                <li><strong>拖拽时不消失</strong>：拖拽过程中工具栏保持显示</li>
                <li><strong>样式保持</strong>：拖拽时工具栏有适当的视觉反馈</li>
                <li><strong>性能优化</strong>：使用requestAnimationFrame确保流畅性</li>
            </ol>
        </div>

        <h2>🧪 测试场景</h2>
        
        <div class="test-case">
            <h3>测试1: 基础拖拽功能</h3>
            <div class="test-text">
                请选择这段文字创建高亮，然后将鼠标悬停在工具栏的Quote图标上，观察光标是否变为移动箭头。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>选择上面的文字并创建高亮</li>
                <li>hover到工具栏右侧的Quote图标</li>
                <li>观察光标是否变为move箭头</li>
                <li>按住鼠标左键拖拽Quote图标</li>
                <li>观察工具栏是否跟随鼠标移动</li>
            </ol>
            <p><strong>预期结果</strong>：✅ Quote图标显示move光标，拖拽时工具栏实时跟随</p>
        </div>

        <div class="test-case">
            <h3>测试2: 边界约束验证</h3>
            <div class="viewport-demo"></div>
            <div class="test-text">
                创建高亮后，尝试将工具栏拖拽到页面边缘，验证边界约束功能。
            </div>
            <p><strong>测试方法</strong>：</p>
            <ul>
                <li>向左拖拽：工具栏不应超出左边界</li>
                <li>向右拖拽：工具栏不应超出右边界</li>
                <li>向上拖拽：工具栏不应超出上边界</li>
                <li>向下拖拽：工具栏不应超出下边界</li>
            </ul>
            <p><strong>预期结果</strong>：✅ 工具栏始终保持在视口范围内</p>
        </div>

        <div class="test-case">
            <h3>测试3: 拖拽时的视觉反馈</h3>
            <div class="test-text">
                观察拖拽过程中工具栏的视觉变化，包括阴影、缩放和Quote图标的状态。
            </div>
            <p><strong>视觉效果验证</strong>：</p>
            <ul>
                <li>拖拽时工具栏有更深的阴影</li>
                <li>拖拽时工具栏轻微放大(scale 1.02)</li>
                <li>拖拽时Quote图标有背景色和放大效果</li>
                <li>拖拽时光标变为grabbing状态</li>
            </ul>
            <p><strong>预期结果</strong>：✅ 拖拽时有清晰的视觉反馈</p>
        </div>

        <div class="test-case">
            <h3>测试4: 拖拽与其他功能的兼容性</h3>
            <div class="drag-demo">
                <div class="demo-column">
                    <h4>第一段高亮文本</h4>
                    <div class="test-text">
                        这是第一段需要高亮的文本内容，用于测试拖拽功能与hover切换的兼容性。
                    </div>
                </div>
                <div class="demo-column">
                    <h4>第二段高亮文本</h4>
                    <div class="test-text">
                        这是第二段需要高亮的文本内容，也用于测试拖拽功能的兼容性。
                    </div>
                </div>
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>分别在两段文字中创建高亮</li>
                <li>hover第一段高亮文字显示工具栏</li>
                <li>拖拽工具栏到新位置</li>
                <li>hover第二段高亮文字</li>
                <li>观察工具栏是否正常滑动到新位置</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 拖拽不影响hover切换和滑动动效</p>
        </div>

        <div class="test-case">
            <h3>测试5: 光标状态演示</h3>
            <p>下面的演示元素模拟了Quote图标的光标状态：</p>
            <div style="text-align: center;">
                <div class="cursor-demo">
                    hover我：显示move光标
                </div>
                <div class="cursor-demo">
                    按住拖拽：显示grabbing光标
                </div>
            </div>
            <p><strong>说明</strong>：实际的Quote图标应该有相同的光标行为</p>
        </div>

        <h2>🔍 技术实现细节</h2>
        
        <div class="feature-highlight">
            <h3>拖拽算法说明</h3>
            <ul>
                <li><strong>事件绑定</strong>：mousedown开始，mousemove更新，mouseup结束</li>
                <li><strong>位置计算</strong>：基于鼠标移动距离和初始位置计算新坐标</li>
                <li><strong>边界检测</strong>：constrainToViewport方法确保工具栏不超出视口</li>
                <li><strong>性能优化</strong>：requestAnimationFrame优化拖拽流畅性</li>
                <li><strong>事件冲突处理</strong>：拖拽时禁用hover事件和工具栏隐藏</li>
                <li><strong>状态管理</strong>：dragState跟踪拖拽状态和相关数据</li>
            </ul>
        </div>

        <div style="height: 500px; background: linear-gradient(to bottom, #f0f8ff, #e6f3ff); margin: 40px 0; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <div style="text-align: center; color: #666;">
                <h3>滚动测试区域</h3>
                <p>在这个区域创建高亮，然后滚动页面测试拖拽功能</p>
                <div class="test-text" style="max-width: 400px;">
                    这是一个用于测试滚动情况下拖拽功能的文本区域。请选择这段文字创建高亮，然后测试在页面滚动状态下的拖拽效果。
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('工具栏拖拽功能测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，拖拽功能已生效');
                
                // 检查拖拽状态
                if (highlightSystem.dragState) {
                    console.log('✅ 拖拽状态管理已初始化');
                } else {
                    console.log('ℹ️ 拖拽状态可能在私有属性中');
                }
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);

        // 监听拖拽相关事件
        document.addEventListener('mousedown', (e) => {
            if (e.target.closest('.quote-icon')) {
                console.log('🎯 检测到Quote图标mousedown事件');
            }
        });

        document.addEventListener('mousemove', (e) => {
            // 只在拖拽时记录
            if (document.querySelector('.quote-selection-toolbar.dragging')) {
                console.log('🔄 工具栏拖拽中...');
            }
        });

        document.addEventListener('mouseup', (e) => {
            if (document.querySelector('.quote-selection-toolbar.dragging')) {
                console.log('✅ 工具栏拖拽结束');
            }
        });
    </script>
</body>
</html>
