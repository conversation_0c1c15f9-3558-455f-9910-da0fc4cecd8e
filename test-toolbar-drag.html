<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 200vh; /* 增加页面高度以测试滚动 */
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .drag-demo {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-column {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
        }
        .cursor-demo {
            display: inline-block;
            padding: 8px 12px;
            border: 1px solid #007bff;
            border-radius: 4px;
            background: #f8f9fa;
            cursor: grab;
            margin: 5px;
            user-select: none;
        }
        .cursor-demo:active {
            cursor: grabbing;
            background: #e9ecef;
        }
        .viewport-demo {
            position: relative;
            width: 300px;
            height: 200px;
            border: 2px solid #007bff;
            margin: 20px 0;
            background: #f8f9fa;
            overflow: hidden;
        }
        .draggable-item {
            position: absolute;
            top: 50px;
            left: 50px;
            width: 80px;
            height: 40px;
            background: #007bff;
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: grab;
            user-select: none;
            font-size: 12px;
        }
        .draggable-item:active {
            cursor: grabbing;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 工具栏拖拽功能测试</h1>
        
        <div class="success-indicator">
            <strong>🎉 拖拽功能已实现！</strong> Quote图标现在支持拖拽移动工具栏位置。
        </div>

        <div class="feature-highlight">
            <h3>🔧 拖拽功能特性</h3>
            <ol>
                <li><strong>移动光标</strong>：hover Quote图标时显示grab光标</li>
                <li><strong>实时拖拽</strong>：拖拽时工具栏实时跟随鼠标</li>
                <li><strong>边界约束</strong>：工具栏不会被拖出视口边界</li>
                <li><strong>拖拽时不消失</strong>：拖拽过程中工具栏保持显示</li>
                <li><strong>样式保持</strong>：拖拽时工具栏样式不变，增强阴影效果</li>
                <li><strong>性能优化</strong>：使用requestAnimationFrame优化拖拽性能</li>
            </ol>
        </div>

        <h2>🧪 测试场景</h2>
        
        <div class="test-case">
            <h3>场景1: 基础拖拽测试</h3>
            <div class="test-text">
                请选择这段文字创建高亮，然后将鼠标悬停在高亮文本上显示工具栏，接着尝试拖拽Quote图标移动工具栏位置。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>选择上面的文字并创建高亮</li>
                <li>hover高亮文本显示工具栏</li>
                <li>将鼠标移动到Quote图标上，观察光标变化</li>
                <li>按住Quote图标并拖拽，观察工具栏移动</li>
            </ol>
            <p><strong>预期结果</strong>：</p>
            <ul>
                <li>✅ hover Quote图标时显示grab光标</li>
                <li>✅ 拖拽时显示grabbing光标</li>
                <li>✅ 工具栏实时跟随鼠标移动</li>
                <li>✅ 拖拽时工具栏有增强阴影效果</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>场景2: 边界约束测试</h3>
            <div class="drag-demo">
                <div class="demo-column">
                    <h4>左侧文本</h4>
                    <div class="test-text">
                        选择这段靠近左边界的文本，测试工具栏拖拽到左边界的约束效果。
                    </div>
                </div>
                <div class="demo-column">
                    <h4>右侧文本</h4>
                    <div class="test-text">
                        选择这段靠近右边界的文本，测试工具栏拖拽到右边界的约束效果。
                    </div>
                </div>
            </div>
            <p><strong>测试方法</strong>：</p>
            <ol>
                <li>分别在左右两侧创建高亮</li>
                <li>尝试将工具栏拖拽到视口边界外</li>
                <li>观察边界约束是否生效</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏无法被拖出视口边界</p>
        </div>

        <div class="test-case">
            <h3>场景3: 光标样式演示</h3>
            <div class="feature-highlight">
                <p>以下是拖拽光标的演示效果：</p>
                <div class="cursor-demo">hover我 - grab光标</div>
                <div class="cursor-demo">按住拖拽我 - grabbing光标</div>
            </div>
            <p><strong>说明</strong>：Quote图标应该有相同的光标效果</p>
        </div>

        <div class="test-case">
            <h3>场景4: 边界约束演示</h3>
            <div class="viewport-demo">
                <div class="draggable-item" id="demo-draggable">拖拽我</div>
            </div>
            <p><strong>说明</strong>：上面的蓝色方块演示了边界约束效果，工具栏应该有相似的约束行为</p>
        </div>

        <div class="test-case">
            <h3>场景5: 滚动页面测试</h3>
            <div class="test-text">
                创建高亮后，尝试滚动页面，然后拖拽工具栏，测试在不同滚动位置下的拖拽效果。
            </div>
            <p><strong>测试方法</strong>：</p>
            <ol>
                <li>创建高亮并显示工具栏</li>
                <li>滚动页面到不同位置</li>
                <li>拖拽工具栏测试位置计算</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 滚动后拖拽仍然正常工作</p>
        </div>

        <h2>🔍 技术实现详情</h2>
        
        <div class="feature-highlight">
            <h3>核心技术点</h3>
            <ul>
                <li><strong>事件处理</strong>：mousedown → mousemove → mouseup 事件链</li>
                <li><strong>位置计算</strong>：考虑滚动偏移和视口边界</li>
                <li><strong>性能优化</strong>：requestAnimationFrame + 边界约束算法</li>
                <li><strong>样式管理</strong>：动态添加/移除拖拽样式类</li>
                <li><strong>事件清理</strong>：destroy方法中正确清理事件监听器</li>
            </ul>
            
            <h3>拖拽状态管理</h3>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 12px;">
dragState = {
  isDragging: false,
  startX: 0, startY: 0,
  initialToolbarX: 0, initialToolbarY: 0,
  dragOffset: { x: 0, y: 0 }
}
            </pre>
        </div>

        <!-- 添加更多内容以测试滚动 -->
        <div style="height: 500px; background: linear-gradient(45deg, #f0f0f0, #e0e0e0); margin: 50px 0; display: flex; align-items: center; justify-content: center; border-radius: 8px;">
            <div style="text-align: center; color: #666;">
                <h3>滚动测试区域</h3>
                <p>在这个区域创建高亮，然后测试拖拽功能</p>
                <div class="test-text" style="max-width: 400px; margin: 20px auto;">
                    这是一个位于页面中间的测试文本，用于验证在不同滚动位置下工具栏拖拽功能的正确性。
                </div>
            </div>
        </div>
    </div>

    <script>
        console.log('工具栏拖拽功能测试页面加载完成');
        
        // 简单的拖拽演示实现
        const demoDraggable = document.getElementById('demo-draggable');
        let isDragging = false;
        let startX, startY, initialX, initialY;
        
        demoDraggable.addEventListener('mousedown', (e) => {
            isDragging = true;
            startX = e.clientX;
            startY = e.clientY;
            const rect = demoDraggable.getBoundingClientRect();
            initialX = rect.left;
            initialY = rect.top;
            demoDraggable.style.cursor = 'grabbing';
        });
        
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            
            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;
            
            let newX = initialX + deltaX;
            let newY = initialY + deltaY;
            
            // 边界约束
            const container = demoDraggable.parentElement;
            const containerRect = container.getBoundingClientRect();
            const itemRect = demoDraggable.getBoundingClientRect();
            
            newX = Math.max(0, Math.min(newX - containerRect.left, containerRect.width - itemRect.width));
            newY = Math.max(0, Math.min(newY - containerRect.top, containerRect.height - itemRect.height));
            
            demoDraggable.style.left = newX + 'px';
            demoDraggable.style.top = newY + 'px';
        });
        
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                demoDraggable.style.cursor = 'grab';
            }
        });
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，拖拽功能已生效');
                
                // 检查拖拽状态
                if (highlightSystem.dragState) {
                    console.log('✅ 拖拽状态管理已初始化');
                } else {
                    console.log('ℹ️ 拖拽状态可能在私有属性中');
                }
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);
    </script>
</body>
</html>
