<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>高亮采集系统测试页面</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.6;
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }
      p {
        margin-bottom: 15px;
        text-align: justify;
      }
      .instructions {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
      }
    </style>
  </head>
  <body>
    <div class="content">
      <h1>高亮采集系统测试页面</h1>

      <div class="instructions">
        <h3>📋 测试说明</h3>
        <p>请选中下方任意文本段落，观察是否出现工具栏。</p>
      </div>

      <p>
        这是第一段测试文本。人工智能（Artificial
        Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。
      </p>

      <p>
        这是第二段测试文本。机器学习是人工智能的一个重要分支，它是一种通过算法使机器能够从数据中学习并做出决策或预测的方法。
      </p>

      <p>
        这是第三段测试文本。深度学习是机器学习的一个子集，它模仿人脑神经网络的结构和功能。
      </p>
    </div>

    <script>
      // 添加调试信息
      console.log("测试页面加载完成");

      // 检查扩展是否加载
      setTimeout(() => {
        const containers = document.querySelectorAll('[id*="quote"]');
        console.log("页面中的Quote相关元素:", containers);

        // 检查是否有高亮系统的容器
        const toolbarContainer = document.getElementById(
          "quote-selection-toolbar-container"
        );
        const notificationContainer = document.getElementById(
          "quote-notification-container"
        );

        console.log("高亮系统容器状态:", {
          toolbarContainer: !!toolbarContainer,
          notificationContainer: !!notificationContainer,
        });

        // 检查全局实例
        console.log("全局高亮系统实例:", window.highlightSystemInstance);
        console.log("测试方法:", window.testHighlightSystem);
      }, 2000);

      // 监听文本选择事件
      document.addEventListener("mouseup", () => {
        const selection = window.getSelection();
        console.log("页面文本选择:", {
          hasSelection: !!selection,
          isCollapsed: selection?.isCollapsed,
          selectedText: selection?.toString(),
        });
      });

      // 添加手动测试按钮
      setTimeout(() => {
        const button = document.createElement("button");
        button.textContent = "手动测试工具栏";
        button.style.position = "fixed";
        button.style.top = "10px";
        button.style.right = "10px";
        button.style.zIndex = "999999";
        button.style.padding = "10px";
        button.style.backgroundColor = "#007bff";
        button.style.color = "white";
        button.style.border = "none";
        button.style.borderRadius = "4px";
        button.onclick = () => {
          if (window.testHighlightSystem) {
            window.testHighlightSystem();
          } else {
            console.log("测试方法不存在");
          }
        };
        document.body.appendChild(button);
      }, 3000);
    </script>
  </body>
</html>
