<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格布局保护测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .before-after {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .before-after > div {
            flex: 1;
            padding: 15px;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .before {
            background-color: #ffe6e6;
        }
        .after {
            background-color: #e6ffe6;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>表格布局保护测试</h1>
        
        <div class="instructions">
            <h3>📋 测试目标</h3>
            <p>验证新的文本节点分裂算法是否能够保护表格布局不被破坏。</p>
            <p><strong>测试方法</strong>：选择跨表格的文本（包括表格外和表格内），观察表格布局是否保持不变。</p>
        </div>

        <div class="test-case">
            <h3>测试案例1: 表格外到表格内的混合选择</h3>
            <p>这是表格前的文本内容，包含重要信息。</p>
            
            <table>
                <thead>
                    <tr>
                        <th>列1标题</th>
                        <th>列2标题</th>
                        <th>列3标题</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>第一行第一列数据</td>
                        <td>第一行第二列数据</td>
                        <td>第一行第三列数据</td>
                    </tr>
                    <tr>
                        <td>第二行第一列数据</td>
                        <td>第二行第二列数据</td>
                        <td>第二行第三列数据</td>
                    </tr>
                </tbody>
            </table>
            
            <p>这是表格后的文本内容，也包含重要信息。</p>
            
            <p><strong>测试方法</strong>：选择从"表格前的文本"到"第二行第三列数据"的跨区域文本。</p>
        </div>

        <div class="test-case">
            <h3>测试案例2: 复杂表格结构</h3>
            <p>复杂表格测试开始。</p>
            
            <table>
                <thead>
                    <tr>
                        <th rowspan="2">合并行标题</th>
                        <th colspan="2">合并列标题</th>
                        <th>普通标题</th>
                    </tr>
                    <tr>
                        <th>子标题1</th>
                        <th>子标题2</th>
                        <th>子标题3</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>数据A1</td>
                        <td>数据B1</td>
                        <td>数据C1</td>
                        <td>数据D1</td>
                    </tr>
                    <tr>
                        <td>数据A2</td>
                        <td colspan="2">跨列数据BC2</td>
                        <td>数据D2</td>
                    </tr>
                </tbody>
            </table>
            
            <p>复杂表格测试结束。</p>
            
            <p><strong>测试方法</strong>：选择从"复杂表格测试开始"到"数据D2"的文本。</p>
        </div>

        <div class="test-case">
            <h3>测试案例3: 嵌套内容表格</h3>
            <p>嵌套内容测试。</p>
            
            <table>
                <tr>
                    <td>
                        <strong>加粗文本</strong>和<em>斜体文本</em>
                        <ul>
                            <li>列表项1</li>
                            <li>列表项2</li>
                        </ul>
                    </td>
                    <td>
                        <div>
                            <span>嵌套span</span>
                            <p>嵌套段落</p>
                        </div>
                    </td>
                    <td>
                        <a href="#">链接文本</a>
                        <br>
                        <code>代码文本</code>
                    </td>
                </tr>
            </table>
            
            <p>嵌套内容测试结束。</p>
            
            <p><strong>测试方法</strong>：选择从"嵌套内容测试"到"代码文本"的复杂嵌套文本。</p>
        </div>

        <h2>🔍 预期行为</h2>
        <div class="before-after">
            <div class="before">
                <h4>❌ 修复前的问题</h4>
                <ul>
                    <li>表格列宽发生变化</li>
                    <li>单元格高度异常</li>
                    <li>表格整体布局错乱</li>
                    <li>文本对齐方式改变</li>
                </ul>
            </div>
            <div class="after">
                <h4>✅ 修复后的效果</h4>
                <ul>
                    <li>表格布局完全保持不变</li>
                    <li>高亮效果正常显示</li>
                    <li>跨区域选择正常工作</li>
                    <li>文本位置无任何偏移</li>
                </ul>
            </div>
        </div>

        <h2>🧪 测试步骤</h2>
        <ol>
            <li><strong>重新加载扩展</strong>：确保使用最新版本</li>
            <li><strong>选择跨区域文本</strong>：从表格外文本选择到表格内文本</li>
            <li><strong>观察表格布局</strong>：高亮后表格是否保持原样</li>
            <li><strong>测试hover功能</strong>：鼠标悬停在高亮文本上</li>
            <li><strong>测试删除功能</strong>：点击取消高亮按钮</li>
        </ol>

        <h2>📊 技术改进点</h2>
        <ul>
            <li><strong>统一算法</strong>：表格内外使用相同的文本节点分裂算法</li>
            <li><strong>CSS保护</strong>：表格内高亮元素使用特殊的重置样式</li>
            <li><strong>DOM安全</strong>：使用DocumentFragment确保安全的DOM操作</li>
            <li><strong>错误处理</strong>：包含完整的回退机制</li>
        </ul>
    </div>

    <script>
        console.log('表格布局保护测试页面加载完成');
        
        // 添加测试按钮
        setTimeout(() => {
            const button = document.createElement('button');
            button.textContent = '手动测试工具栏';
            button.style.position = 'fixed';
            button.style.top = '10px';
            button.style.right = '10px';
            button.style.zIndex = '999999';
            button.style.padding = '10px';
            button.style.backgroundColor = '#007bff';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '4px';
            button.onclick = () => {
                if (window.testHighlightSystem) {
                    window.testHighlightSystem();
                } else {
                    console.log('测试方法不存在');
                }
            };
            document.body.appendChild(button);
        }, 1000);
    </script>
</body>
</html>
