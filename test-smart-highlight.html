<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能高亮算法测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            background-color: #fafafa;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 10px 0;
        }
        td, th {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .expected-result {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-section">
        <h1>🧠 智能高亮算法测试页面</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p>这个页面用于测试新的智能文本片段高亮算法，该算法借鉴了Remio扩展的成功经验。</p>
            <p><strong>测试重点</strong>：验证跨表格选择时不会破坏HTML结构，只在安全位置插入高亮span。</p>
        </div>

        <div class="test-case">
            <h3>测试案例1: 简单文本高亮</h3>
            <p>这是一段普通的文本内容，用于测试基础的文本高亮功能。请选择这段文字的任意部分进行高亮测试。</p>
            <div class="expected-result">
                <strong>预期结果</strong>：选中的文本被正确高亮，不影响周围文本布局。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例2: 跨元素文本高亮</h3>
            <p>这段文本包含<strong>粗体文字</strong>和<em>斜体文字</em>，测试跨越多个HTML元素的文本选择高亮效果。</p>
            <div class="expected-result">
                <strong>预期结果</strong>：每个文本片段被独立高亮，保持原有的HTML结构。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例3: 表格内文本高亮</h3>
            <table>
                <thead>
                    <tr>
                        <th>列标题1</th>
                        <th>列标题2</th>
                        <th>列标题3</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>单元格A1内容</td>
                        <td>单元格B1内容</td>
                        <td>单元格C1内容</td>
                    </tr>
                    <tr>
                        <td>单元格A2内容</td>
                        <td>单元格B2内容</td>
                        <td>单元格C2内容</td>
                    </tr>
                </tbody>
            </table>
            <p><strong>测试方法</strong>：选择从"单元格A1内容"到"单元格C2内容"的跨表格文本。</p>
            <div class="expected-result">
                <strong>预期结果</strong>：只有单元格内的文本被高亮，表格结构（table、tr、td标签）完全不受影响。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例4: 复杂表格结构</h3>
            <table>
                <tr>
                    <td rowspan="2">
                        <div>
                            <span>嵌套内容1</span>
                            <p>段落内容</p>
                        </div>
                    </td>
                    <td>普通单元格1</td>
                </tr>
                <tr>
                    <td>
                        <ul>
                            <li>列表项1</li>
                            <li>列表项2</li>
                        </ul>
                    </td>
                </tr>
            </table>
            <p><strong>测试方法</strong>：选择跨越复杂嵌套结构的文本。</p>
            <div class="expected-result">
                <strong>预期结果</strong>：算法能够智能识别安全的容器，避免在表格结构元素中插入span。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例5: 包含空白字符的选择</h3>
            <table>
                <tr>
                    <td>
                        文本内容1
                    </td>
                    <td>
                        文本内容2
                    </td>
                </tr>
            </table>
            <p><strong>测试方法</strong>：选择包含换行符和空格的文本。</p>
            <div class="expected-result">
                <strong>预期结果</strong>：算法自动过滤空白字符节点，只高亮有意义的文本内容。
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 调试信息</h2>
        <div id="debug-info">
            <p>打开浏览器控制台查看详细的调试日志。</p>
        </div>
    </div>

    <script>
        // 添加调试信息
        console.log('智能高亮算法测试页面加载完成');
        
        // 检查扩展是否加载
        setTimeout(() => {
            const toolbarContainer = document.getElementById('quote-selection-toolbar-container');
            const notificationContainer = document.getElementById('quote-notification-container');
            
            console.log('高亮系统状态:', {
                toolbarContainer: !!toolbarContainer,
                notificationContainer: !!notificationContainer,
                highlightSystemInstance: !!window.highlightSystemInstance
            });

            // 更新页面调试信息
            const debugInfo = document.getElementById('debug-info');
            debugInfo.innerHTML = `
                <p><strong>扩展状态</strong>:</p>
                <ul>
                    <li>工具栏容器: ${toolbarContainer ? '✅ 已创建' : '❌ 未找到'}</li>
                    <li>通知栏容器: ${notificationContainer ? '✅ 已创建' : '❌ 未找到'}</li>
                    <li>高亮系统实例: ${window.highlightSystemInstance ? '✅ 已初始化' : '❌ 未初始化'}</li>
                </ul>
                <p><strong>测试提示</strong>: 选择任意文本后，观察控制台日志中的"智能文本片段算法"相关信息。</p>
            `;
        }, 2000);
        
        // 监听文本选择事件
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            if (selection && !selection.isCollapsed) {
                console.log('文本选择事件:', {
                    selectedText: selection.toString(),
                    rangeCount: selection.rangeCount,
                    startContainer: selection.getRangeAt(0).startContainer,
                    endContainer: selection.getRangeAt(0).endContainer
                });
            }
        });
    </script>
</body>
</html>
