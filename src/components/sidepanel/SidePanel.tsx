import React from "react";

/**
 * 侧边栏组件 - 简化版本，不包含认证功能
 */
export const SidePanel: React.FC = () => {
  return (
    <div className="h-full flex flex-col">
      <header className="bg-blue-600 text-white p-4 shadow-md">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-bold">Quote Extension</h1>
        </div>
      </header>

      <main className="flex-1 overflow-y-auto p-6">
        <div className="h-full flex items-center justify-center">
          <div className="text-center text-gray-500 dark:text-gray-400">
            <div className="mb-4">
              <svg
                className="w-16 h-16 mx-auto text-gray-300 dark:text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1.5}
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <h3 className="text-lg font-medium mb-2">Quote 法律文档采集扩展</h3>
            <p className="text-sm">
              扩展已准备就绪，您可以在网页上使用相关功能。
            </p>
          </div>
        </div>
      </main>
    </div>
  );
};
