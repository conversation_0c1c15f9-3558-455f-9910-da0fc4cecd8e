import React, { useEffect, useState } from "react";
import { initAuthClient } from "@quote/auth-client/react";
import {
  getAuthServiceApiUrl,
  getAuthServiceRedirectUrl,
  validateAuthEnv,
} from "../../utils/env";

interface AuthProviderProps {
  children: React.ReactNode;
}

/**
 * 认证提供者组件
 * 负责初始化认证客户端并提供认证上下文
 */
export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [initError, setInitError] = useState<string | null>(null);

  useEffect(() => {
    const initAuth = async () => {
      try {
        // 验证认证环境变量
        if (!validateAuthEnv()) {
          throw new Error("认证环境变量未正确配置");
        }

        // 初始化认证客户端
        initAuthClient(getAuthServiceApiUrl(), getAuthServiceRedirectUrl(), {
          // 可选：配置敏感域名列表
          sensitiveHostnames: [],
          // 可选：配置请求超时时间
          timeout: 15000,
        });

        console.log("认证客户端初始化成功");
        setIsInitialized(true);
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "认证客户端初始化失败";
        console.error("认证客户端初始化失败:", error);
        setInitError(errorMessage);
        setIsInitialized(true); // 即使失败也设置为已初始化，让组件可以显示错误
      }
    };

    initAuth();
  }, []);

  // 如果还没有初始化完成，显示加载状态
  if (!isInitialized) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
        <div className="text-center">
          <div className="mb-4">
            <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Initializing Authentication
          </h3>
          <p className="text-gray-600 text-sm">
            Please wait while we set up the authentication system...
          </p>
        </div>
      </div>
    );
  }

  // 如果初始化失败，显示错误信息
  if (initError) {
    return (
      <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
        <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
          <div className="mb-4">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg
                className="w-8 h-8 text-red-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
                />
              </svg>
            </div>
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Authentication Setup Failed
            </h2>
            <p className="text-gray-600 text-sm mb-4">{initError}</p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Retry
            </button>
          </div>
        </div>
      </div>
    );
  }

  // 初始化成功，渲染子组件
  return <>{children}</>;
};
