import React from "react";

/**
 * 认证状态加载组件
 * 在检查用户认证状态时显示
 */
export const AuthLoading: React.FC = () => {
  return (
    <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
      <div className="text-center">
        {/* 加载动画 */}
        <div className="mb-4">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto"></div>
        </div>
        
        {/* 加载文本 */}
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Checking Authentication
        </h3>
        <p className="text-gray-600 text-sm">
          Please wait while we verify your login status...
        </p>
      </div>
    </div>
  );
};
