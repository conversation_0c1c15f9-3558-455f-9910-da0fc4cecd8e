import React from "react";

interface AuthErrorProps {
  error: string;
  statusCode?: number;
  onRetry: () => void;
  onLogin: () => void;
}

/**
 * 认证错误组件
 * 当认证过程中出现错误时显示
 */
export const AuthError: React.FC<AuthErrorProps> = ({
  error,
  statusCode,
  onRetry,
  onLogin,
}) => {
  const getErrorMessage = () => {
    switch (statusCode) {
      case 401:
        return "Your session has expired. Please log in again.";
      case 408:
        return "Request timeout. Please check your network connection.";
      case 500:
        return "System temporarily unavailable. Please try again later.";
      default:
        return error || "An unexpected error occurred.";
    }
  };

  const getErrorIcon = () => {
    switch (statusCode) {
      case 401:
        return (
          <svg
            className="w-8 h-8 text-yellow-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        );
      case 408:
        return (
          <svg
            className="w-8 h-8 text-orange-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
            />
          </svg>
        );
      default:
        return (
          <svg
            className="w-8 h-8 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
            />
          </svg>
        );
    }
  };

  return (
    <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
        {/* 错误图标 */}
        <div className="mb-4">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            {getErrorIcon()}
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Authentication Error
          </h2>
          <p className="text-gray-600 text-sm">{getErrorMessage()}</p>
        </div>

        {/* 状态码显示 */}
        {statusCode && (
          <div className="mb-4 p-2 bg-gray-100 rounded text-xs text-gray-500">
            Error Code: {statusCode}
          </div>
        )}

        {/* 操作按钮 */}
        <div className="space-y-3">
          {statusCode === 401 ? (
            <button
              onClick={onLogin}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Log In Again
            </button>
          ) : (
            <button
              onClick={onRetry}
              className="w-full py-2 px-4 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
            >
              Retry
            </button>
          )}
          
          <button
            onClick={onRetry}
            className="w-full py-2 px-4 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>
    </div>
  );
};
