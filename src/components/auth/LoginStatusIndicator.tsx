import React from "react";

interface LoginStatusIndicatorProps {
  isChecking: boolean;
}

/**
 * 登录状态指示器
 * 显示系统正在检查认证状态
 */
export const LoginStatusIndicator: React.FC<LoginStatusIndicatorProps> = ({
  isChecking,
}) => {
  if (!isChecking) return null;

  return (
    <div className="fixed top-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-md shadow-lg flex items-center space-x-2 z-50">
      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
      <span className="text-sm">Checking login status...</span>
    </div>
  );
};
