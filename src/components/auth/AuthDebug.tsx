import React from "react";
import {
  getAuthServiceApiUrl,
  getAuthServiceRedirectUrl,
  getAuthDomain,
  validateAuthEnv,
} from "../../utils/env";

/**
 * 认证调试组件
 * 用于显示认证配置信息，帮助调试
 */
export const AuthDebug: React.FC = () => {
  const isValidConfig = validateAuthEnv();

  return (
    <div className="p-4 bg-gray-100 border rounded-md text-xs">
      <h3 className="font-semibold mb-2">Auth Configuration Debug</h3>
      <div className="space-y-1">
        <div>
          <span className="font-medium">Valid Config:</span>{" "}
          <span className={isValidConfig ? "text-green-600" : "text-red-600"}>
            {isValidConfig ? "✓" : "✗"}
          </span>
        </div>
        <div>
          <span className="font-medium">API URL:</span>{" "}
          <span className="text-gray-600">
            {getAuthServiceApiUrl() || "Not configured"}
          </span>
        </div>
        <div>
          <span className="font-medium">Redirect URL:</span>{" "}
          <span className="text-gray-600">
            {getAuthServiceRedirectUrl() || "Not configured"}
          </span>
        </div>
        <div>
          <span className="font-medium">Domain:</span>{" "}
          <span className="text-gray-600">
            {getAuthDomain() || "Not configured"}
          </span>
        </div>
      </div>
    </div>
  );
};
