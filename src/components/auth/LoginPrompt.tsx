import React from "react";
import { AuthDebug } from "./AuthDebug";

interface LoginPromptProps {
  onLogin: () => void;
  loading?: boolean;
  error?: string | null;
}

/**
 * 登录提示组件
 * 当用户未登录时显示此组件
 */
export const LoginPrompt: React.FC<LoginPromptProps> = ({
  onLogin,
  loading = false,
  error = null,
}) => {
  return (
    <div className="flex flex-col items-center justify-center h-full p-6 bg-gray-50">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6 text-center">
        {/* Logo 或图标区域 */}
        <div className="mb-6">
          <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg
              className="w-8 h-8 text-blue-600"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
          </div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            Welcome to Quote Extension
          </h2>
          <p className="text-gray-600 text-sm">
            Please log in to access the extension features
          </p>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-red-600 text-sm">{error}</p>
          </div>
        )}

        {/* 登录按钮 */}
        <button
          onClick={onLogin}
          disabled={loading}
          className={`w-full py-3 px-4 rounded-md font-medium text-white transition-colors ${
            loading
              ? "bg-gray-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2"
          }`}
        >
          {loading ? (
            <div className="flex items-center justify-center">
              <svg
                className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
              >
                <circle
                  className="opacity-25"
                  cx="12"
                  cy="12"
                  r="10"
                  stroke="currentColor"
                  strokeWidth="4"
                ></circle>
                <path
                  className="opacity-75"
                  fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
              </svg>
              Checking...
            </div>
          ) : (
            "Log In"
          )}
        </button>

        {/* 帮助信息 */}
        <div className="mt-4 text-xs text-gray-500">
          <p>
            By logging in, you agree to our terms of service and privacy policy.
          </p>
        </div>

        {/* 开发环境调试信息 */}
        {process.env.NODE_ENV === "development" && (
          <div className="mt-4">
            <AuthDebug />
          </div>
        )}
      </div>
    </div>
  );
};
