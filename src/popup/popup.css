/* Popup样式 - 简洁现代设计 */

:root {
  --quote-primary: #574BC8;
  --quote-primary-hover: #4034BA;
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-muted: #9ca3af;
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --border-color: #e5e7eb;
  --border-radius: 8px;
  --spacing-unit: 8px;
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  width: 320px;
  min-height: 200px;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', <PERSON><PERSON>, 'Noto Sans', sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  line-height: 1.5;
}

.popup-container {
  width: 100%;
  padding: calc(var(--spacing-unit) * 3);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

/* 加载状态 */
.loading-view {
  padding: calc(var(--spacing-unit) * 4) 0;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--quote-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto calc(var(--spacing-unit) * 2);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: var(--text-secondary);
  font-size: 14px;
}

/* Logo和品牌 */
.popup-header {
  margin-bottom: calc(var(--spacing-unit) * 3);
}

.popup-logo {
  width: 48px;
  height: 48px;
  margin: 0 auto calc(var(--spacing-unit) * 2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.popup-logo svg {
  width: 32px;
  height: 39px;
  color: var(--quote-primary);
}

.popup-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

/* 未登录状态 */
.unauthenticated-view {
  width: 100%;
}

.auth-status {
  margin: calc(var(--spacing-unit) * 3) 0;
}

.auth-status-text {
  color: var(--text-secondary);
  font-size: 14px;
  margin: 0;
}

.login-button {
  width: 100%;
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
  background: var(--quote-primary);
  color: white;
  border: none;
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: calc(var(--spacing-unit) * 1);
}

.login-button:hover {
  background: var(--quote-primary-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.login-button:active {
  transform: translateY(0);
}

.login-button:disabled {
  background: var(--text-muted);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 已登录状态 */
.authenticated-view {
  width: 100%;
}

.user-info {
  margin: calc(var(--spacing-unit) * 3) 0;
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: var(--bg-secondary);
  border: 2px solid var(--border-color);
  margin: 0 auto calc(var(--spacing-unit) * 2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  font-weight: 600;
  color: var(--quote-primary);
}

.user-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0 0 calc(var(--spacing-unit) / 2);
}

.user-email {
  font-size: 13px;
  color: var(--text-secondary);
  margin: 0 0 calc(var(--spacing-unit) / 2);
}

.user-status {
  font-size: 12px;
  color: var(--quote-primary);
  background: rgba(87, 75, 200, 0.1);
  padding: calc(var(--spacing-unit) / 2) calc(var(--spacing-unit) * 1);
  border-radius: calc(var(--border-radius) / 2);
  display: inline-block;
  margin: 0;
}

.logout-button {
  width: 100%;
  padding: calc(var(--spacing-unit) * 1.5) calc(var(--spacing-unit) * 3);
  background: var(--bg-secondary);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin-top: calc(var(--spacing-unit) * 3);
}

.logout-button:hover {
  background: #ef4444;
  color: white;
  border-color: #ef4444;
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.logout-button:active {
  transform: translateY(0);
}

/* 错误状态 */
.error-view {
  width: 100%;
  padding: calc(var(--spacing-unit) * 2);
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: var(--border-radius);
  margin: calc(var(--spacing-unit) * 2) 0;
}

.error-text {
  color: #dc2626;
  font-size: 13px;
  margin: 0;
}

/* 按钮加载状态 */
.button-loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.button-spinner {
  width: 14px;
  height: 14px;
  border: 1.5px solid transparent;
  border-top: 1.5px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 响应式调整 */
@media (max-width: 320px) {
  .popup-container {
    padding: calc(var(--spacing-unit) * 2);
  }
}

/* 深色模式支持（可选） */
@media (prefers-color-scheme: dark) {
  :root {
    --text-primary: #f9fafb;
    --text-secondary: #d1d5db;
    --text-muted: #9ca3af;
    --bg-primary: #1f2937;
    --bg-secondary: #374151;
    --border-color: #4b5563;
  }
  
  .error-view {
    background: #451a1a;
    border-color: #7f1d1d;
  }
  
  .user-status {
    background: rgba(87, 75, 200, 0.2);
  }
}
