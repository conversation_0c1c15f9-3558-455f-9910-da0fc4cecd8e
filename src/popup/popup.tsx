/**
 * Quote浏览器扩展Popup组件
 * 集成认证功能，提供简洁现代的用户界面
 * 参考SidePanel.tsx的实现方案
 */

import React, { useState, useEffect } from "react";
import { useAuth } from "@quote/auth-client/react";
import { getAuthServiceRedirectUrl } from "../utils/env";
import { getIcon } from "../contentScript/components/icon/icons";
import "./popup.css";

interface PopupState {
  isLoading: boolean;
  isAuthenticated: boolean;
  error?: string;
  isProcessing: boolean;
}

export function Popup() {
  // 使用与SidePanel相同的useAuth hook
  const { isAuthenticated, login, logout, loading } = useAuth();

  const [state, setState] = useState<PopupState>({
    isLoading: true,
    isAuthenticated: false,
    isProcessing: false,
  });

  // 同步useAuth的状态到本地状态
  useEffect(() => {
    // 只有当 useAuth 不在加载状态时才更新本地状态
    if (!loading) {
      setState((prev) => ({
        ...prev,
        isLoading: false,
        isAuthenticated: isAuthenticated,
      }));
    }
  }, [isAuthenticated, loading]); // 移除 user 依赖，因为不再使用用户信息

  /**
   * 处理登录 - 使用与SidePanel相同的登录方法
   */
  const handleLogin = async () => {
    try {
      setState((prev) => ({ ...prev, isProcessing: true, error: undefined }));

      console.log("[Popup] 开始登录流程...");

      // 使用SidePanel相同的登录方法
      const authServiceRedirectUrl = getAuthServiceRedirectUrl();
      const loginUrl = `${authServiceRedirectUrl}/login`;

      console.log("[Popup] 打开登录页面:", loginUrl);

      // 调用useAuth的login方法
      login(loginUrl);

      // 在新标签页打开登录页面
      await chrome.tabs.create({ url: loginUrl });

      // 关闭popup
      window.close();
    } catch (error) {
      console.error("[Popup] 登录失败:", error);
      setState((prev) => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : "登录失败",
      }));
    }
  };

  /**
   * 处理退出登录 - 使用与SidePanel相同的退出登录方法
   */
  const handleLogout = async () => {
    try {
      setState((prev) => ({ ...prev, isProcessing: true, error: undefined }));

      console.log("[Popup] 开始退出登录...");

      // 使用SidePanel相同的logout方法
      await logout();

      console.log("[Popup] 退出登录成功");

      // useAuth会自动更新状态，但我们也手动更新本地状态
      setState({
        isLoading: false,
        isAuthenticated: false,
        isProcessing: false,
      });
    } catch (error) {
      console.error("[Popup] 退出登录失败:", error);
      setState((prev) => ({
        ...prev,
        isProcessing: false,
        error: error instanceof Error ? error.message : "退出登录失败",
      }));
    }
  };

  /**
   * 渲染加载状态
   */
  const renderLoadingView = () => (
    <div className="loading-view">
      <div className="loading-spinner"></div>
      <p className="loading-text">检查登录状态...</p>
    </div>
  );

  /**
   * 渲染未登录状态
   */
  const renderUnauthenticatedView = () => (
    <div className="unauthenticated-view">
      <div className="auth-status">
        <p className="auth-status-text">暂未登录 Quote 账户</p>
      </div>

      <button
        className={`login-button ${state.isProcessing ? "button-loading" : ""}`}
        onClick={handleLogin}
        disabled={state.isProcessing}
      >
        {state.isProcessing ? (
          <>
            <div className="button-spinner"></div>
            正在跳转...
          </>
        ) : (
          "登录 Quote"
        )}
      </button>
    </div>
  );

  /**
   * 渲染已登录状态
   */
  const renderAuthenticatedView = () => {
    return (
      <div className="authenticated-view">
        <div className="auth-status">
          <p className="auth-status-text">已登录 Quote 账户</p>
        </div>

        <button
          className={`logout-button ${state.isProcessing ? "button-loading" : ""}`}
          onClick={handleLogout}
          disabled={state.isProcessing}
        >
          {state.isProcessing ? (
            <>
              <div className="button-spinner"></div>
              正在退出...
            </>
          ) : (
            "退出登录"
          )}
        </button>
      </div>
    );
  };

  /**
   * 渲染错误状态
   */
  const renderErrorView = () => {
    if (!state.error) return null;

    return (
      <div className="error-view">
        <p className="error-text">{state.error}</p>
      </div>
    );
  };

  return (
    <div className="popup-container">
      {/* 头部Logo和标题 */}
      <div className="popup-header">
        <div 
          className="popup-logo" 
          dangerouslySetInnerHTML={{ __html: getIcon("quote") }}
        />
        <h1 className="popup-title">Quote 浏览器扩展</h1>
      </div>

      {/* 错误提示 */}
      {renderErrorView()}

      {/* 主要内容区域 */}
      {state.isLoading
        ? renderLoadingView()
        : state.isAuthenticated
          ? renderAuthenticatedView()
          : renderUnauthenticatedView()}
    </div>
  );
}
