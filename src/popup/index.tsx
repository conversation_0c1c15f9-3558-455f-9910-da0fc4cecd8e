/**
 * Popup入口文件
 * 渲染Quote浏览器扩展的popup界面
 * 包含AuthProvider以支持useAuth hook
 */

import React from "react";
import { createRoot } from "react-dom/client";
import { AuthProvider } from "../components/auth";
import { Popup } from "./popup";

console.log("[Popup] 初始化Quote浏览器扩展Popup");

// 获取根元素
const container = document.getElementById("root");
if (!container) {
  throw new Error("找不到根元素 #root");
}

// 创建React根并渲染应用，包装在AuthProvider中
const root = createRoot(container);
root.render(
  <AuthProvider>
    <Popup />
  </AuthProvider>
);

console.log("[Popup] Popup组件渲染完成");
