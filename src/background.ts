/**
 * 简单的 Background Script 测试
 * 用于验证基本的消息通信功能和登录检测
 */

import { API_ENDPOINTS, DEFAULT_PROJECT_CONFIG } from "./config/api";

console.log("=== Background Script 启动 ===");

// 存储登录标签页信息
const loginTabs = new Map();

// 标签页更新监听器
chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
  // 只处理 URL 变化
  if (!changeInfo.url) return;

  console.log("=== 标签页更新 ===", { tabId, url: changeInfo.url });

  // 检查是否是登录标签页
  const loginTabInfo = loginTabs.get(tabId);
  if (!loginTabInfo) return;

  console.log("=== 检测到登录标签页更新 ===", {
    tabId,
    url: changeInfo.url,
    authDomain: loginTabInfo.authDomain,
  });

  // 检查是否完成登录
  const isLoginPage = changeInfo.url.includes("/login");
  const isAuthDomain = changeInfo.url.includes(loginTabInfo.authDomain);
  const isRootPath =
    changeInfo.url === loginTabInfo.authDomain ||
    changeInfo.url === `${loginTabInfo.authDomain}/`;
  const hasSuccessIndicator =
    /\/(success|dashboard|home|profile|account)/i.test(changeInfo.url);
  const isLoginComplete =
    isAuthDomain && !isLoginPage && (isRootPath || hasSuccessIndicator);

  console.log("=== 登录状态检查 ===", {
    url: changeInfo.url,
    authDomain: loginTabInfo.authDomain,
    isLoginPage,
    isAuthDomain,
    isRootPath,
    hasSuccessIndicator,
    isLoginComplete,
  });

  if (isLoginComplete) {
    console.log("=== 检测到登录完成，广播消息 ===");

    // 1. 通知 SidePanel（保持现有逻辑）
    chrome.runtime
      .sendMessage({
        type: "LOGIN_COMPLETE",
        tabId,
      })
      .then(() => {
        console.log("=== 登录完成消息发送成功 ===");
      })
      .catch((error) => {
        console.log("=== 发送登录完成消息失败 ===", error);
      });

    // 2. 新增：向所有标签页广播登录完成消息
    chrome.tabs.query({}, (tabs) => {
      console.log(`=== 向 ${tabs.length} 个标签页广播认证状态变化 ===`);

      tabs.forEach((tab) => {
        if (tab.id && tab.id !== tabId) {
          // 排除登录标签页本身
          chrome.tabs
            .sendMessage(tab.id, {
              type: "AUTH_STATUS_CHANGED",
              isAuthenticated: true,
              timestamp: Date.now(),
            })
            .then(() => {
              console.log(`=== 成功通知标签页 ${tab.id} 认证状态变化 ===`);
            })
            .catch((error) => {
              // 忽略没有内容脚本的标签页错误，这是正常的
              console.log(`=== 标签页 ${tab.id} 无内容脚本，跳过通知 ===`);
            });
        }
      });
    });

    // 清理登录标签页记录
    loginTabs.delete(tabId);
  }
});

// 标签页关闭监听器
chrome.tabs.onRemoved.addListener((tabId) => {
  const loginTabInfo = loginTabs.get(tabId);
  if (loginTabInfo) {
    console.log("=== 登录标签页关闭 ===", { tabId });
    loginTabs.delete(tabId);

    // 通知 SidePanel
    chrome.runtime
      .sendMessage({
        type: "LOGIN_TAB_CLOSED",
        tabId,
      })
      .catch((error) => {
        console.log("=== 发送标签页关闭消息失败 ===", error);
      });
  }
});

// 处理打开登录标签页请求
async function handleOpenLoginTab(message: any): Promise<{ tabId?: number }> {
  console.log("=== 开始处理登录标签页打开请求 ===", message);

  try {
    const { loginUrl } = message.data;

    if (!loginUrl) {
      throw new Error("缺少登录URL");
    }

    // 使用 Chrome API 打开新标签页
    const tab = await chrome.tabs.create({
      url: loginUrl,
      active: true, // 激活新标签页
    });

    if (!tab.id) {
      throw new Error("无法获取标签页ID");
    }

    // 注册登录标签页
    loginTabs.set(tab.id, {
      tabId: tab.id,
      url: loginUrl,
      timestamp: Date.now(),
    });

    console.log("=== 登录标签页创建成功 ===", {
      tabId: tab.id,
      url: tab.url,
    });

    return { tabId: tab.id };
  } catch (error) {
    console.error("=== 创建登录标签页失败 ===", error);
    throw error;
  }
}

// 处理获取项目列表
async function handleGetProjects(): Promise<any> {
  console.log("=== 开始获取项目列表 ===");

  try {
    console.log("=== 发送到项目列表端点 ===", API_ENDPOINTS.PROJECTS);
    const response = await fetch(API_ENDPOINTS.PROJECTS, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        credentials: "include",
      },
      credentials: "include", // 包含 cookies
    });

    console.log("=== 项目列表API响应状态 ===", {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });

    if (!response.ok) {
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (parseError) {
        console.warn("=== 解析错误响应失败 ===", parseError);
      }
      throw new Error(`获取项目列表失败: ${errorMessage}`);
    }

    const projects = await response.json();
    console.log("=== 项目列表获取成功 ===", projects);

    return {
      success: true,
      projects: projects,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("=== 获取项目列表失败 ===", error);

    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error(
        `网络连接失败，请检查网络连接和后端服务状态: ${error.message}`
      );
    } else if (error instanceof Error) {
      throw new Error(`获取项目列表失败: ${error.message}`);
    } else {
      throw new Error(`获取项目列表失败: ${String(error)}`);
    }
  }
}

// 处理采集到指定项目
async function handleSendExtractedContentToProject(data: any): Promise<any> {
  console.log("=== 开始处理采集到指定项目 ===", {
    site: data?.site,
    contentLength: data?.content?.length,
    targetProjectId: data?.targetProjectId,
    timestamp: data?.timestamp,
  });

  try {
    // 验证数据完整性
    if (!data || !data.site || !data.content || !data.targetProjectId) {
      throw new Error("缺少必要的数据字段");
    }

    return await sendContentToAPI(data, data.targetProjectId);
  } catch (error) {
    console.error("=== 采集到指定项目失败 ===", error);
    throw error;
  }
}

// 处理采集到收件箱
async function handleSendExtractedContentToInbox(data: any): Promise<any> {
  console.log("=== 开始处理采集到收件箱 ===", {
    site: data?.site,
    contentLength: data?.content?.length,
    timestamp: data?.timestamp,
  });

  try {
    // 验证数据完整性
    if (!data || !data.site || !data.content) {
      throw new Error("缺少必要的数据字段");
    }

    // 获取INBOX项目
    const projectsResponse = await handleGetProjects();
    if (!projectsResponse.success) {
      throw new Error("无法获取项目列表");
    }

    const inboxProject = projectsResponse.projects.find(
      (project: any) => project.status === "INBOX"
    );

    if (!inboxProject) {
      throw new Error("未找到收件箱项目");
    }

    return await sendContentToAPI(data, inboxProject.id);
  } catch (error) {
    console.error("=== 采集到收件箱失败 ===", error);
    throw error;
  }
}

// 通用的发送内容到API的函数
async function sendContentToAPI(data: any, projectId: string): Promise<any> {
  console.log("=== 开始发送内容到API ===", {
    site: data?.site,
    contentLength: data?.content?.length,
    projectId: projectId,
    metadataKeys: data?.metadata ? Object.keys(data.metadata) : [],
    timestamp: data?.timestamp,
  });

  try {
    // 构建符合API规范的数据格式
    const fileName =
      data.metadata?.file_name ||
      data.metadata?.title ||
      `${data.site}_document_${Date.now()}`;

    // 确保 file_name 以 "/" 开头
    const formattedFileName = fileName.startsWith("/")
      ? fileName
      : `/${fileName}`;

    const apiPayload = {
      project_id: projectId,
      region: DEFAULT_PROJECT_CONFIG.REGION,
      full_name: formattedFileName,
      content: data.content,
      source_url: data.metadata?.url,
      meta_data: {
        site: data.site,
        caseNumber: data.metadata?.caseNumber,
        caseType: data.metadata?.caseType,
        documentId: data.metadata?.documentId,
        keywords: data.metadata?.keywords,
        source: "browser-extension",
      },
    };

    console.log("=== 准备发送到后端API ===", {
      project_id: apiPayload.project_id,
      region: apiPayload.region,
      full_name: apiPayload.full_name,
      contentLength: apiPayload.content.length,
      source_url: apiPayload.source_url,
      site: apiPayload.meta_data.site,
      hasMetadata: Object.keys(apiPayload.meta_data).length > 0,
    });

    // 发送到文件上传API端点
    console.log("=== 发送到文件上传端点 ===", API_ENDPOINTS.FILE_UPLOAD);
    const response = await fetch(API_ENDPOINTS.FILE_UPLOAD, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // 包含认证信息
        credentials: "include",
      },
      credentials: "include", // 包含 cookies
      body: JSON.stringify(apiPayload),
    });

    console.log("=== 后端API响应状态 ===", {
      status: response.status,
      statusText: response.statusText,
      ok: response.ok,
    });

    if (!response.ok) {
      // HTTP错误状态
      let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
      try {
        const errorData = await response.json();
        errorMessage = errorData.message || errorData.error || errorMessage;
      } catch (parseError) {
        console.warn("=== 解析错误响应失败 ===", parseError);
      }
      throw new Error(`后端API调用失败: ${errorMessage}`);
    }

    // 解析成功响应
    const responseData = await response.json();
    console.log("=== 后端API响应数据 ===", responseData);

    return {
      success: true,
      message: "内容发送成功",
      contentLength: apiPayload.content.length,
      timestamp: new Date().toISOString(),
      backendResponse: responseData,
    };
  } catch (error) {
    console.error("=== 处理内容发送失败 ===", error);

    // 提供更详细的错误信息
    if (error instanceof TypeError && error.message.includes("fetch")) {
      throw new Error(
        `网络连接失败，请检查网络连接和后端服务状态: ${error.message}`
      );
    } else if (error instanceof Error) {
      throw new Error(`内容发送失败: ${error.message}`);
    } else {
      throw new Error(`内容发送失败: ${String(error)}`);
    }
  }
}

// 简单的消息处理器
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("=== 收到消息 ===", message);

  switch (message.type) {
    case "TEST_MESSAGE":
      console.log("处理测试消息:", message.data);
      sendResponse({
        success: true,
        message: "简单 Background Script 正常工作",
        receivedData: message.data,
        timestamp: new Date().toISOString(),
      });
      return true;

    case "REGISTER_LOGIN_TAB":
      console.log("注册登录标签页:", message.tabId, message.authDomain);
      loginTabs.set(message.tabId, {
        tabId: message.tabId,
        authDomain: message.authDomain,
      });
      sendResponse({ success: true });
      return true;

    case "UNREGISTER_LOGIN_TAB":
      console.log("取消注册登录标签页:", message.tabId);
      loginTabs.delete(message.tabId);
      sendResponse({ success: true });
      return true;

    // 检查认证状态
    case "CHECK_AUTH_STATUS":
      console.log("=== 收到认证状态检查请求 ===");
      handleCheckAuthStatus()
        .then((result) => {
          console.log("=== 认证状态检查完成 ===", result);
          sendResponse({ success: true, ...result });
        })
        .catch((error) => {
          console.error("=== 认证状态检查失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    // 打开侧边栏
    case "OPEN_SIDE_PANEL":
      console.log("=== 收到打开侧边栏请求 ===");
      handleOpenSidePanel(sender)
        .then(() => {
          console.log("=== 侧边栏打开完成 ===");
          sendResponse({ success: true });
        })
        .catch((error) => {
          console.error("=== 打开侧边栏失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    case "OPEN_LOGIN_TAB":
      console.log("=== 收到打开登录标签页请求 ===", message);
      handleOpenLoginTab(message)
        .then((result) => {
          console.log("=== 登录标签页打开完成 ===", result);
          sendResponse({ success: true, ...result });
        })
        .catch((error) => {
          console.error("=== 打开登录标签页失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    // 获取项目列表
    case "GET_PROJECTS":
      console.log("=== 收到获取项目列表请求 ===");
      handleGetProjects()
        .then((result) => {
          console.log("=== 项目列表获取完成 ===", result);
          sendResponse({ success: true, ...result });
        })
        .catch((error) => {
          console.error("=== 项目列表获取失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    // 处理采集到指定项目
    case "SEND_EXTRACTED_CONTENT_TO_PROJECT":
      console.log("=== 收到采集到指定项目请求 ===", {
        site: message.data?.site,
        contentLength: message.data?.content?.length,
        targetProjectId: message.data?.targetProjectId,
        timestamp: message.data?.timestamp,
      });
      handleSendExtractedContentToProject(message.data)
        .then((result) => {
          console.log("=== 采集到指定项目完成 ===", result);
          sendResponse({ success: true, result });
        })
        .catch((error) => {
          console.error("=== 采集到指定项目失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    // 处理采集到收件箱
    case "SEND_EXTRACTED_CONTENT_TO_INBOX":
      console.log("=== 收到采集到收件箱请求 ===", {
        site: message.data?.site,
        contentLength: message.data?.content?.length,
        timestamp: message.data?.timestamp,
      });
      handleSendExtractedContentToInbox(message.data)
        .then((result) => {
          console.log("=== 采集到收件箱完成 ===", result);
          sendResponse({ success: true, result });
        })
        .catch((error) => {
          console.error("=== 采集到收件箱失败 ===", error);
          sendResponse({ success: false, error: String(error) });
        });
      return true;

    default:
      console.log("未知消息类型:", message.type);
      sendResponse({ success: false, error: "未知消息类型" });
      return false;
  }
});

// 检查认证状态
async function handleCheckAuthStatus(): Promise<{
  isAuthenticated: boolean;
  error?: string;
}> {
  try {
    // 直接调用认证 API（因为 Background Script 无法使用认证客户端）
    const response = await fetch(API_ENDPOINTS.AUTH_VERIFY, {
      method: "GET",
      credentials: "include", // 包含 cookies
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      const data = await response.json();

      const result = {
        isAuthenticated: data.success === true,
        error: data.success ? undefined : data.message || "认证失败",
      };

      return result;
    } else {
      // HTTP 错误状态
      const result = {
        isAuthenticated: false,
        error: `认证检查失败: ${response.status} ${response.statusText}`,
      };

      return result;
    }
  } catch (error) {
    console.error("=== 认证检查失败 ===", error);
    return {
      isAuthenticated: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}

// 打开侧边栏
async function handleOpenSidePanel(
  sender: chrome.runtime.MessageSender
): Promise<void> {
  try {
    if (!sender.tab?.windowId) {
      throw new Error("无法获取窗口ID");
    }

    // 尝试打开侧边栏
    try {
      // 打开侧边栏
      await (chrome as any).sidePanel.open({ windowId: sender.tab.windowId });
    } catch (apiError) {
      // 如果失败，用户可以手动点击插件图标打开侧边栏
      console.log("侧边栏打开失败，请手动点击插件图标", apiError);
    }
  } catch (error) {
    console.error("打开侧边栏失败", error);
    throw error;
  }
}

// 注意：Service Worker 环境不支持 XMLHttpRequest，已移除

console.log("=== Background Script 启动完成 ===");
