/**
 * 文本处理工具类
 * 提供中英文文本的视觉宽度计算和智能截取功能
 */

export class TextUtils {
  /**
   * 判断字符是否为中文字符（包括中文标点符号）
   */
  static isChineseChar(char: string): boolean {
    const code = char.charCodeAt(0);
    return (
      (code >= 0x4e00 && code <= 0x9fff) || // CJK统一汉字
      (code >= 0x3000 && code <= 0x303f) || // CJK标点符号
      (code >= 0xff00 && code <= 0xffef)    // 全角ASCII、全角标点
    );
  }

  /**
   * 计算字符串的视觉宽度
   * @param str 要计算的字符串
   * @param chineseWidthRatio 中文字符的宽度比例，相对于英文字符，默认1.8
   */
  static getVisualWidth(str: string, chineseWidthRatio: number = 1.8): number {
    let width = 0;
    for (let i = 0; i < str.length; i++) {
      width += this.isChineseChar(str[i]) ? chineseWidthRatio : 1;
    }
    return width;
  }

  /**
   * 截取指定视觉宽度的字符串
   * @param str 原始字符串
   * @param maxWidth 最大视觉宽度
   * @param fromStart 是否从开头截取（false则从结尾截取）
   * @param chineseWidthRatio 中文字符的宽度比例
   */
  static truncateToVisualWidth(
    str: string,
    maxWidth: number,
    fromStart: boolean = true,
    chineseWidthRatio: number = 1.8
  ): string {
    let width = 0;
    let result = "";

    if (fromStart) {
      // 从开头截取
      for (let i = 0; i < str.length; i++) {
        const charWidth = this.isChineseChar(str[i]) ? chineseWidthRatio : 1;
        if (width + charWidth <= maxWidth) {
          width += charWidth;
          result += str[i];
        } else {
          break;
        }
      }
      return result;
    } else {
      // 从结尾截取
      for (let i = str.length - 1; i >= 0; i--) {
        const charWidth = this.isChineseChar(str[i]) ? chineseWidthRatio : 1;
        if (width + charWidth <= maxWidth) {
          width += charWidth;
          result = str[i] + result;
        } else {
          break;
        }
      }
      return result;
    }
  }

  /**
   * 应用中间省略逻辑
   * @param text 需要显示的文本
   * @param maxLength 文本的最大视觉长度，超过则显示省略号
   * @param startChars 开头保留的视觉宽度
   * @param endChars 结尾保留的视觉宽度
   * @param chineseWidthRatio 中文字符的宽度比例
   */
  static applyMiddleEllipsis(
    text: string,
    maxLength: number = 20,
    startChars: number = 12,
    endChars: number = 5,
    chineseWidthRatio: number = 1.8
  ): string {
    // 如果文本为空，直接返回空字符串
    if (!text) {
      return '';
    }

    // 计算文本的视觉宽度
    const textVisualWidth = this.getVisualWidth(text, chineseWidthRatio);

    // 如果文本视觉宽度小于最大长度，直接显示
    if (textVisualWidth <= maxLength) {
      return text;
    }

    // 计算实际可用的视觉宽度（考虑省略号占用的空间）
    const ellipsisLength = 3; // 省略号的长度
    const availableWidth = Math.max(maxLength - ellipsisLength, 2); // 确保至少有2个单位宽度可用

    // 如果 startChars + endChars 超过了可用宽度，按比例分配
    let adjustedStartWidth = startChars;
    let adjustedEndWidth = endChars;

    if (startChars + endChars > availableWidth) {
      // 按照原始比例分配可用宽度
      const ratio = startChars / (startChars + endChars);
      adjustedStartWidth = Math.max(Math.floor(availableWidth * ratio), 1);
      adjustedEndWidth = Math.max(availableWidth - adjustedStartWidth, 1);
    }

    // 计算中间省略部分
    const start = this.truncateToVisualWidth(
      text,
      adjustedStartWidth,
      true,
      chineseWidthRatio
    );
    const end = this.truncateToVisualWidth(
      text,
      adjustedEndWidth,
      false,
      chineseWidthRatio
    );

    return `${start}...${end}`;
  }
}
