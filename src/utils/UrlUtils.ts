/**
 * URL处理工具类
 * 专门处理超长URL的截断和清理
 */

export class UrlUtils {
  /**
   * 只对超长URL进行处理，保持URL的原始性
   * @param url 原始URL
   * @param maxLength 最大长度限制，默认255
   * @returns 处理后的URL
   */
  static cleanAndTruncateUrl(url: string, maxLength: number = 255): string {
    // 🔥 只有超长URL才进行任何处理
    if (url.length <= maxLength) {
      return url; // 短URL直接返回，保持原始性
    }

    console.log(`[UrlUtils] 检测到超长URL，长度: ${url.length}，开始处理`);

    try {
      const urlObj = new URL(url);
      
      // 根据不同网站采用不同的截断策略
      return this.truncateByDomain(urlObj, maxLength);
      
    } catch (error) {
      console.warn(`[UrlUtils] URL解析失败，使用降级截断:`, error);
      // 降级处理：直接截断
      return url.substring(0, maxLength - 3) + '...';
    }
  }

  /**
   * 根据域名采用不同的截断策略
   */
  private static truncateByDomain(urlObj: URL, maxLength: number): string {
    const domain = urlObj.hostname.toLowerCase();
    
    // 威科先行相关域名
    if (domain.includes('wkinf') || domain.includes('wkinfo')) {
      return this.truncateWkinfo(urlObj, maxLength);
    }
    
    // 北大法宝
    if (domain.includes('pkulaw')) {
      return this.truncatePkulaw(urlObj, maxLength);
    }
    
    // 中国裁判文书网
    if (domain.includes('wenshu')) {
      return this.truncateWenshu(urlObj, maxLength);
    }
    
    // 其他网站使用通用策略
    return this.truncateGeneric(urlObj, maxLength);
  }

  /**
   * 威科先行URL截断策略
   * 保留到 /judgment-documents/detail/文档ID 的程度
   */
  private static truncateWkinfo(urlObj: URL, maxLength: number): string {
    const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
    
    // 提取路径中的关键部分：/judgment-documents/detail/文档ID
    const pathMatch = urlObj.pathname.match(/^(\/judgment-documents\/detail\/[^\/]+)/);
    
    if (pathMatch) {
      const coreUrl = `${baseUrl}${pathMatch[1]}`;
      
      // 检查核心URL是否仍然超长
      if (coreUrl.length <= maxLength) {
        console.log(`[UrlUtils] 威科先行URL截断完成: ${coreUrl}`);
        return coreUrl;
      } else {
        // 如果核心URL仍然超长，截断文档ID部分
        const availableLength = maxLength - baseUrl.length - '/judgment-documents/detail/'.length - 3;
        const truncatedId = pathMatch[1].split('/').pop()?.substring(0, availableLength) || '';
        const result = `${baseUrl}/judgment-documents/detail/${truncatedId}...`;
        console.log(`[UrlUtils] 威科先行URL进一步截断: ${result}`);
        return result;
      }
    }
    
    // 如果路径不匹配预期格式，使用通用策略
    return this.truncateGeneric(urlObj, maxLength);
  }

  /**
   * 北大法宝URL截断策略
   */
  private static truncatePkulaw(urlObj: URL, maxLength: number): string {
    const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
    
    // 移除查询参数，保留路径
    const coreUrl = `${baseUrl}${urlObj.pathname}`;
    
    if (coreUrl.length <= maxLength) {
      return coreUrl;
    }
    
    // 如果路径也太长，截断路径
    const availableLength = maxLength - baseUrl.length - 3;
    const truncatedPath = urlObj.pathname.substring(0, availableLength);
    return `${baseUrl}${truncatedPath}...`;
  }

  /**
   * 中国裁判文书网URL截断策略
   */
  private static truncateWenshu(urlObj: URL, maxLength: number): string {
    const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
    
    // 移除查询参数，保留路径
    const coreUrl = `${baseUrl}${urlObj.pathname}`;
    
    if (coreUrl.length <= maxLength) {
      return coreUrl;
    }
    
    // 截断路径
    const availableLength = maxLength - baseUrl.length - 3;
    const truncatedPath = urlObj.pathname.substring(0, availableLength);
    return `${baseUrl}${truncatedPath}...`;
  }

  /**
   * 通用URL截断策略
   */
  private static truncateGeneric(urlObj: URL, maxLength: number): string {
    const baseUrl = `${urlObj.protocol}//${urlObj.host}`;
    
    // 首先尝试移除查询参数
    const coreUrl = `${baseUrl}${urlObj.pathname}`;
    
    if (coreUrl.length <= maxLength) {
      return coreUrl;
    }
    
    // 如果还是太长，截断路径
    const availableLength = maxLength - baseUrl.length - 3;
    const truncatedPath = urlObj.pathname.substring(0, availableLength);
    return `${baseUrl}${truncatedPath}...`;
  }

  /**
   * 获取URL处理的统计信息
   */
  static getUrlProcessingInfo(originalUrl: string, processedUrl: string): {
    originalLength: number;
    processedLength: number;
    wasProcessed: boolean;
    lengthReduction: number;
    processingType: string;
  } {
    const originalLength = originalUrl.length;
    const processedLength = processedUrl.length;
    const wasProcessed = originalUrl !== processedUrl;
    
    let processingType = 'none';
    if (wasProcessed) {
      if (originalLength > 255) {
        processingType = 'truncated_oversize';
      } else {
        processingType = 'cleaned';
      }
    }
    
    return {
      originalLength,
      processedLength,
      wasProcessed,
      lengthReduction: originalLength - processedLength,
      processingType
    };
  }
}
