/**
 * HTML到Markdown转换器
 * 将HTML结构转换为格式化的Markdown文本，保留重要的文档结构
 */

export class MarkdownConverter {
  /**
   * 将HTML元素转换为Markdown格式
   * @param element HTML元素
   * @param options 转换选项
   */
  static htmlToMarkdown(
    element: Element,
    options: MarkdownOptions = {}
  ): string {
    const defaultOptions: MarkdownOptions = {
      preserveHeadings: true,
      preserveParagraphs: true,
      preserveLists: true,
      preserveLinks: false,
      preserveBold: true,
      preserveItalic: true,
      paragraphSeparator: "\n\n",
      listIndent: "- ",
      ...options,
    };

    return this.processElement(element, defaultOptions).trim();
  }

  /**
   * 递归处理HTML元素
   */
  private static processElement(
    element: Element,
    options: MarkdownOptions
  ): string {
    let result = "";

    // 处理不同类型的元素
    const tagName = element.tagName.toLowerCase();

    switch (tagName) {
      case "h1":
      case "h2":
      case "h3":
      case "h4":
      case "h5":
      case "h6":
        if (options.preserveHeadings) {
          const level = parseInt(tagName.charAt(1));
          const text = this.getTextContent(element);
          if (text.trim()) {
            result += "#".repeat(level) + " " + text.trim() + "\n\n";
          }
        } else {
          result += this.getTextContent(element) + "\n\n";
        }
        break;

      case "p":
        if (options.preserveParagraphs) {
          const text = this.processChildren(element, options);
          if (text.trim()) {
            result += text.trim() + options.paragraphSeparator;
          }
        } else {
          result += this.processChildren(element, options);
        }
        break;

      case "div":
        // 检查是否是PDF_title（中国裁判文书网主标题）
        if (
          element.classList.contains("PDF_title") &&
          options.preserveHeadings
        ) {
          const text = this.getTextContent(element);
          if (text.trim()) {
            result += "# " + text.trim() + "\n\n"; // 主标题使用h1
          }
        }
        // 检查是否是机构名称或文书类型标题
        else if (this.isInstitutionTitle(element) && options.preserveHeadings) {
          const text = this.getTextContent(element);
          if (text.trim()) {
            result += "## " + text.trim() + "\n\n"; // 机构名称使用h2
          }
        }
        // 🔥 所有其他div都进行分段处理
        else {
          const text = this.processChildren(element, options);
          if (text.trim()) {
            result += text.trim() + options.paragraphSeparator;
          }
        }
        break;

      case "ul":
      case "ol":
        if (options.preserveLists) {
          result += this.processList(element, options, tagName === "ol");
        } else {
          result += this.processChildren(element, options);
        }
        break;

      case "li":
        // 在processList中处理，这里直接返回内容
        result += this.processChildren(element, options);
        break;

      case "strong":
      case "b":
        if (options.preserveBold) {
          const text = this.processChildren(element, options);
          if (text.trim()) {
            result += `**${text.trim()}**`;
          }
        } else {
          result += this.processChildren(element, options);
        }
        break;

      case "em":
      case "i":
        if (options.preserveItalic) {
          const text = this.processChildren(element, options);
          if (text.trim()) {
            result += `*${text.trim()}*`;
          }
        } else {
          result += this.processChildren(element, options);
        }
        break;

      case "a":
        if (options.preserveLinks) {
          const text = this.processChildren(element, options);
          const href = element.getAttribute("href");
          if (text.trim() && href) {
            result += `[${text.trim()}](${href})`;
          } else {
            result += text;
          }
        } else {
          result += this.processChildren(element, options);
        }
        break;

      case "br":
        result += "\n";
        break;

      case "hr":
        result += "\n---\n\n";
        break;

      // 忽略的标签
      case "script":
      case "style":
      case "noscript":
        break;

      default:
        // 其他标签直接处理子元素
        result += this.processChildren(element, options);
        break;
    }

    return result;
  }

  /**
   * 处理元素的子节点
   */
  private static processChildren(
    element: Element,
    options: MarkdownOptions
  ): string {
    let result = "";

    for (let i = 0; i < element.childNodes.length; i++) {
      const child = element.childNodes[i];
      if (child.nodeType === Node.TEXT_NODE) {
        // 文本节点
        const text = child.textContent || "";
        result += this.cleanText(text);
      } else if (child.nodeType === Node.ELEMENT_NODE) {
        // 元素节点
        result += this.processElement(child as Element, options);
      }
    }

    return result;
  }

  /**
   * 处理列表
   */
  private static processList(
    element: Element,
    options: MarkdownOptions,
    isOrdered: boolean = false
  ): string {
    let result = "";
    const listItems = element.querySelectorAll(":scope > li");

    listItems.forEach((li, index) => {
      const text = this.processChildren(li, options).trim();
      if (text) {
        if (isOrdered) {
          result += `${index + 1}. ${text}\n`;
        } else {
          result += `${options.listIndent}${text}\n`;
        }
      }
    });

    return result + "\n";
  }

  /**
   * 判断div是否是机构名称或文书类型标题
   */
  private static isInstitutionTitle(element: Element): boolean {
    const style = element.getAttribute("style") || "";
    const text = element.textContent?.trim() || "";

    // 检查是否包含居中样式
    const isCentered = style.includes("TEXT-ALIGN: center");

    // 检查是否包含机构或文书类型关键词
    const isInstitution =
      text.includes("人民法院") ||
      text.includes("通知书") ||
      text.includes("判决书") ||
      text.includes("裁定书") ||
      text.includes("决定书");

    // 检查是否是标题样式（黑体、大字号）
    const isTitleStyle =
      style.includes("FONT-FAMILY: 黑体") || style.includes("FONT-SIZE: 18pt");

    // 标题通常较短
    const isShort = text.length < 50;

    return (isCentered || isTitleStyle) && isInstitution && isShort;
  }

  /**
   * 获取元素的纯文本内容
   */
  private static getTextContent(element: Element): string {
    return element.textContent || "";
  }

  /**
   * 清理文本内容
   */
  private static cleanText(text: string): string {
    return (
      text
        // 保留单个换行，但清理多余的空白
        .replace(/[ \t]+/g, " ")
        // 清理行首行尾空白，但保留换行
        .replace(/^[ \t]+|[ \t]+$/gm, "")
    );
  }

  /**
   * 智能段落检测和分割
   */
  static detectParagraphs(element: Element): string {
    // 首先尝试查找明确的段落元素
    const paragraphElements = element.querySelectorAll(
      "p, div.paragraph, .content-paragraph, .text-content"
    );

    if (paragraphElements.length > 0) {
      const paragraphs: string[] = [];
      paragraphElements.forEach((p) => {
        const text = p.textContent?.trim();
        if (text && text.length > 1) {
          // 降低过滤阈值
          paragraphs.push(text);
        }
      });
      if (paragraphs.length > 0) {
        return paragraphs.join("\n\n");
      }
    }

    // 如果没有明确的段落标签，通过文本分析分割段落
    const text = element.textContent || "";
    const paragraphs = text
      .split(/\n\s*\n/) // 按双换行分割
      .map((p) => p.trim())
      .filter((p) => p.length > 1); // 降低过滤阈值

    return paragraphs.join("\n\n");
  }

  /**
   * 提取并格式化标题
   */
  static extractHeadings(element: Element): string {
    const headings = element.querySelectorAll("h1, h2, h3, h4, h5, h6");
    let result = "";

    headings.forEach((heading) => {
      const level = parseInt(heading.tagName.charAt(1));
      const text = heading.textContent?.trim();
      if (text) {
        result += "#".repeat(level) + " " + text + "\n\n";
      }
    });

    return result;
  }

  /**
   * 后处理Markdown内容，清理常见问题
   */
  static postProcessMarkdown(content: string): string {
    let processed = content;

    // 1. 移除完全重复的主标题
    processed = this.removeDuplicateMainTitles(processed);

    // 2. 移除重复的法院信息
    processed = this.removeRedundantCourtInfo(processed);

    // 3. 清理多余空行（保留最多2个连续换行）- 最后执行
    processed = this.cleanExcessiveLineBreaks(processed);

    return processed.trim();
  }

  /**
   * 清理多余空行
   */
  private static cleanExcessiveLineBreaks(content: string): string {
    // 将3个或更多连续换行替换为2个换行
    // 使用全局替换，直到没有更多的替换为止
    return content.replace(/\n{3,}/g, "\n\n");
  }

  /**
   * 移除完全重复的主标题
   */
  private static removeDuplicateMainTitles(content: string): string {
    const lines = content.split("\n");
    const processedLines: string[] = [];
    const seenTitles = new Set<string>();

    for (const line of lines) {
      // 检查是否是主标题（以 # 或 ## 开头）
      const titleMatch = line.match(/^(#{1,2})\s+(.+)$/);

      if (titleMatch) {
        const titleContent = titleMatch[2].trim();

        // 如果是完全重复的标题，跳过
        if (seenTitles.has(titleContent)) {
          continue;
        }

        seenTitles.add(titleContent);
      }

      processedLines.push(line);
    }

    return processedLines.join("\n");
  }

  /**
   * 移除重复的法院信息
   */
  private static removeRedundantCourtInfo(content: string): string {
    // 查找并移除类似 "#### 黑龙江省****(2025)黑****清申96号2025.07**** 裁判" 的行
    // 这种格式通常是重复信息
    const courtInfoPattern =
      /^####\s+[^#\n]*\(\d{4}\)[^#\n]*\d{4}\.[^#\n]*裁判[^#\n]*$/gm;

    return content.replace(courtInfoPattern, "");
  }
}

/**
 * Markdown转换选项接口
 */
export interface MarkdownOptions {
  /** 是否保留标题格式 */
  preserveHeadings?: boolean;
  /** 是否保留段落格式 */
  preserveParagraphs?: boolean;
  /** 是否保留列表格式 */
  preserveLists?: boolean;
  /** 是否保留链接格式 */
  preserveLinks?: boolean;
  /** 是否保留粗体格式 */
  preserveBold?: boolean;
  /** 是否保留斜体格式 */
  preserveItalic?: boolean;
  /** 段落分隔符 */
  paragraphSeparator?: string;
  /** 列表项前缀 */
  listIndent?: string;
}
