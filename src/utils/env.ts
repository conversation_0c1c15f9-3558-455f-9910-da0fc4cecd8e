/**
 * 环境变量工具函数
 * 简化版本，基于 config/api.ts
 */

/**
 * 获取认证服务 API URL
 */
export function getAuthServiceApiUrl(): string {
  return process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL || "";
}

/**
 * 获取认证服务重定向 URL
 */
export function getAuthServiceRedirectUrl(): string {
  return process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL || "";
}

/**
 * 获取认证域名
 */
export function getAuthDomain(): string {
  return process.env.NEXT_PUBLIC_AUTH_DOMAIN || "";
}

/**
 * 获取文件服务 URL
 */
export function getFileServiceApiUrl(): string {
  return process.env.API_BASE_URL || "";
}

/**
 * 获取默认项目 ID
 */
export function getDefaultProjectId(): string {
  const projectId = process.env.NEXT_PUBLIC_DEFAULT_PROJECT_ID;
  if (!projectId) {
    throw new Error("环境变量 NEXT_PUBLIC_DEFAULT_PROJECT_ID 未设置或为空");
  }
  return projectId;
}

/**
 * 验证认证环境变量是否完整
 */
export function validateAuthEnv(): boolean {
  try {
    const authServiceApiUrl = getAuthServiceApiUrl();
    const authServiceRedirectUrl = getAuthServiceRedirectUrl();
    const authDomain = getAuthDomain();

    return Boolean(authServiceApiUrl && authServiceRedirectUrl && authDomain);
  } catch (error) {
    console.error("认证环境变量验证失败:", error);
    return false;
  }
}

/**
 * 验证文件服务环境变量是否完整
 */
export function validateFileServiceEnv(): boolean {
  try {
    const fileServiceApiUrl = getFileServiceApiUrl();
    const defaultProjectId = getDefaultProjectId();

    return Boolean(fileServiceApiUrl && defaultProjectId);
  } catch (error) {
    console.error("文件服务环境变量验证失败:", error);
    return false;
  }
}

/**
 * 验证所有环境变量是否完整
 */
export function validateAllEnv(): boolean {
  return validateAuthEnv() && validateFileServiceEnv();
}
