// API 配置文件
// 支持环境变量和默认值

interface ApiConfig {
  authServiceUrl: string;
  authServiceRedirectUrl: string;
  fileServiceUrl: string;
  projectId: string;
}

// 从环境变量获取配置，永远不使用默认值
// 注意：必须使用直接的 process.env.VARIABLE_NAME 引用，这样 Webpack DefinePlugin 才能正确替换
function getRequiredEnvVar(value: string | undefined, name: string): string {
  if (!value) {
    throw new Error(`环境变量 ${name} 未设置或为空`);
  }
  return value;
}

const getApiConfig = (): ApiConfig => {
  // 在浏览器扩展中，环境变量需要在构建时注入
  // 这些变量会在 webpack 构建时被 DefinePlugin 替换为字面量
  // 必须使用直接引用，不能使用 process.env[variableName] 的动态方式
  const authServiceUrl = getRequiredEnvVar(
    process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL,
    "NEXT_PUBLIC_AUTH_SERVICE_API_URL"
  );
  const authServiceRedirectUrl = getRequiredEnvVar(
    process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL,
    "NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL"
  );
  const fileServiceUrl = getRequiredEnvVar(
    process.env.API_BASE_URL,
    "API_BASE_URL"
  );
  const projectId = getRequiredEnvVar(
    process.env.NEXT_PUBLIC_DEFAULT_PROJECT_ID,
    "NEXT_PUBLIC_DEFAULT_PROJECT_ID"
  );

  return {
    authServiceUrl,
    authServiceRedirectUrl,
    fileServiceUrl,
    projectId,
  };
};

// 导出配置实例
export const apiConfig = getApiConfig();

// 导出具体的 API 端点
export const API_ENDPOINTS = {
  // 认证相关
  AUTH_VERIFY: `${apiConfig.authServiceUrl}/v1/auth/verify_status`,

  // 文件相关
  FILE_UPLOAD: `${apiConfig.fileServiceUrl}/v1/files/`,

  // 项目相关
  PROJECTS: `${apiConfig.fileServiceUrl}/v1/projects`,

  // 项目ID
  PROJECT_ID: apiConfig.projectId,
} as const;

/**
 * 默认项目配置
 */
export const DEFAULT_PROJECT_CONFIG = {
  PROJECT_ID: apiConfig.projectId, // 使用配置的项目ID，不提供默认值
  REGION: "REFERENCE", // 默认区域
};

// 开发环境下打印配置信息
if (process.env.NODE_ENV === "development") {
  console.log("=== API 配置 ===", {
    authServiceUrl: apiConfig.authServiceUrl,
    authServiceRedirectUrl: apiConfig.authServiceRedirectUrl,
    fileServiceUrl: apiConfig.fileServiceUrl,
    projectId: apiConfig.projectId,
    endpoints: API_ENDPOINTS,
    defaultProjectConfig: DEFAULT_PROJECT_CONFIG,
  });
}

// 导出类型
export type { ApiConfig };
