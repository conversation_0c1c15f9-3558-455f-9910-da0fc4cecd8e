/**
 * Content Script - 简化版本
 * 整合核心功能：站点检测、按钮注入、消息处理
 */

import {
  isWkinfoPage,
  getWkinfoSiteInfo,
  injectWkinfoQuoteButton,
} from "./contentScript/sites/wkinfo";
import { initHighlightSystem } from "./contentScript/highlight";
import {
  isPkulawPage,
  getPkulawSiteInfo,
  injectPkulawQuoteButton,
} from "./contentScript/sites/pkulaw";
import {
  isWenshuPage,
  getWenshuSiteInfo,
  injectWenshuQuoteButton,
} from "./contentScript/sites/wenshu";

console.log("[Content Script] 加载完成", {
  url: window.location.href,
  timestamp: new Date().toISOString(),
});

/**
 * 检查扩展上下文是否有效
 */
function isExtensionContextValid(): boolean {
  try {
    return !!(chrome && chrome.runtime && chrome.runtime.id);
  } catch (error) {
    return false;
  }
}

/**
 * 安全发送消息（带上下文检查）
 */
function safeSendMessage(message: any): Promise<any> {
  return new Promise((resolve, reject) => {
    if (!isExtensionContextValid()) {
      console.log("[Content Script] 扩展上下文无效，跳过消息发送");
      reject(new Error("Extension context invalidated"));
      return;
    }

    try {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          console.log(
            "[Content Script] 消息发送错误:",
            chrome.runtime.lastError
          );
          reject(chrome.runtime.lastError);
        } else {
          resolve(response);
        }
      });
    } catch (error) {
      console.log("[Content Script] 发送消息异常:", error);
      reject(error);
    }
  });
}

/**
 * 主要功能初始化
 */
function initialize(): void {
  console.log("[Content Script] 开始初始化，当前URL:", window.location.href);

  // 检测威科先行站点
  if (isWkinfoPage()) {
    console.log("[Content Script] 检测到威科先行站点");
    initializeWkinfoSite();
  }
  // 检测北大法宝站点
  else if (isPkulawPage()) {
    console.log("[Content Script] 检测到北大法宝站点");
    initializePkulawSite();
  }
  // 检测中国裁判文书网站点
  else if (isWenshuPage()) {
    console.log("[Content Script] 检测到中国裁判文书网站点");
    initializeWenshuSite();
  } else {
    console.log("[Content Script] 当前页面不是支持的法律数据库站点");
  }

  // 初始化高亮采集系统（在所有非法律数据库站点）
  initHighlightSystem();
}

/**
 * 初始化威科先行站点功能
 */
function initializeWkinfoSite(): void {
  console.log("[Content Script] 初始化威科先行站点");

  try {
    // 注入Quote按钮
    injectWkinfoQuoteButton();

    console.log("[Content Script] 威科先行站点信息:", getWkinfoSiteInfo());
  } catch (error) {
    console.error("[Content Script] 威科先行初始化失败:", error);
  }
}

/**
 * 初始化北大法宝站点功能
 */
function initializePkulawSite(): void {
  const siteInfo = getPkulawSiteInfo();
  console.log("[Content Script] 北大法宝站点信息:", siteInfo);

  // 尝试多次注入按钮，适应不同的页面加载时机
  let attempts = 0;
  const maxAttempts = 5;

  function tryInjectButton() {
    attempts++;
    console.log(`[Content Script] 北大法宝尝试注入按钮 (第${attempts}次)`);

    const success = injectPkulawQuoteButton();
    if (success) {
      console.log('[Content Script] 北大法宝成功注入"采集到Quote"按钮');
      return;
    }

    if (attempts < maxAttempts) {
      console.log(
        `[Content Script] 北大法宝注入失败，将在${2000 * attempts}ms后重试`
      );
      setTimeout(tryInjectButton, 2000 * attempts);
    } else {
      console.log("[Content Script] 北大法宝达到最大重试次数，注入按钮失败");
    }
  }

  // 立即尝试一次
  tryInjectButton();

  // 发送站点信息给侧边栏
  safeSendMessage({
    type: "SITE_INFO",
    siteInfo: siteInfo,
  }).catch((error) => {
    console.log("[Content Script] 北大法宝发送站点信息失败:", error);
  });

  // 北大法宝不需要定期检查下载按钮状态
  console.log("[Content Script] 北大法宝初始化完成，无需下载按钮检查");
}

/**
 * 初始化中国裁判文书网站点功能
 */
function initializeWenshuSite(): void {
  const siteInfo = getWenshuSiteInfo();
  console.log("[Content Script] 中国裁判文书网站点信息:", siteInfo);

  // 尝试多次注入按钮，适应不同的页面加载时机
  let attempts = 0;
  const maxAttempts = 5;

  function tryInjectButton() {
    attempts++;
    console.log(
      `[Content Script] 中国裁判文书网尝试注入按钮 (第${attempts}次)`
    );

    const success = injectWenshuQuoteButton();
    if (success) {
      console.log('[Content Script] 中国裁判文书网成功注入"采集到Quote"按钮');
      return;
    }

    if (attempts < maxAttempts) {
      console.log(
        `[Content Script] 中国裁判文书网注入失败，将在${2000 * attempts}ms后重试`
      );
      setTimeout(tryInjectButton, 2000 * attempts);
    } else {
      console.log(
        "[Content Script] 中国裁判文书网达到最大重试次数，注入按钮失败"
      );
    }
  }

  // 立即尝试一次
  tryInjectButton();

  // 发送站点信息给侧边栏
  safeSendMessage({
    type: "SITE_INFO",
    siteInfo: siteInfo,
  }).catch((error) => {
    console.log("[Content Script] 中国裁判文书网发送站点信息失败:", error);
  });

  // 中国裁判文书网不需要定期检查下载按钮状态
  console.log("[Content Script] 中国裁判文书网初始化完成，无需下载按钮检查");
}

/**
 * 消息处理器
 */
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log("[Content Script] 收到消息:", message);

  switch (message.type) {
    case "PING":
      // 连接测试
      sendResponse({ success: true, message: "Content script 正常运行" });
      return false;

    case "GET_SITE_INFO":
      // 获取站点信息
      if (isWkinfoPage()) {
        const siteInfo = getWkinfoSiteInfo();
        sendResponse({ success: true, siteInfo: siteInfo });
      } else if (isPkulawPage()) {
        const siteInfo = getPkulawSiteInfo();
        sendResponse({ success: true, siteInfo: siteInfo });
      } else if (isWenshuPage()) {
        const siteInfo = getWenshuSiteInfo();
        sendResponse({ success: true, siteInfo: siteInfo });
      } else {
        sendResponse({ success: false, message: "当前不是支持的站点" });
      }
      return false;

    case "TRIGGER_DOWNLOAD_BUTTON":
      // 威科先行已废弃下载按钮功能
      sendResponse({
        success: false,
        message: "威科先行下载按钮功能已废弃，请使用Quote按钮进行内容采集",
      });
      return false;

    case "CHECK_DOWNLOAD_BUTTON":
      // 威科先行已废弃下载按钮功能，所有站点都返回false
      sendResponse({ success: true, buttonExists: false });
      return false;

    default:
      console.log("[Content Script] 未知消息类型:", message.type);
      sendResponse({ success: false, message: "未知消息类型" });
      return false;
  }
});

// 页面加载完成后初始化
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", initialize);
} else {
  initialize();
}

// 如果页面是动态加载的，也要监听历史变化
let lastUrl = location.href;
const urlObserver = new MutationObserver(() => {
  // 检查扩展上下文
  if (!isExtensionContextValid()) {
    urlObserver.disconnect();
    return;
  }

  const currentUrl = location.href;
  if (currentUrl !== lastUrl) {
    lastUrl = currentUrl;
    console.log("[Content Script] 页面URL变化，重新初始化");
    setTimeout(initialize, 1000);
  }
});

try {
  urlObserver.observe(document, { subtree: true, childList: true });
} catch (error) {
  console.log("[Content Script] 设置URL观察器失败:", error);
}

console.log("[Content Script] 初始化完成");
