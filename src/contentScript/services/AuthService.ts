/**
 * 认证服务类
 * 提供统一的认证状态检查、登录提示和登录处理功能
 */

import { createLoginPrompt } from "../components/LoginPrompt";

export interface AuthStatus {
  isAuthenticated: boolean;
  error?: string;
}

export class AuthService {
  private static isShowingLoginPrompt: boolean = false;

  /**
   * 检查认证状态
   */
  static async checkAuthenticationStatus(): Promise<AuthStatus> {
    try {
      console.log("[AuthService] 向 Background 发送认证状态检查请求");
      const response = await chrome.runtime.sendMessage({
        type: "CHECK_AUTH_STATUS",
      });

      if (response && response.success) {
        return {
          isAuthenticated: response.isAuthenticated,
          error: response.error,
        };
      } else {
        return {
          isAuthenticated: false,
          error: response?.error || "认证检查失败",
        };
      }
    } catch (error) {
      console.error("[AuthService] 认证状态检查失败:", error);
      return {
        isAuthenticated: false,
        error: error instanceof Error ? error.message : String(error),
      };
    }
  }

  /**
   * 显示登录提示浮窗
   */
  static async showLoginPrompt(): Promise<"login" | "cancel"> {
    if (this.isShowingLoginPrompt) {
      console.log("[AuthService] 登录浮窗已在显示中，跳过重复显示");
      return "cancel";
    }

    this.isShowingLoginPrompt = true;
    try {
      const loginPrompt = createLoginPrompt({
        onLogin: async () => {
          await this.handleLogin();
        },
        onCancel: () => {
          console.log("[AuthService] 用户取消登录");
        },
      });

      return await loginPrompt.show();
    } finally {
      this.isShowingLoginPrompt = false;
    }
  }

  /**
   * 处理登录操作
   */
  static async handleLogin(): Promise<void> {
    try {
      console.log("[AuthService] 开始处理登录操作");

      const response = await chrome.runtime.sendMessage({
        type: "OPEN_LOGIN_TAB",
        data: {
          loginUrl: `${process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL || ""}/login`,
        },
      });

      if (response && response.success) {
        console.log("[AuthService] 登录标签页已打开");
      } else {
        console.error("[AuthService] 打开登录标签页失败:", response?.error);
      }
    } catch (error) {
      console.error("[AuthService] 处理登录操作失败:", error);
    }
  }

  /**
   * 重置状态（用于清理）
   */
  static reset(): void {
    this.isShowingLoginPrompt = false;
  }
}
