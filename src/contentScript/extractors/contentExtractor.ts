/**
 * 通用内容提取器
 * 根据配置从不同网站提取内容
 */

import {
  getExtractionConfig,
  ExtractionConfig,
} from "../config/extractionConfig";
import { MarkdownConverter } from "../../utils/MarkdownConverter";

/**
 * 错误类型枚举
 */
export enum ErrorType {
  CONTENT_CONTAINER_NOT_FOUND = "CONTENT_CONTAINER_NOT_FOUND",
  CONTENT_EMPTY = "CONTENT_EMPTY",
  SITE_CONFIG_NOT_FOUND = "SITE_CONFIG_NOT_FOUND",
  NETWORK_ERROR = "NETWORK_ERROR",
  AUTH_ERROR = "AUTH_ERROR",
  UNKNOWN_ERROR = "UNKNOWN_ERROR"
}

/**
 * 内容提取结果接口
 */
export interface ExtractionResult {
  success: boolean;
  content?: string;
  metadata?: Record<string, any>;
  url?: string;
  error?: string;
  errorType?: ErrorType;
  rawElement?: Element; // 新增：原始DOM元素，供格式化处理使用
}

/**
 * 从指定网站提取内容
 * @param site 网站标识
 * @returns 提取结果
 */
export function extractContent(site: string): ExtractionResult {
  const config = getExtractionConfig(site);
  if (!config) {
    return {
      success: false,
      error: `未找到站点 ${site} 的提取配置`,
      errorType: ErrorType.SITE_CONFIG_NOT_FOUND,
    };
  }

  try {
    console.log(`[ContentExtractor] 开始从 ${site} 提取内容`);

    // 1. 提取主要内容
    const contentResult = extractMainContent(config);
    if (!contentResult.success) {
      return contentResult;
    }

    // 2. 提取元数据
    const metadata = extractMetadata(config);

    // 3. 应用内容处理器
    let processedContent = contentResult.content!;
    if (config.processors) {
      processedContent = applyProcessors(
        processedContent,
        config.processors,
        contentResult.rawElement
      );
    }

    console.log(
      `[ContentExtractor] ${site} 内容提取成功，内容长度: ${processedContent.length}`
    );

    return {
      success: true,
      content: processedContent,
      metadata: metadata,
      url: window.location.href,
    };
  } catch (error) {
    console.error(`[ContentExtractor] ${site} 内容提取失败:`, error);
    const errorMessage = error instanceof Error ? error.message : String(error);
    
    // 根据错误消息判断错误类型
    let errorType = ErrorType.UNKNOWN_ERROR;
    if (errorMessage.includes("网络") || errorMessage.includes("timeout") || errorMessage.includes("fetch")) {
      errorType = ErrorType.NETWORK_ERROR;
    } else if (errorMessage.includes("认证") || errorMessage.includes("登录") || errorMessage.includes("auth")) {
      errorType = ErrorType.AUTH_ERROR;
    }
    
    return {
      success: false,
      error: errorMessage,
      errorType,
    };
  }
}

/**
 * 提取主要内容
 */
function extractMainContent(config: ExtractionConfig): ExtractionResult {
  const contentElement = document.querySelector(config.selectors.content);

  if (!contentElement) {
    return {
      success: false,
      error: `未找到内容容器: ${config.selectors.content}`,
      errorType: ErrorType.CONTENT_CONTAINER_NOT_FOUND,
    };
  }

  let processedElement = contentElement;

  // 如果配置了排除选择器且启用了元素排除功能，则处理排除逻辑
  if (config.selectors.exclude && config.processors?.excludeElements) {
    console.log(
      `[ContentExtractor] 检测到排除配置: ${config.selectors.exclude}`
    );

    // 克隆元素以避免修改原DOM
    processedElement = contentElement.cloneNode(true) as Element;

    // 处理排除逻辑
    removeExcludedElements(processedElement, config.selectors.exclude);
  }

  // 提取原始文本内容，格式转换将在applyProcessors中处理
  const content = processedElement.textContent?.trim() || "";

  console.log(`[ContentExtractor] 提取原始文本内容，长度: ${content.length}`);

  if (!content || content.trim().length === 0) {
    return {
      success: false,
      error: "内容容器为空",
      errorType: ErrorType.CONTENT_EMPTY,
    };
  }

  return {
    success: true,
    content: content,
    rawElement: processedElement, // 传递原始元素供后续格式化处理
  };
}

/**
 * 提取元数据
 */
function extractMetadata(config: ExtractionConfig): Record<string, any> {
  // 提取标题，优先使用配置的title选择器，否则使用document.title
  let title = document.title;
  if (config.selectors.title) {
    const titleElement = document.querySelector(config.selectors.title);
    if (titleElement && titleElement.textContent?.trim()) {
      title = titleElement.textContent.trim();
    }
  }

  const metadata: Record<string, any> = {
    site: config.site,
    title: title,
  };

  // 提取配置中定义的元数据
  if (config.selectors.metadata) {
    for (const [key, selector] of Object.entries(config.selectors.metadata)) {
      try {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          if (elements.length === 1) {
            metadata[key] = elements[0].textContent?.trim() || "";
          } else {
            // 多个元素，提取为数组
            metadata[key] = Array.from(elements).map(
              (el) => el.textContent?.trim() || ""
            );
          }
        }
      } catch (error) {
        console.warn(`[ContentExtractor] 提取元数据 ${key} 失败:`, error);
      }
    }
  }

  return metadata;
}

/**
 * 应用内容处理器
 * 统一处理所有格式化操作，包括Markdown转换和文本清理
 */
function applyProcessors(
  content: string,
  processors: NonNullable<ExtractionConfig["processors"]>,
  rawElement?: Element
): string {
  let processedContent = content;

  // 根据配置选择格式化方式
  if (
    processors.preserveFormatting &&
    processors.formatOptions?.outputFormat === "markdown" &&
    rawElement
  ) {
    // Markdown格式转换（内置文本清理）
    console.log(`[ContentExtractor] 应用Markdown格式转换`);
    const formatOptions = processors.formatOptions;
    processedContent = MarkdownConverter.htmlToMarkdown(rawElement, {
      preserveHeadings: formatOptions.preserveHeadings ?? true,
      preserveParagraphs: true,
      preserveLists: formatOptions.preserveLists ?? true,
      preserveLinks: formatOptions.preserveLinks ?? false,
      preserveBold: formatOptions.preserveBold ?? true,
      preserveItalic: false,
      paragraphSeparator: formatOptions.paragraphSeparator ?? "\n\n",
    });

    // 🔥 新增：后处理优化
    console.log(`[ContentExtractor] 应用Markdown后处理优化`);
    processedContent = MarkdownConverter.postProcessMarkdown(processedContent);
  } else {
    // 纯文本格式（需要cleanText处理）
    if (processors.cleanText) {
      console.log(`[ContentExtractor] 应用文本清理处理`);
      processedContent = cleanText(processedContent);
    }
  }

  return processedContent;
}

/**
 * 移除需要排除的元素
 * 支持复杂的排除规则，包括文本匹配
 */
function removeExcludedElements(
  container: Element,
  excludeSelector: string
): void {
  // 分割多个选择器（用逗号分隔）
  const selectors = excludeSelector.split(",").map((s) => s.trim());

  selectors.forEach((selector) => {
    // 检查是否包含文本匹配规则
    if (selector.includes(":contains(")) {
      // 处理包含文本的选择器
      const match = selector.match(/^(.+?):contains\('([^']+)'\)$/);
      if (match) {
        const [, baseSelector, searchText] = match;
        const elements = container.querySelectorAll(baseSelector);

        elements.forEach((el) => {
          if (el.textContent && el.textContent.includes(searchText)) {
            console.log(
              `[ContentExtractor] 移除包含文本"${searchText}"的元素:`,
              el.tagName,
              el.className
            );
            el.remove();
          }
        });
      }
    } else {
      // 处理普通CSS选择器
      const elements = container.querySelectorAll(selector);
      console.log(
        `[ContentExtractor] 找到 ${elements.length} 个需要排除的元素 (${selector})`
      );

      elements.forEach((el, index) => {
        console.log(
          `[ContentExtractor] 移除排除元素 ${index + 1}:`,
          el.tagName,
          el.className
        );
        el.remove();
      });
    }
  });
}

/**
 * 清理文本内容
 */
function cleanText(text: string): string {
  return (
    text
      // 移除多余的空白字符
      .replace(/\s+/g, " ")
      // 移除行首行尾空白
      .replace(/^\s+|\s+$/gm, "")
      // 移除多余的换行
      .replace(/\n\s*\n\s*\n/g, "\n\n")
      .trim()
  );
}

/**
 * 验证提取结果
 */
export function validateExtractionResult(result: ExtractionResult): boolean {
  if (!result.success) {
    return false;
  }

  if (!result.content || result.content.length === 0) {
    return false;
  }

  if (!result.metadata || !result.metadata.site) {
    return false;
  }

  return true;
}
