/**
 * 统一的内容提取配置管理
 * 支持多个法律数据库网站的内容提取配置
 */

/**
 * 内容提取配置接口
 */
export interface ExtractionConfig {
  /** 网站标识 */
  site: string;
  /** 选择器配置 */
  selectors: {
    /** 主要内容选择器 */
    content: string;
    /** 标题选择器 */
    title?: string;
    /** 需要排除的元素选择器 */
    exclude?: string;
    /** 元数据选择器映射 */
    metadata?: Record<string, string>;
  };
  /** 内容处理器配置 */
  processors?: {
    /** 是否清理文本 */
    cleanText?: boolean;
    /** 是否提取链接 */
    extractLinks?: boolean;
    /** 是否保留格式 */
    preserveFormatting?: boolean;
    /** 是否启用元素排除功能 */
    excludeElements?: boolean;
    /** 输出格式选项 */
    formatOptions?: {
      /** 输出格式类型 */
      outputFormat?: "text" | "markdown";
      /** 段落分隔符 */
      paragraphSeparator?: string;
      /** 是否保留标题 */
      preserveHeadings?: boolean;
      /** 是否保留列表 */
      preserveLists?: boolean;
      /** 是否保留链接 */
      preserveLinks?: boolean;
      /** 是否保留粗体 */
      preserveBold?: boolean;
    };
  };
}

/**
 * 所有支持网站的提取配置
 */
const EXTRACTION_CONFIGS: Record<string, ExtractionConfig> = {
  // 北大法宝配置
  pkulaw: {
    site: "pkulaw",
    selectors: {
      content: "#divFullText",
      title: "title",
      metadata: {
        caseNumber: ".case-flag",
        court: ".court-info",
        keywords: "#keywordv",
        caseType: ".case-type",
      },
    },
    processors: {
      cleanText: true,
      extractLinks: false,
      preserveFormatting: true,
      formatOptions: {
        outputFormat: "markdown",
        paragraphSeparator: "\n\n",
        preserveHeadings: true, // 🔥 启用标题格式
        preserveLists: true, // 🔥 启用列表格式
        preserveLinks: false, // 保持禁用链接，避免URL信息
        preserveBold: true, // 🔥 启用粗体格式
      },
    },
  },

  // 威科先行配置
  wkinfo: {
    site: "wkinfo",
    selectors: {
      content: ".detail-container",
      title: ".detail-floating-title, .doc-title, h1.biao",
      exclude:
        "h4 a.refer-url.wkb-href, b-case-parties a, .refer-url, a[href*='查看企业信息']",
      metadata: {
        caseNumber: ".detail-meta-parameter-text, .metaname#cchcncasenumber",
        court: ".metaname#cchcncourt",
        caseType: ".metaname#cchcncauseofaction",
        judgmentDate: ".metaname#cchcnjudgmentdate",
        documentId: "input#documentaid",
      },
    },
    processors: {
      cleanText: true,
      extractLinks: false,
      preserveFormatting: true,
      excludeElements: true,
      formatOptions: {
        outputFormat: "markdown",
        paragraphSeparator: "\n\n",
        preserveHeadings: true,
        preserveLists: true,
        preserveLinks: false,
        preserveBold: true,
      },
    },
  },

  // 中国裁判文书网配置
  wenshu: {
    site: "wenshu",
    selectors: {
      content: ".PDF_box", // 包含完整文档结构
      title: ".PDF_title",
      exclude: ".PDF_cut.clearfix", // 排除元数据区域
      metadata: {
        caseNumber: "#ahdiv",
        caseType: "#aydiv",
        documentId: "#wenshuid",
      },
    },
    processors: {
      cleanText: false, // 🔥 禁用cleanText，保持原始div结构
      extractLinks: false,
      preserveFormatting: true,
      excludeElements: true,
      formatOptions: {
        outputFormat: "markdown",
        paragraphSeparator: "\n\n",
        preserveHeadings: true,
        preserveLists: true,
        preserveLinks: false,
        preserveBold: true,
      },
    },
  },
};

/**
 * 获取指定网站的提取配置
 * @param site 网站标识
 * @returns 提取配置对象，如果不存在则返回null
 */
export function getExtractionConfig(site: string): ExtractionConfig | null {
  const config = EXTRACTION_CONFIGS[site];
  if (!config) {
    console.warn(`[ExtractionConfig] 未找到站点 ${site} 的提取配置`);
    return null;
  }
  return config;
}

/**
 * 获取所有支持的网站列表
 * @returns 支持的网站标识数组
 */
export function getSupportedSites(): string[] {
  return Object.keys(EXTRACTION_CONFIGS);
}

/**
 * 检查是否支持指定网站
 * @param site 网站标识
 * @returns 是否支持
 */
export function isSiteSupported(site: string): boolean {
  return site in EXTRACTION_CONFIGS;
}

/**
 * 添加新的网站配置（用于动态扩展）
 * @param config 新的提取配置
 */
export function addExtractionConfig(config: ExtractionConfig): void {
  EXTRACTION_CONFIGS[config.site] = config;
  console.log(`[ExtractionConfig] 已添加站点 ${config.site} 的提取配置`);
}
