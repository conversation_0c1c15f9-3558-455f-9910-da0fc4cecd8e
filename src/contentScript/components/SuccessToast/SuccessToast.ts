/**
 * 成功提示气泡组件
 * 用于在采集成功后显示提示信息
 */

export class SuccessToast {
  private container: HTMLDivElement;
  private isVisible: boolean = false;
  private hideTimeout: number | null = null;

  constructor() {
    this.container = this.createToastContainer();
    this.ensureStyles();
  }

  /**
   * 创建toast容器
   */
  private createToastContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-success-toast";
    return container;
  }

  /**
   * 确保样式存在
   */
  private ensureStyles(): void {
    const styleId = "ext-success-toast-styles";
    if (document.getElementById(styleId)) {
      return; // 样式已存在
    }

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      .ext-success-toast {
        position: fixed;
        background: #ffffff90;
        color: #5D5D5D;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 12px;
        font-weight: 500;
        white-space: nowrap;
        backdrop-filter: blur(60px);
        -webkit-backdrop-filter: blur(60px);
        border: 1px solid rgba(0,0,0,0.1);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10003;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        
        /* 初始状态 */
        opacity: 0;
        transform: translateX(-50%) translateY(10px);
        transition: all 0.3s ease;
        pointer-events: none;
      }

      .ext-success-toast.show {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 检查气泡是否可见
   */
  public getVisibility(): boolean {
    return this.isVisible;
  }

  /**
   * 显示成功提示
   * @param message 提示消息
   * @param duration 显示时长(ms)，默认3000ms
   */
  public show(message: string, duration: number = 3000): void {
    if (this.isVisible) {
      this.hide(); // 如果已显示，先隐藏
    }

    // 清除之前的隐藏定时器
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    this.container.textContent = message;
    document.body.appendChild(this.container);
    this.positionAboveButton();

    // 显示动画
    requestAnimationFrame(() => {
      this.container.classList.add("show");
      this.isVisible = true;
    });

    // 自动隐藏
    this.hideTimeout = window.setTimeout(() => this.hide(), duration);
  }

  /**
   * 隐藏提示
   */
  public hide(): void {
    if (!this.isVisible) return;

    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }

    this.container.classList.remove("show");
    this.isVisible = false;

    // 动画结束后移除
    setTimeout(() => {
      if (this.container.parentNode) {
        this.container.remove();
      }
    }, 300);
  }

  /**
   * 定位到Quote按钮上方
   */
  private positionAboveButton(): void {
    // 查找Quote按钮容器
    const buttonContainer = document.querySelector(
      ".ext-quote-button-container"
    ) as HTMLElement;

    if (!buttonContainer) {
      console.warn("[SuccessToast] 未找到Quote按钮容器");
      return;
    }

    const buttonRect = buttonContainer.getBoundingClientRect();

    // 临时显示toast以获取其尺寸
    const originalVisibility = this.container.style.visibility;
    const originalOpacity = this.container.style.opacity;

    this.container.style.visibility = "hidden";
    this.container.style.opacity = "1";

    const toastRect = this.container.getBoundingClientRect();

    // 计算位置（按钮上方居中，间距12px）
    const toastLeft = buttonRect.left + buttonRect.width / 2;
    const toastTop = buttonRect.top - toastRect.height - 12;

    this.container.style.left = `${toastLeft}px`;
    this.container.style.top = `${toastTop}px`;

    // 恢复原始状态
    this.container.style.visibility = originalVisibility;
    this.container.style.opacity = originalOpacity;
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    this.hide();
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout);
      this.hideTimeout = null;
    }
  }
}
