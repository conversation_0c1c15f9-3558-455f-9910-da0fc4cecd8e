/**
 * 浮动登录提示组件
 * 用于content script中显示登录提示浮窗
 */

export interface LoginPromptConfig {
  onLogin: () => Promise<void>;
  onCancel: () => void;
}

export class LoginPromptFloating {
  private promptElement: HTMLDivElement | null = null;
  private styleElement: HTMLStyleElement | null = null;
  private config: LoginPromptConfig;

  constructor(config: LoginPromptConfig) {
    this.config = config;
  }

  /**
   * 显示登录提示浮窗
   * @returns Promise<'login' | 'cancel'> 用户的选择结果
   */
  public show(): Promise<"login" | "cancel"> {
    return new Promise((resolve) => {
      try {
        // 移除已存在的提示框
        this.hide();

        // 创建提示框
        this.promptElement = this.createPromptElement();
        this.styleElement = this.createStyleElement();

        // 添加到页面
        document.head.appendChild(this.styleElement);
        document.body.appendChild(this.promptElement);

        // 设置事件处理
        this.setupEventHandlers(resolve);

        console.log("[LoginPromptFloating] 登录提示浮窗已显示");
      } catch (error) {
        console.error("[LoginPromptFloating] 显示登录提示失败:", error);
        resolve("cancel");
      }
    });
  }

  /**
   * 隐藏登录提示浮窗
   */
  public hide(): void {
    if (this.promptElement) {
      this.promptElement.remove();
      this.promptElement = null;
    }
    if (this.styleElement) {
      this.styleElement.remove();
      this.styleElement = null;
    }
  }

  /**
   * 创建提示框元素
   */
  private createPromptElement(): HTMLDivElement {
    const promptDiv = document.createElement("div");
    promptDiv.className = "ext-login-prompt";
    promptDiv.style.cssText = `
      position: fixed;
      bottom: 94px;
      left: 50%;
      transform: translateX(-50%);
      background: rgba(255, 255, 255, 0.7);
      border-radius: 12px;
      box-shadow: 0 8px 12px rgba(0,0,0,0.1);
      backdrop-filter: blur(60px);
      padding: 24px;
      width: 312px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      animation: slideUp 0.2s ease-out;
      z-index: 10001;
      border: 1px solid rgba(0,0,0,0.1);
    `;

    promptDiv.innerHTML = `
      <div style="text-align: center;">
        <div style="
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 12px;
          margin: 0 0 16px 0;
        ">
          <svg width="70" height="18" viewBox="0 0 70 18"><g><g><path d="M68.12094038208008,15.4506C66.28198038208008,15.4506,64.79031038208008,14.02359,64.79031038208008,12.26272L69.99996038208008,12.26257L69.99996038208008,5.25L59.46236038208008,5.25L59.46236038208008,12.991959999999999C59.46236038208008,15.7581,61.804670382080076,18,64.69469038208008,18L68.45447038208007,18L68.45447038208007,15.4499L68.12171038208008,15.4499L68.12094038208008,15.4506Z" fill="#4034BA" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M43.50310100585938,5.877870096954346C42.50384100585937,5.356131096954345,41.42861100585937,5.095261096954346,40.278141005859375,5.095261096954346C39.127671005859376,5.095261096954346,38.04805100585938,5.356131096954345,37.04075100585938,5.877870096954346C36.033451005859376,6.399611096954345,35.230681005859374,7.151141096954346,34.630973005859374,8.131751096954346C34.03126900585937,9.112361096954345,33.731781005859375,10.251231096954346,33.731781005859375,11.547631096954346C33.731781005859375,12.844021096954346,34.03126900585937,14.003121096954345,34.630973005859374,14.975061096954345C35.230681005859374,15.947761096954345,36.029801005859376,16.694961096954344,37.029061005859376,17.216661096954347C38.027601005859374,17.738361096954346,39.11087100585937,17.999261096954346,40.278141005859375,17.999261096954346C41.445401005859374,17.999261096954346,42.52356100585938,17.738361096954346,43.51552100585938,17.216661096954347C44.506781005859374,16.694961096954344,45.297881005859374,15.947761096954345,45.88948100585937,14.975061096954345C46.480481005859374,14.002401096954346,46.776281005859374,12.859921096954345,46.776281005859374,11.547631096954346C46.776281005859374,10.235331096954345,46.480481005859374,9.112361096954345,45.88948100585937,8.131751096954346C45.297881005859374,7.151141096954346,44.50238100585938,6.400331096954345,43.50384100585937,5.877871096954346L43.50310100585938,5.877870096954346ZM42.016631005859374,13.836921096954345C41.544751005859375,14.398401096954347,40.95746100585937,14.678781096954346,40.254031005859375,14.678781096954346C39.55060100585938,14.678781096954346,38.93921100585938,14.398401096954347,38.46733100585938,13.836921096954345C37.995461005859376,13.275431096954346,37.760251005859374,12.512341096954346,37.760251005859374,11.547631096954346C37.760251005859374,10.582911096954346,37.99619100585937,9.816201096954345,38.46733100585938,9.246771096954346C38.93848100585937,8.677341096954345,39.53453100585938,8.392621096954345,40.254031005859375,8.392621096954345C40.973541005859374,8.392621096954345,41.544751005859375,8.677341096954345,42.016631005859374,9.246771096954346C42.48777100585937,9.816201096954345,42.72371100585937,10.583641096954345,42.72371100585937,11.547631096954346C42.72371100585937,12.511611096954347,42.48777100585937,13.275431096954346,42.016631005859374,13.836921096954345Z" fill="#313131" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M55.235693331298826,14.678748292350768C54.56440333129883,14.678748292350768,54.10860333129883,14.461248292350769,53.86900333129883,14.026248292350768C53.62941333129883,13.59124829235077,53.509623331298826,12.97914829235077,53.509623331298826,12.187848292350768L53.50954333129883,9.22371829235077L57.29944333129883,9.22371829235077L57.29944333129883,5.251068292350769L53.50954333129883,5.251068292350769L53.509623331298826,1.489348292350769L49.60094333129883,1.489348292350769L49.60203333129883,5.250338292350769L47.32338333129883,5.250338292350769L47.32338333129883,9.22298829235077L49.60203333129883,9.22298829235077L49.60094333129883,12.875148292350769C49.60094333129883,14.78864829235077,50.04068333129883,16.121148292350767,50.92015333129883,16.87194829235077C51.79889333129883,17.62354829235077,52.96616333129883,17.99854829235077,54.42123333129883,17.99854829235077C56.051613331298825,17.99854829235077,57.31457333129883,17.60324829235077,58.209383331298824,16.81274829235077L56.84269333129883,14.13244829235077C56.31530333129883,14.49664829235077,55.77915333129883,14.67804829235077,55.23642333129883,14.67804829235077L55.235693331298826,14.678748292350768Z" fill="#313131" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M31.969878927612307,5.387194707969728L26.848688927612304,5.387194707969728L26.848688927612304,8.69178415664673L28.061248927612304,8.69178415664673L28.061248927612304,11.191364156646728C28.061248927612304,12.267354156646729,27.861108927612307,13.10488415664673,27.461548927612306,13.706114156646729C27.061988927612305,14.307344156646728,26.494418927612305,14.607234156646728,25.759578927612303,14.607234156646728C25.024738927612304,14.607234156646728,24.492238927612306,14.310954156646728,24.213198927612304,13.717674156646728C23.933438927612304,13.124394156646728,23.793188927612306,12.33817415664673,23.793188927612306,11.357564156646728L23.793188927612306,5.3871941566467285L18.708478927612305,5.3871941566467285L18.708478927612305,8.747424156646728L19.884518927612305,8.747424156646728L19.884518927612305,12.662634156646728C19.884518927612305,14.417904156646728,20.287728927612307,15.746794156646729,21.095608927612304,16.64789415664673C21.902768927612303,17.549094156646728,23.010138927612303,17.99999415664673,24.416998927612305,17.99999415664673C26.191278927612306,17.99999415664673,27.486378927612307,17.26439415664673,28.301568927612305,15.793794156646728C28.349778927612306,15.99969415664673,28.405298927612307,16.45789415664673,28.469568927612304,17.169694156646727L28.517788927612305,17.69139415664673L33.140878927612306,17.69139415664673L33.140878927612306,14.609404156646729L31.970678927612305,14.609404156646729L31.970678927612305,5.387195259296728L31.969878927612307,5.387194707969728Z" fill="#313131" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M16.0131,13.8022C16.7801,12.443,17.1635,10.838,17.1635,8.98808C17.1635,7.13814,16.7801,5.55775,16.0131,4.19776C15.2461,2.83849,14.211,1.79863,12.9086,1.07889C11.6062,0.35987,10.1643,0,8.58214,0C6.99997,0,5.55366,0.35987,4.24322,1.07889C2.93278,1.79863,1.89773,2.83777,1.13878,4.19776C0.379107,5.55775,0,7.15404,0,8.98808C0,10.8221,0.379107,12.443,1.13878,13.8022C1.89773,15.1622,2.9284,16.2014,4.23081,16.9211C5.53321,17.6409,6.9839,18,8.58214,18L17.9758,18L17.9758,14.6766L15.4514,14.6766C15.6522,14.4013,15.84,14.1101,16.0138,13.8022L16.0131,13.8022ZM6.37689,13.6483C5.73774,13.2133,5.23811,12.5933,4.87872,11.7868C4.51934,10.9804,4.33964,10.0482,4.33964,8.98808C4.33964,7.92798,4.51934,7.02397,4.87872,6.22546C5.23884,5.42695,5.73774,4.81055,6.37689,4.37553C7.01604,3.9405,7.75088,3.72299,8.58214,3.72299C9.4134,3.72299,10.1519,3.9405,10.7998,4.37553C11.447,4.81055,11.9503,5.42695,12.3097,6.22546C12.669,7.02397,12.8487,7.9446,12.8487,8.98808C12.8487,10.0316,12.669,10.9804,12.3097,11.7868C11.9503,12.5933,11.447,13.214,10.7998,13.6483C10.1526,14.0833,9.4134,14.3009,8.58214,14.3009C7.75088,14.3009,7.01531,14.0841,6.37689,13.6483Z" fill="#313131" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>

          <h3 style="
            margin: 0;
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
          ">暂未登录</h3>
        </div>
        
        <p style="
          margin: 0 0 18px 0;
          color: #6b7280;
          font-size: 14px;
          line-height: 1.5;
        ">请先登录 Quote 账户后再进行采集操作</p>
        
        <div style="
          display: flex;
          gap: 12px;
          justify-content: center;
        ">
          <button class="ext-login-cancel-btn" style="
            background: #E7E7E7;
            color: #525252;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            min-width: 80px;
          ">取消</button>
          
          <button class="ext-login-confirm-btn" style="
            background: #574BC8;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s;
            min-width: 80px;
          ">立即登录</button>
        </div>
      </div>
    `;

    return promptDiv;
  }

  /**
   * 创建样式元素
   */
  private createStyleElement(): HTMLStyleElement {
    const style = document.createElement("style");
    style.textContent = `
      @keyframes slideUp {
        from { 
          transform: translateX(-50%) translateY(20px); 
          opacity: 0; 
        }
        to { 
          transform: translateX(-50%) translateY(0); 
          opacity: 1; 
        }
      }
      @keyframes slideDown {
        from { 
          transform: translateX(-50%) translateY(0); 
          opacity: 1; 
        }
        to { 
          transform: translateX(-50%) translateY(20px); 
          opacity: 0; 
        }
      }
      .ext-login-cancel-btn:hover {
        background: #D6D6D6 !important;
      }
      .ext-login-confirm-btn:hover {
        background: #4034BA !important;
      }
    `;
    return style;
  }

  /**
   * 设置事件处理器
   */
  private setupEventHandlers(
    resolve: (result: "login" | "cancel") => void
  ): void {
    if (!this.promptElement) return;

    // 处理取消按钮
    const cancelBtn = this.promptElement.querySelector(".ext-login-cancel-btn");
    cancelBtn?.addEventListener("click", () => {
      this.config.onCancel();
      this.closePrompt("cancel", resolve);
    });

    // 处理立即登录按钮
    const confirmBtn = this.promptElement.querySelector(
      ".ext-login-confirm-btn"
    );
    confirmBtn?.addEventListener("click", async () => {
      try {
        await this.config.onLogin();
        this.closePrompt("login", resolve);
        console.log(
          "[LoginPromptFloating] 登录页面已打开，请在新标签页完成登录后重新尝试采集"
        );
      } catch (error) {
        console.error("[LoginPromptFloating] 登录处理失败:", error);
        this.closePrompt("cancel", resolve);
      }
    });

    // ESC键关闭
    const handleEscKey = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        this.closePrompt("cancel", resolve);
        document.removeEventListener("keydown", handleEscKey);
      }
    };
    document.addEventListener("keydown", handleEscKey);

    // 保存handleEscKey的引用用于清理
    (this.promptElement as any).handleEscKey = handleEscKey;
  }

  /**
   * 关闭提示框
   */
  private closePrompt(
    result: "login" | "cancel",
    resolve: (result: "login" | "cancel") => void
  ): void {
    if (!this.promptElement) return;

    this.promptElement.style.animation = "slideDown 0.2s ease-out";
    setTimeout(() => {
      if (this.promptElement) {
        // 清理ESC键事件监听器
        const handleEscKey = (this.promptElement as any).handleEscKey;
        if (handleEscKey) {
          document.removeEventListener("keydown", handleEscKey);
        }
      }
      this.hide();
      resolve(result);
    }, 200);
  }
}

/**
 * 创建登录提示浮窗实例
 */
export function createLoginPrompt(
  config: LoginPromptConfig
): LoginPromptFloating {
  return new LoginPromptFloating(config);
}
