/**
 * 最小化的Quote按钮组件 - 用于调试
 */

import { ButtonState } from "./types";

interface MinimalConfig {
  onCollectToProject: (projectId: string) => Promise<void>;
  onCollectToInbox: () => Promise<void>;
}

export class QuoteButtonMinimal {
  private container: HTMLDivElement;
  private splitButtonContainer: HTMLDivElement;
  private leftButton: HTMLButtonElement;
  private rightButton: HTMLButtonElement;
  private state: ButtonState = ButtonState.IDLE;
  private config: MinimalConfig;

  constructor(config: MinimalConfig) {
    console.log("[QuoteButtonMinimal] 开始初始化");
    this.config = config;
    
    try {
      // 创建基础容器
      this.container = this.createContainer();
      console.log("[QuoteButtonMinimal] 容器创建成功");
      
      // 创建分割按钮容器
      this.splitButtonContainer = this.createSplitButtonContainer();
      console.log("[QuoteButtonMinimal] 分割按钮容器创建成功");
      
      // 创建按钮
      this.leftButton = this.createLeftButton();
      console.log("[QuoteButtonMinimal] 左侧按钮创建成功");
      
      this.rightButton = this.createRightButton();
      console.log("[QuoteButtonMinimal] 右侧按钮创建成功");
      
      // 组装
      this.splitButtonContainer.appendChild(this.leftButton);
      this.splitButtonContainer.appendChild(this.rightButton);
      this.container.appendChild(this.splitButtonContainer);
      
      console.log("[QuoteButtonMinimal] 初始化完成");
    } catch (error) {
      console.error("[QuoteButtonMinimal] 初始化失败:", error);
      throw error;
    }
  }

  /**
   * 获取组件根元素
   */
  public getElement(): HTMLElement {
    return this.container;
  }

  /**
   * 创建容器
   */
  private createContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-quote-button-container-minimal";
    container.style.cssText = `
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10000;
    `;
    return container;
  }

  /**
   * 创建分割按钮容器
   */
  private createSplitButtonContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-split-button-container-minimal";
    container.style.cssText = `
      display: flex;
      border-radius: 10px;
      overflow: hidden;
      border: 1px solid rgba(0,0,0,0.1);
      background: rgba(255,255,255,0.9);
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    `;
    return container;
  }

  /**
   * 创建左侧按钮
   */
  private createLeftButton(): HTMLButtonElement {
    const button = document.createElement("button");
    button.className = "ext-quote-left-button-minimal";
    button.type = "button";
    button.textContent = "选择项目采集";
    button.style.cssText = `
      flex: 3;
      padding: 12px 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      font-size: 14px;
      font-weight: 600;
    `;

    button.addEventListener("click", () => {
      console.log("[QuoteButtonMinimal] 左侧按钮被点击");
      this.config.onCollectToProject("test-project-id");
    });

    return button;
  }

  /**
   * 创建右侧按钮
   */
  private createRightButton(): HTMLButtonElement {
    const button = document.createElement("button");
    button.className = "ext-quote-right-button-minimal";
    button.type = "button";
    button.textContent = "⚡";
    button.style.cssText = `
      flex: 1;
      padding: 12px 8px;
      border: none;
      border-left: 1px solid rgba(0,0,0,0.1);
      background: transparent;
      cursor: pointer;
      font-size: 16px;
    `;

    button.addEventListener("click", () => {
      console.log("[QuoteButtonMinimal] 右侧按钮被点击");
      this.config.onCollectToInbox();
    });

    return button;
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container);
    }
  }
}

/**
 * 创建最小化Quote按钮
 */
export function createQuoteButtonMinimal(config: MinimalConfig): QuoteButtonMinimal {
  return new QuoteButtonMinimal(config);
}
