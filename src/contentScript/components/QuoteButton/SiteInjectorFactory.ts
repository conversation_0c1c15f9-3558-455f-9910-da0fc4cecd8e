/**
 * 通用站点注入器工厂
 * 抽象三个站点的共同逻辑，减少代码重复
 */

import { QuoteButton } from "./QuoteButton";
import { ButtonState } from "./types";
import { AuthService } from "../../services/AuthService";
import {
  extractContent,
  validateExtractionResult,
} from "../../extractors/contentExtractor";
import { UrlUtils } from "../../../utils/UrlUtils";

/**
 * 站点配置接口
 */
export interface SiteConfig {
  /** 站点名称标识 */
  siteName: string;
  /** 日志前缀 */
  logPrefix: string;
  /** 内容提取器类型 */
  extractorType: string;
  /** 显示名称 */
  displayName?: string;
}

/**
 * 注入器接口
 */
export interface SiteInjector {
  /** 注入Quote按钮到页面 */
  injectQuoteButton: () => boolean;
  /** 创建Quote按钮组件 */
  createQuoteButton: () => QuoteButton;
}

/**
 * 创建站点注入器工厂
 * @param config 站点配置
 * @returns 站点注入器实例
 */
export function createSiteInjector(config: SiteConfig): SiteInjector {
  const { siteName, logPrefix, extractorType } = config;

  // 存储当前按钮实例的引用
  let currentButtonInstance: QuoteButton | null = null;

  /**
   * 执行采集流程的通用函数
   */
  async function performCollectFlow(targetProjectId: string): Promise<void> {
    try {
      // 1. 检查认证状态
      console.log(`${logPrefix} 检查认证状态...`);
      const authStatus = await AuthService.checkAuthenticationStatus();

      if (!authStatus.isAuthenticated) {
        console.log(`${logPrefix} 用户未登录，显示登录提示浮窗`);

        // 设置按钮为等待登录状态，避免超时
        if (currentButtonInstance) {
          currentButtonInstance.setWaitingForLogin(true);
        }

        const userChoice = await AuthService.showLoginPrompt();

        // 无论用户选择什么，都清除等待登录状态
        if (currentButtonInstance) {
          currentButtonInstance.setWaitingForLogin(false);
        }

        if (userChoice === "cancel") {
          console.log(`${logPrefix} 用户取消登录，停止采集流程`);
          throw new Error("USER_CANCELLED");
        } else {
          console.log(
            `${logPrefix} 用户选择登录，登录页面已打开，停止当前采集流程`
          );
          throw new Error("USER_LOGIN_REDIRECT");
        }
      }

      // 2. 认证成功，开始内容提取和采集流程
      console.log(`${logPrefix} 认证成功，开始内容提取`);

      // 2.1 提取页面内容
      const extractResult = extractContent(extractorType);
      if (!extractResult.success) {
        throw new Error(`内容提取失败: ${extractResult.error}`);
      }

      // 2.2 验证提取结果
      if (!validateExtractionResult(extractResult)) {
        throw new Error("提取的内容验证失败");
      }

      console.log(
        `${logPrefix} 内容提取成功，内容长度: ${extractResult.content!.length}`
      );

      // 2.3 发送内容到后端
      console.log(
        `${logPrefix} 准备发送内容到后端，项目ID: ${targetProjectId}`
      );
      const sendResult = await sendContentToBackend(
        extractResult.content!,
        extractResult.metadata!,
        extractResult.url!,
        targetProjectId
      );

      if (!sendResult) {
        console.error(
          `${logPrefix} 发送内容到后端失败，sendResult: ${sendResult}`
        );
        throw new Error("发送内容到后端失败，请检查网络连接和后端服务状态");
      }

      console.log(`${logPrefix} 内容提取和发送完成`);
    } catch (error) {
      console.error(`${logPrefix} 采集过程出错:`, error);
      throw error;
    }
  }

  /**
   * 监听认证状态变化消息
   */
  function setupAuthStatusListener() {
    console.log(`${logPrefix} 设置认证状态变化监听器`);

    chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
      if (message.type === "AUTH_STATUS_CHANGED") {
        console.log(`${logPrefix} 收到认证状态变化消息:`, message);

        // 如果登录成功，更新按钮状态
        if (message.isAuthenticated && currentButtonInstance) {
          console.log(`${logPrefix} 更新按钮认证状态为已登录`);
          updateButtonAuthStatus(true);
        }
      }
    });
  }

  /**
   * 更新按钮认证状态
   */
  function updateButtonAuthStatus(isAuthenticated: boolean) {
    if (currentButtonInstance && isAuthenticated) {
      console.log(`${logPrefix} 按钮认证状态已更新为已登录`);
      // 调用按钮实例的认证状态更新方法
      currentButtonInstance.updateAuthStatus(isAuthenticated);
    }
  }

  /**
   * 创建Quote按钮组件
   */
  function createQuoteButton(): QuoteButton {
    console.log(`${logPrefix} 尝试创建分割按钮`);

    const button = new QuoteButton({
      onCollectToProject: async (projectId: string) => {
        console.log(`${logPrefix} 开始采集到项目: ${projectId}`);
        await performCollectFlow(projectId);
      },
      onCollectToInbox: async () => {
        console.log(`${logPrefix} 开始采集到收件箱`);
        await performCollectFlow("INBOX");
      },
      onStateChange: (state: ButtonState) => {
        console.log(`${logPrefix} 状态变化: ${state}`);
      },
    });

    // 保存按钮实例引用
    currentButtonInstance = button;

    return button;
  }

  /**
   * 注入Quote按钮到页面
   */
  function injectQuoteButton(): boolean {
    try {
      // 检查是否已经注入过按钮
      const existingButton = document.querySelector(
        ".ext-quote-button-container"
      );

      if (existingButton) {
        console.log(`${logPrefix} 按钮已存在，跳过注入`);
        return true;
      }

      console.log(`${logPrefix} 开始注入Quote按钮`);

      // 创建Quote按钮组件
      const quoteButtonComponent = createQuoteButton();
      const quoteButtonElement = quoteButtonComponent.getElement();

      // 直接添加到页面body（组件内部已包含所有样式和定位）
      document.body.appendChild(quoteButtonElement);

      // 设置认证状态变化监听器
      setupAuthStatusListener();

      console.log(`${logPrefix} 成功注入Quote按钮到页面`);
      return true;
    } catch (error) {
      console.error(`${logPrefix} 注入按钮失败:`, error);
      return false;
    }
  }

  /**
   * 发送提取的内容到后端
   */
  async function sendContentToBackend(
    content: string,
    metadata: Record<string, any>,
    url: string,
    targetProjectId?: string
  ): Promise<boolean> {
    try {
      console.log(`${logPrefix} 准备发送内容到后端`);

      // 🔥 处理超长URL
      const processedUrl = UrlUtils.cleanAndTruncateUrl(url, 255);
      const urlInfo = UrlUtils.getUrlProcessingInfo(url, processedUrl);

      // 记录URL处理信息
      if (urlInfo.wasProcessed) {
        console.log(`${logPrefix} URL处理完成:`, {
          originalLength: urlInfo.originalLength,
          processedLength: urlInfo.processedLength,
          lengthReduction: urlInfo.lengthReduction,
          processingType: urlInfo.processingType,
          originalUrl: url,
          processedUrl: processedUrl,
        });
      }

      // 构建发送给后端的JSON数据
      const payload = {
        site: siteName,
        content: content,
        metadata: {
          ...metadata,
          url: processedUrl, // 🔥 使用处理后的URL
          // 如果URL被处理过，保留原始URL信息用于调试
          ...(urlInfo.wasProcessed && {
            original_url_length: urlInfo.originalLength,
            url_processing_type: urlInfo.processingType,
            url_was_truncated: true,
          }),
        },
        timestamp: Date.now(),
        targetProjectId: targetProjectId, // 添加目标项目ID
      };

      console.log(`${logPrefix} 发送数据预览:`, {
        site: payload.site,
        contentLength: payload.content.length,
        metadataKeys: Object.keys(payload.metadata),
        timestamp: payload.timestamp,
        targetProjectId: payload.targetProjectId,
      });

      // 通过Background Script发送到后端
      const messageType =
        targetProjectId === "INBOX"
          ? "SEND_EXTRACTED_CONTENT_TO_INBOX"
          : "SEND_EXTRACTED_CONTENT_TO_PROJECT";

      console.log(`${logPrefix} 发送消息类型: ${messageType}`);

      const response = await chrome.runtime.sendMessage({
        type: messageType,
        data: payload,
      });

      console.log(`${logPrefix} 后端响应:`, response);

      if (response && response.success) {
        console.log(`${logPrefix} 内容发送成功，响应详情:`, response.result);
        return true;
      } else {
        console.error(`${logPrefix} 后端返回失败:`, {
          success: response?.success,
          error: response?.error,
          fullResponse: response,
        });
        return false;
      }
    } catch (error) {
      console.error(`${logPrefix} 发送内容到后端失败:`, error);
      return false;
    }
  }

  // 返回注入器接口
  return {
    injectQuoteButton,
    createQuoteButton,
  };
}

/**
 * 预定义的站点配置
 */
export const SITE_CONFIGS = {
  WKINFO: {
    siteName: "wkinfo",
    logPrefix: "[Wkinfo Quote按钮]",
    extractorType: "wkinfo",
    displayName: "威科先行",
  },
  PKULAW: {
    siteName: "pkulaw",
    logPrefix: "[PKULaw Quote按钮]",
    extractorType: "pkulaw",
    displayName: "北大法宝",
  },
  WENSHU: {
    siteName: "wenshu",
    logPrefix: "[Wenshu Quote按钮]",
    extractorType: "wenshu",
    displayName: "中国裁判文书网",
  },
} as const;
