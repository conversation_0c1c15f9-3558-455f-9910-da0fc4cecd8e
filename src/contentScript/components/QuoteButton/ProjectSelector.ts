/**
 * 项目选择器组件
 * 用于显示项目选择浮窗
 */

import { Project } from "./types";
import { Icons } from "../icon/icons";
import { TextUtils } from "../../../utils/TextUtils";

export interface ProjectSelectorConfig {
  onProjectSelect: (projectId: string) => void;
  onCancel: () => void;
}

export class ProjectSelector {
  private container: HTMLDivElement;
  private overlay: HTMLDivElement;
  private isVisible: boolean = false;
  private config: ProjectSelectorConfig;
  private projects: Project[] = [];

  constructor(config: ProjectSelectorConfig) {
    this.config = config;
    this.container = this.createContainer();
    this.overlay = this.createOverlay();
    this.setupStyles();
  }

  /**
   * 检查项目选择器是否可见
   */
  public getVisibility(): boolean {
    return this.isVisible;
  }

  /**
   * 根据ID获取项目信息
   */
  public getProjectById(projectId: string): Project | null {
    return this.projects.find((p) => p.id === projectId) || null;
  }

  /**
   * 显示项目选择器
   */
  public async show(): Promise<void> {
    if (this.isVisible) return;

    console.log("[ProjectSelector] 显示项目选择器");
    this.isVisible = true;

    // 确保样式存在
    this.ensureAnimationStyles();
    this.ensureScrollbarStyles();
    this.ensureHoverStyles();

    // 添加到页面
    document.body.appendChild(this.overlay);
    document.body.appendChild(this.container);

    // 加载项目列表
    await this.loadAndRenderProjects();

    // 显示动画
    this.showWithAnimation();

    // 添加点击外部关闭的事件监听
    this.setupOutsideClickHandler();
  }

  /**
   * 隐藏项目选择器
   */
  public hide(): void {
    if (!this.isVisible) return;

    console.log("[ProjectSelector] 隐藏项目选择器");
    this.isVisible = false;

    // 隐藏动画
    this.hideWithAnimation(() => {
      // 动画完成后移除元素
      if (this.container.parentNode) {
        this.container.parentNode.removeChild(this.container);
      }
      if (this.overlay.parentNode) {
        this.overlay.parentNode.removeChild(this.overlay);
      }
    });

    // 移除事件监听
    this.removeOutsideClickHandler();
  }

  /**
   * 创建容器
   */
  private createContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-project-selector";
    return container;
  }

  /**
   * 创建遮罩层
   */
  private createOverlay(): HTMLDivElement {
    const overlay = document.createElement("div");
    overlay.className = "ext-project-selector-overlay";
    return overlay;
  }

  /**
   * 设置样式
   */
  private setupStyles(): void {
    // 遮罩层样式
    this.overlay.style.cssText = `
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: transparent;
      z-index: 9999;
      opacity: 0;
      transition: opacity 0.2s ease;
    `;

    // 容器样式 - 参考认证浮窗的毛玻璃设计
    this.container.style.cssText = `
      position: fixed;
      bottom: 100px;
      left: 50%;
      transform: translateX(-50%) translateY(10px);
      background: #ffffff90;
      border-radius: 12px;
      box-shadow: 0 8px 12px rgba(0,0,0,0.1);
      backdrop-filter: blur(60px);
      -webkit-backdrop-filter: blur(60px);
      border: 1px solid rgba(0,0,0,0.1);
      z-index: 10002;
      min-width: 320px;
      max-width: 360px;
      max-height: 300px;
      overflow: hidden;
      opacity: 0;
      transition: all 0.2s ease;
      animation: slideUp 0.2s ease-out;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    `;
  }

  /**
   * 确保动画样式存在
   */
  private ensureAnimationStyles(): void {
    if (!document.querySelector("#ext-project-selector-animations")) {
      const style = document.createElement("style");
      style.id = "ext-project-selector-animations";
      style.textContent = `
        @keyframes slideUp {
          from {
            opacity: 0;
            transform: translateX(-50%) translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 确保滚动条样式存在
   */
  private ensureScrollbarStyles(): void {
    if (!document.querySelector("#ext-project-scrollbar-styles")) {
      const style = document.createElement("style");
      style.id = "ext-project-scrollbar-styles";
      style.textContent = `
        .ext-project-list-container::-webkit-scrollbar {
          width: 4px;
        }
        .ext-project-list-container::-webkit-scrollbar-track {
          background: transparent;
        }
        .ext-project-list-container::-webkit-scrollbar-thumb {
          background: rgba(0,0,0,0.1);
          border-radius: 2px;
        }
        .ext-project-list-container::-webkit-scrollbar-thumb:hover {
          background: rgba(0,0,0,0.2);
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 确保hover样式存在
   */
  private ensureHoverStyles(): void {
    if (!document.querySelector("#ext-project-hover-styles")) {
      const style = document.createElement("style");
      style.id = "ext-project-hover-styles";
      style.textContent = `
        .ext-project-item {
          transition: all 0.2s ease;
        }
        .ext-project-item:hover {
          background-color: #f5f5f5 !important;
          border-radius: 6px;
          margin: 0 8px;
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 计算容器高度
   */
  private calculateContainerHeight(projectCount: number): string {
    const itemHeight = 36;
    const maxVisibleItems = 3.5; // 改为3.5个选项

    if (projectCount <= 3) {
      return `${projectCount * itemHeight}px`;
    } else {
      return `${maxVisibleItems * itemHeight}px`; // 126px
    }
  }

  /**
   * 阻止滚动穿透
   */
  private preventScrollPropagation(element: HTMLElement): void {
    element.addEventListener(
      "wheel",
      (e) => {
        const { scrollTop, scrollHeight, clientHeight } = element;
        const isAtTop = scrollTop === 0;
        const isAtBottom = scrollTop + clientHeight >= scrollHeight;

        // 在顶部向上滚动或底部向下滚动时阻止事件传播
        if ((isAtTop && e.deltaY < 0) || (isAtBottom && e.deltaY > 0)) {
          e.preventDefault();
        }
        e.stopPropagation();
      },
      { passive: false }
    );
  }

  /**
   * 加载并渲染项目列表
   */
  private async loadAndRenderProjects(): Promise<void> {
    try {
      // 显示加载状态
      this.renderLoadingState();

      // 通过background script获取项目列表
      const response = await chrome.runtime.sendMessage({
        type: "GET_PROJECTS",
      });

      if (response && response.success) {
        this.projects = response.projects || [];
        console.log("[ProjectSelector] 获取到项目列表:", this.projects);
        this.renderProjectList();
      } else {
        console.error("[ProjectSelector] 获取项目列表失败:", response?.error);
        this.renderErrorState();
      }
    } catch (error) {
      console.error("[ProjectSelector] 加载项目列表异常:", error);
      this.renderErrorState();
    }
  }

  /**
   * 渲染加载状态
   */
  private renderLoadingState(): void {
    this.container.innerHTML = `
      <div style="padding: 20px; text-align: center;">
        <div style="margin-bottom: 12px;">
          ${Icons.loading.replace('width="16"', 'width="20"').replace('height="16"', 'height="20"')}
        </div>
        <div style="font-size: 14px; color: #666; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
          加载项目中...
        </div>
      </div>
    `;

    // 确保旋转动画样式存在
    this.ensureSpinAnimation();
  }

  /**
   * 渲染项目列表
   */
  private renderProjectList(): void {
    // 按last_edited_at排序
    const sortedProjects = [...this.projects].sort(
      (a, b) =>
        new Date(b.last_edited_at).getTime() -
        new Date(a.last_edited_at).getTime()
    );

    const header = `
      <div style="padding: 16px 16px 12px 16px;">
        <div style="font-size: 14px; font-weight: 600; color: #333; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
          采集至目标位置
        </div>
      </div>
    `;

    const projectItems = sortedProjects
      .map((project) => this.createProjectItemHTML(project))
      .join("");

    const projectCount = sortedProjects.length;
    const containerHeight = this.calculateContainerHeight(projectCount);
    const showScrollbar = projectCount > 3;

    // 底部渐变遮罩
    const gradientMask =
      projectCount > 3
        ? `
      <div style="
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        height: 20px;
        background: linear-gradient(transparent, #ffffffB3);
        pointer-events: none;
        border-radius: 0 0 12px 12px;
      "></div>
    `
        : "";

    this.container.innerHTML = `
      <div style="position: relative;">
        ${header}
        <div class="ext-project-list-container" style="
          height: ${containerHeight};
          overflow-y: ${showScrollbar ? "auto" : "hidden"};
          overflow-x: hidden;
        ">
          ${projectItems}
        </div>
        ${gradientMask}
      </div>
    `;

    // 添加滚动事件处理
    const listContainer = this.container.querySelector(
      ".ext-project-list-container"
    );
    if (listContainer && showScrollbar) {
      this.preventScrollPropagation(listContainer as HTMLElement);
    }

    // 添加点击事件
    this.attachProjectClickEvents();
  }

  /**
   * 创建项目选项HTML
   */
  private createProjectItemHTML(project: Project): string {
    // 应用中间省略逻辑
    const displayName = TextUtils.applyMiddleEllipsis(
      project.name,
      30, // maxLength: 适合当前浮窗宽度(320-360px)
      24, // startChars: 保留开头12个视觉单位
      6, // endChars: 保留结尾5个视觉单位
      1.8 // chineseWidthRatio: 中文字符宽度比例
    );

    return `
      <div class="ext-project-item" data-project-id="${project.id}" style="
        padding: 8px 16px;
        height: 36px;
        cursor: pointer;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        display: flex;
        align-items: center;
        box-sizing: border-box;
      " title="${this.escapeHtml(project.name)}">
        <span style="margin-right: 8px; color: #666;">${Icons.folderMinus}</span>
        <div style="
          flex: 1;
          font-size: 14px;
          font-weight:400;
          color: #333;
          white-space: nowrap;
        ">
          ${this.escapeHtml(displayName)}
        </div>
      </div>
    `;
  }

  /**
   * 渲染错误状态
   */
  private renderErrorState(): void {
    this.container.innerHTML = `
      <div style="padding: 20px; text-align: center;">
        <div style="margin-bottom: 12px; font-size: 24px;">⚠️</div>
        <div style="font-size: 14px; color: #666; font-family: -apple-system, BlinkMacSystemFont, sans-serif;">
          加载项目失败，请重试
        </div>
      </div>
    `;
  }

  /**
   * 添加项目点击事件
   */
  private attachProjectClickEvents(): void {
    const projectItems = this.container.querySelectorAll(".ext-project-item");
    projectItems.forEach((item) => {
      item.addEventListener("click", () => {
        const projectId = item.getAttribute("data-project-id");
        if (projectId) {
          console.log("[ProjectSelector] 选择项目:", projectId);
          this.config.onProjectSelect(projectId);
          this.hide();
        }
      });
    });
  }

  /**
   * 显示动画
   */
  private showWithAnimation(): void {
    // 强制重排以确保初始样式生效
    this.container.offsetHeight;
    this.overlay.offsetHeight;

    // 应用显示样式
    this.overlay.style.opacity = "1";
    this.container.style.opacity = "1";
    this.container.style.transform = "translateX(-50%) translateY(0)";

    // 对整个容器添加滚动穿透阻止
    this.preventScrollPropagation(this.container);
  }

  /**
   * 隐藏动画
   */
  private hideWithAnimation(callback: () => void): void {
    this.overlay.style.opacity = "0";
    this.container.style.opacity = "0";
    this.container.style.transform = "translateX(-50%) translateY(10px)";

    // 动画完成后执行回调
    setTimeout(callback, 200);
  }

  /**
   * 设置点击外部关闭
   */
  private setupOutsideClickHandler(): void {
    this.overlay.addEventListener("click", this.handleOutsideClick);
  }

  /**
   * 移除点击外部关闭
   */
  private removeOutsideClickHandler(): void {
    this.overlay.removeEventListener("click", this.handleOutsideClick);
  }

  /**
   * 处理点击外部
   */
  private handleOutsideClick = (): void => {
    this.config.onCancel();
    this.hide();
  };

  /**
   * 确保旋转动画样式存在
   */
  private ensureSpinAnimation(): void {
    if (!document.querySelector("#ext-project-selector-animations")) {
      const style = document.createElement("style");
      style.id = "ext-project-selector-animations";
      style.textContent = `
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * HTML转义
   */
  private escapeHtml(text: string): string {
    const div = document.createElement("div");
    div.textContent = text;
    return div.innerHTML;
  }
}
