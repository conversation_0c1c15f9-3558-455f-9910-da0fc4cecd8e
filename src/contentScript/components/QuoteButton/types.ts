/**
 * Quote按钮相关类型定义
 */

/**
 * 按钮状态枚举
 */
export enum ButtonState {
  IDLE = "idle", // 空闲状态
  PROCESSING = "processing", // 处理中
  SUCCESS = "success", // 采集成功
  ERROR = "error", // 采集失败
  LOADING_PROJECTS = "loading_projects", // 加载项目列表中
}

/**
 * 项目信息接口
 */
export interface Project {
  id: string;
  name: string;
  created_at: string;
  last_edited_at: string;
  description: string;
  status: "ACTIVE" | "ARCHIVED" | "INBOX";
  is_favorite: boolean;
}

/**
 * 项目选项
 */
export interface ProjectOption {
  value: string;
  label: string;
  description?: string;
}

/**
 * 按钮配置
 */
export interface ButtonConfig {
  defaultProject: string;
  projects: ProjectOption[];
  autoResetDelay: number; // 状态自动重置延迟（毫秒）
}

/**
 * 按钮状态数据
 */
export interface ButtonStateData {
  state: ButtonState;
  selectedProject: string;
  message?: string;
  progress?: number;
}

/**
 * 按钮事件回调
 */
export interface ButtonCallbacks {
  onCollect: (project: string) => Promise<void>;
  onProjectChange: (project: string) => void;
  onStateChange: (state: ButtonStateData) => void;
}
