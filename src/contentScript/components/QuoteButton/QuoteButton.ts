/**
 * 简化的Quote采集按钮组件 - 升级为分割按钮
 */

import { ButtonState } from "./types";
import { ProjectSelector } from "./ProjectSelector";
import { AuthService } from "../../services/AuthService";
import { Icons } from "../icon/icons";
import { SuccessToast } from "../SuccessToast";
import { TextUtils } from "../../../utils/TextUtils";
import { ErrorType } from "../../extractors/contentExtractor";

interface SplitButtonConfig {
  onCollectToProject: (projectId: string) => Promise<void>; // 左侧：选择项目采集
  onCollectToInbox: () => Promise<void>; // 右侧：直接采集到收件箱
  onStateChange?: (state: ButtonState) => void;
}

export class QuoteButton {
  private container: HTMLDivElement;
  private splitButtonContainer: HTMLDivElement;
  private leftButton: HTMLButtonElement; // 左侧主按钮
  private rightButton: HTMLButtonElement; // 右侧次级按钮
  private tooltip: HTMLDivElement; // 右侧按钮的tooltip
  private state: ButtonState = ButtonState.IDLE;
  private config: SplitButtonConfig;
  private timeoutId: number | null = null;
  private isWaitingForLogin: boolean = false;
  private projectSelector: ProjectSelector;
  private successToast: SuccessToast;
  private currentErrorType: ErrorType | null = null;

  constructor(config: SplitButtonConfig) {
    this.config = config;

    // 先创建基础容器
    this.container = this.createContainer();

    // 创建分割按钮相关元素
    this.splitButtonContainer = this.createSplitButtonContainer();
    this.leftButton = this.createLeftButton();
    this.rightButton = this.createRightButton();
    this.tooltip = this.createTooltip();

    // 组装分割按钮
    this.splitButtonContainer.appendChild(this.leftButton);
    this.splitButtonContainer.appendChild(this.rightButton);
    this.container.appendChild(this.splitButtonContainer);

    // tooltip使用fixed定位，直接添加到body
    document.body.appendChild(this.tooltip);

    // 创建项目选择器
    this.projectSelector = new ProjectSelector({
      onProjectSelect: (projectId: string) => {
        this.handleProjectSelect(projectId);
      },
      onCancel: () => {
        console.log("[QuoteButton] 用户取消项目选择");
      },
    });

    // 创建成功提示组件
    this.successToast = new SuccessToast();

    this.updateButtonAppearance();
    this.setupFloatingStyles();
  }

  /**
   * 获取组件根元素
   */
  public getElement(): HTMLDivElement {
    return this.container;
  }

  /**
   * 设置状态
   */
  public setState(state: ButtonState): void {
    this.state = state;
    this.updateButtonAppearance();
    this.config.onStateChange?.(state);
  }

  /**
   * 重置到空闲状态
   */
  public resetToIdle(): void {
    this.isWaitingForLogin = false;
    this.setState(ButtonState.IDLE);
  }

  /**
   * 设置登录等待状态
   */
  public setWaitingForLogin(waiting: boolean): void {
    this.isWaitingForLogin = waiting;

    // 如果进入等待登录状态，隐藏tooltip
    if (waiting) {
      this.hideTooltip();
    }
  }

  /**
   * 外部更新认证状态
   * 当检测到用户已登录时调用此方法
   */
  public updateAuthStatus(isAuthenticated: boolean): void {
    if (isAuthenticated) {
      console.log("[QuoteButton] 认证状态已更新为已登录");

      // 如果当前正在等待登录，重置状态
      if (this.isWaitingForLogin) {
        this.resetToIdle();
        console.log("[QuoteButton] 检测到登录完成，重置按钮状态");
      }

      // 这里可以添加其他登录状态的视觉反馈
      // 例如：显示绿色指示器、更新按钮文本等
    }
  }

  /**
   * 销毁组件
   */
  public destroy(): void {
    // 清理超时定时器
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }

    // 移除主容器
    this.container.remove();

    // 移除tooltip
    if (this.tooltip && this.tooltip.parentNode) {
      this.tooltip.remove();
    }

    // 销毁项目选择器
    if (this.projectSelector) {
      this.projectSelector.hide();
    }
  }

  /**
   * 创建容器
   */
  private createContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-quote-button-container";
    return container;
  }

  /**
   * 创建分割按钮容器
   */
  private createSplitButtonContainer(): HTMLDivElement {
    const container = document.createElement("div");
    container.className = "ext-split-button-container";
    container.style.cssText = `
      display: flex;
      border-radius: 10px;
      overflow: hidden;
      border: 1px solid rgba(0,0,0,0.1);
      backdrop-filter: blur(60px);
      -webkit-backdrop-filter: blur(60px);
      box-shadow: 0 4px 20px rgba(0,0,0,0.15);
      transition: all 0.3s ease, box-shadow 0.3s ease;
    `;
    return container;
  }

  /**
   * 创建左侧主按钮
   */
  private createLeftButton(): HTMLButtonElement {
    const button = document.createElement("button");
    button.className = "ext-quote-left-button";
    button.type = "button";
    button.style.cssText = `
      flex: 3;
      padding: 12px 16px;
      border: none;
      background: transparent;
      cursor: pointer;
      font-family: "Source Han Sans SC", "Source Han Sans CN", "Noto Sans CJK SC", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, sans-serif;
      font-size: 14px;
      font-weight: 700;
      transition: background-color 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      white-space: nowrap;
      user-select: none;
    `;

    button.addEventListener("click", async () => {
      if (
        this.state === ButtonState.PROCESSING ||
        this.state === ButtonState.SUCCESS
      )
        return;
      console.log("[QuoteButton] 左侧按钮被点击，检查认证状态");
      await this.handleLeftButtonClick();
    });

    // 悬停效果
    button.addEventListener("mouseenter", () => {
      // 只有在非禁用状态时才应用悬停效果
      if (!this.leftButton.disabled) {
        button.style.backgroundColor = "#f3f4f6"; // 浅灰色
      }
    });
    button.addEventListener("mouseleave", () => {
      button.style.backgroundColor = "transparent";
    });

    return button;
  }

  /**
   * 创建右侧次级按钮
   */
  private createRightButton(): HTMLButtonElement {
    const button = document.createElement("button");
    button.className = "ext-quote-right-button";
    button.type = "button";
    button.style.cssText = `
      flex: 1;
      padding: 12px 8px;
      border: none;
      border-left: 1px solid rgba(0,0,0,0.1);
      background: transparent;
      cursor: pointer;
      transition: background-color 0.2s ease;
      display: flex;
      align-items: center;
      justify-content: center;
      user-select: none;
    `;

    // 闪电图标
    button.innerHTML = Icons.lightning;

    button.addEventListener("click", async () => {
      if (
        this.state === ButtonState.PROCESSING ||
        this.state === ButtonState.SUCCESS
      )
        return;
      console.log("[QuoteButton] 右侧按钮被点击，检查认证状态");
      await this.handleRightButtonClick();
    });

    // 悬停效果和tooltip
    button.addEventListener("mouseenter", () => {
      // 只有在非禁用状态时才应用悬停效果
      if (!this.rightButton.disabled) {
        button.style.backgroundColor = "#f3f4f6"; // 浅灰色
      }
      // 只有在非等待登录状态且项目选择器未显示且成功提示未显示且非错误状态时才显示tooltip
      if (
        !this.isWaitingForLogin &&
        !this.projectSelector.getVisibility() &&
        !this.successToast.getVisibility() &&
        this.state !== ButtonState.ERROR
      ) {
        this.showTooltip();
      }
    });
    button.addEventListener("mouseleave", () => {
      button.style.backgroundColor = "transparent";
      this.hideTooltip();
    });

    return button;
  }

  /**
   * 创建tooltip
   */
  private createTooltip(): HTMLDivElement {
    const tooltip = document.createElement("div");
    tooltip.className = "ext-quote-tooltip";
    tooltip.innerHTML = `
      <div class="tooltip-content">采集到知识库</div>
      <div class="tooltip-arrow"></div>
    `;
    tooltip.style.cssText = `
      position: fixed;
      padding: 0;
      background: transparent;
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
      transition: opacity 0.2s ease, visibility 0.2s ease;
      z-index: 10001;
    `;

    // 添加内部样式
    this.ensureTooltipStyles();

    return tooltip;
  }

  /**
   * 处理左侧按钮点击（选择项目采集）
   */
  private async handleLeftButtonClick(): Promise<void> {
    try {
      // 确保tooltip隐藏
      this.hideTooltip();

      // 1. 检查认证状态
      const authStatus = await AuthService.checkAuthenticationStatus();

      if (!authStatus.isAuthenticated) {
        console.log("[QuoteButton] 用户未登录，显示登录提示浮窗");

        // 设置按钮为等待登录状态
        this.setWaitingForLogin(true);

        const userChoice = await AuthService.showLoginPrompt();

        // 清除等待登录状态
        this.setWaitingForLogin(false);

        if (userChoice === "cancel") {
          console.log("[QuoteButton] 用户取消登录，停止采集流程");
          return;
        } else {
          console.log("[QuoteButton] 用户选择登录，登录页面已打开");
          return;
        }
      }

      // 2. 已登录，显示项目选择器
      console.log("[QuoteButton] 用户已登录，显示项目选择器");
      await this.showProjectSelector();
    } catch (error) {
      console.error("[QuoteButton] 处理左侧按钮点击失败:", error);
    }
  }

  /**
   * 处理右侧按钮点击（直接采集到收件箱）
   */
  private async handleRightButtonClick(): Promise<void> {
    try {
      // 确保tooltip隐藏
      this.hideTooltip();

      // 如果项目选择器正在显示，先关闭它
      if (this.projectSelector.getVisibility()) {
        console.log("[QuoteButton] 项目选择器正在显示，关闭并执行直接采集");
        this.projectSelector.hide();
      }

      // 1. 检查认证状态
      const authStatus = await AuthService.checkAuthenticationStatus();

      if (!authStatus.isAuthenticated) {
        console.log("[QuoteButton] 用户未登录，显示登录提示浮窗");

        // 设置按钮为等待登录状态
        this.setWaitingForLogin(true);

        const userChoice = await AuthService.showLoginPrompt();

        // 清除等待登录状态
        this.setWaitingForLogin(false);

        if (userChoice === "cancel") {
          console.log("[QuoteButton] 用户取消登录，停止采集流程");
          return;
        } else {
          console.log("[QuoteButton] 用户选择登录，登录页面已打开");
          return;
        }
      }

      // 2. 已登录，直接采集到收件箱
      console.log("[QuoteButton] 用户已登录，直接采集到收件箱");
      await this.handleDirectCollect();
    } catch (error) {
      console.error("[QuoteButton] 处理右侧按钮点击失败:", error);
    }
  }

  /**
   * 显示项目选择器
   */
  private async showProjectSelector(): Promise<void> {
    try {
      await this.projectSelector.show();
    } catch (error) {
      console.error("[QuoteButton] 显示项目选择器失败:", error);
    }
  }

  /**
   * 处理项目选择
   */
  private async handleProjectSelect(projectId: string): Promise<void> {
    console.log("[QuoteButton] 选择项目:", projectId);

    this.setState(ButtonState.PROCESSING);
    this.startTimeout();

    try {
      await this.config.onCollectToProject(projectId);
      this.clearTimeout();
      this.setState(ButtonState.SUCCESS);

      // 显示成功提示 - 获取项目名称并应用省略逻辑
      const project = this.projectSelector.getProjectById(projectId);
      const projectName = project?.name || "未知项目";

      // 计算前缀宽度，为项目名称预留空间
      const prefix = "已采集到 ";
      const prefixWidth = TextUtils.getVisualWidth(prefix, 1.8);
      const totalWidth = 28; // 气泡总宽度
      const minProjectNameWidth = 14; // 确保项目名称至少显示14个单位
      const availableWidth = Math.max(
        totalWidth - prefixWidth,
        minProjectNameWidth
      );

      // 优化比例分配：开头70%，结尾30%
      const startRatio = 0.7;
      const endRatio = 0.3;

      const displayName = TextUtils.applyMiddleEllipsis(
        projectName,
        availableWidth,
        Math.floor(availableWidth * startRatio), // 开头占70%
        Math.floor(availableWidth * endRatio), // 结尾占30%
        1.8
      );

      this.successToast.show(`${prefix}${displayName}`);

      setTimeout(() => this.setState(ButtonState.IDLE), 3000);
    } catch (error) {
      this.clearTimeout();
      this.handleCollectError(error);
    }
  }

  /**
   * 处理直接采集到收件箱
   */
  private async handleDirectCollect(): Promise<void> {
    console.log("[QuoteButton] 直接采集到收件箱");

    this.setState(ButtonState.PROCESSING);
    this.startTimeout();

    try {
      await this.config.onCollectToInbox();
      this.clearTimeout();
      this.setState(ButtonState.SUCCESS);

      // 显示成功提示
      this.successToast.show("已采集到知识库");

      setTimeout(() => this.setState(ButtonState.IDLE), 3000);
    } catch (error) {
      this.clearTimeout();
      this.handleCollectError(error);
    }
  }

  /**
   * 显示tooltip
   */
  private showTooltip(): void {
    this.positionTooltip();
    this.tooltip.style.opacity = "1";
    this.tooltip.style.visibility = "visible";
  }

  /**
   * 隐藏tooltip
   */
  private hideTooltip(): void {
    this.tooltip.style.opacity = "0";
    this.tooltip.style.visibility = "hidden";
  }

  /**
   * 定位tooltip到右侧按钮正上方
   */
  private positionTooltip(): void {
    const rightButtonRect = this.rightButton.getBoundingClientRect();

    // 计算右侧按钮的中心位置
    const rightButtonCenterX = rightButtonRect.left + rightButtonRect.width / 2;

    // 先临时显示tooltip以获取其尺寸
    const originalVisibility = this.tooltip.style.visibility;
    const originalOpacity = this.tooltip.style.opacity;

    this.tooltip.style.visibility = "hidden";
    this.tooltip.style.opacity = "1";

    const tooltipRect = this.tooltip.getBoundingClientRect();

    // tooltip居中对齐右侧按钮，增加箭头高度的间距
    const tooltipLeft = rightButtonCenterX - tooltipRect.width / 2;
    const tooltipTop = rightButtonRect.top - tooltipRect.height - 14; // 8px原间距 + 6px箭头高度

    this.tooltip.style.left = `${tooltipLeft}px`;
    this.tooltip.style.top = `${tooltipTop}px`;

    // 恢复原始状态
    this.tooltip.style.visibility = originalVisibility;
    this.tooltip.style.opacity = originalOpacity;
  }

  /**
   * 开始超时计时
   */
  private startTimeout(): void {
    this.timeoutId = window.setTimeout(() => {
      if (this.state === ButtonState.PROCESSING && !this.isWaitingForLogin) {
        console.warn("[QuoteButton] 采集超时，自动重置状态");
        this.setState(ButtonState.ERROR);
        setTimeout(() => this.setState(ButtonState.IDLE), 3000);
      }
    }, 15000);
  }

  /**
   * 清除超时计时
   */
  private clearTimeout(): void {
    if (this.timeoutId) {
      clearTimeout(this.timeoutId);
      this.timeoutId = null;
    }
  }

  /**
   * 处理采集错误
   */
  private handleCollectError(error: any): void {
    // 检查是否为用户中断操作
    if (
      error instanceof Error &&
      (error.message === "USER_CANCELLED" ||
        error.message === "USER_LOGIN_REDIRECT")
    ) {
      // 用户中断操作，直接重置为idle状态，不显示错误
      this.setState(ButtonState.IDLE);
      return;
    }

    // 根据错误信息判断错误类型
    this.currentErrorType = this.determineErrorType(error);

    this.setState(ButtonState.ERROR);
    setTimeout(() => {
      this.setState(ButtonState.IDLE);
      this.currentErrorType = null;
    }, 3000);
    throw error;
  }

  /**
   * 根据错误信息判断错误类型
   */
  private determineErrorType(error: any): ErrorType {
    const errorMessage = error instanceof Error ? error.message : String(error);

    // 如果是ExtractionResult格式，直接获取errorType
    if (error.errorType) {
      return error.errorType;
    }

    // 根据错误消息内容判断错误类型
    if (errorMessage.includes("未找到内容容器")) {
      return ErrorType.CONTENT_CONTAINER_NOT_FOUND;
    } else if (errorMessage.includes("内容容器为空")) {
      return ErrorType.CONTENT_EMPTY;
    } else if (
      errorMessage.includes("未找到站点") &&
      errorMessage.includes("提取配置")
    ) {
      return ErrorType.SITE_CONFIG_NOT_FOUND;
    } else if (
      errorMessage.includes("网络") ||
      errorMessage.includes("timeout") ||
      errorMessage.includes("fetch")
    ) {
      return ErrorType.NETWORK_ERROR;
    } else if (
      errorMessage.includes("认证") ||
      errorMessage.includes("登录") ||
      errorMessage.includes("auth") ||
      errorMessage.includes("401")
    ) {
      return ErrorType.AUTH_ERROR;
    } else {
      return ErrorType.UNKNOWN_ERROR;
    }
  }

  /**
   * 设置浮动样式
   */
  private setupFloatingStyles(): void {
    this.container.style.cssText = `
      position: fixed;
      bottom: 30px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 10000;
      transition: all 0.3s ease;
    `;

    // 悬停效果
    this.container.addEventListener("mouseenter", () => {
      // 只有在非禁用状态时才应用悬停效果
      if (!this.leftButton.disabled && !this.rightButton.disabled) {
        this.container.style.transform = "translateX(-50%) translateY(-2px)";
        // 修改分割按钮容器的box-shadow来实现悬停效果
        this.splitButtonContainer.style.boxShadow =
          "0 6px 24px rgba(0,0,0,0.25)";
      }
    });

    this.container.addEventListener("mouseleave", () => {
      this.container.style.transform = "translateX(-50%)";
      // 恢复原始的box-shadow
      this.splitButtonContainer.style.boxShadow = "0 4px 20px rgba(0,0,0,0.15)";
    });
  }

  /**
   * 更新按钮外观
   */
  private updateButtonAppearance(): void {
    const { icon, text, bgColor, textColor } = this.getStateConfig();

    // 判断是否为SVG图标
    const isSvgIcon = icon.includes("<svg");

    // 更新左侧按钮内容
    this.leftButton.innerHTML = `
      <span style="display: inline-flex; align-items: center; margin-right: 6px; ${isSvgIcon ? "" : "font-size: 16px;"}">${icon}</span>
      <span>${text}</span>
    `;

    // 更新分割按钮容器样式
    this.splitButtonContainer.style.background = bgColor;
    this.splitButtonContainer.style.color = textColor;

    // 更新按钮状态 - 在PROCESSING、SUCCESS和ERROR状态时禁用
    const isDisabled =
      this.state === ButtonState.PROCESSING ||
      this.state === ButtonState.SUCCESS ||
      this.state === ButtonState.ERROR;
    this.leftButton.disabled = isDisabled;
    this.rightButton.disabled = isDisabled;

    // 更新光标样式
    const cursor = isDisabled ? "not-allowed" : "pointer";
    this.leftButton.style.cursor = cursor;
    this.rightButton.style.cursor = cursor;

    // 添加CSS动画
    this.ensureAnimationStyles();
  }

  /**
   * 获取状态配置
   */
  private getStateConfig() {
    const quoteIcon = Icons.quote;

    switch (this.state) {
      case ButtonState.IDLE:
        return {
          icon: quoteIcon,
          text: '采集到 <svg width="45" height="11" viewBox="0 0 50 13">g><g><path d="M48.65781782836914,11.158756984558106C47.344277828369144,11.158756984558106,46.27879782836914,10.128146984558105,46.27879782836914,8.856406984558106L49.99999782836914,8.856296984558107L49.99999782836914,3.7916669845581055L42.47311782836914,3.7916669845581055L42.47311782836914,9.383086984558105C42.47311782836914,11.380846984558104,44.14619782836914,12.999996984558106,46.21049782836914,12.999996984558106L48.89604782836914,12.999996984558106L48.89604782836914,11.158226984558105L48.65836782836914,11.158226984558105L48.65781782836914,11.158756984558106Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M31.073647655029298,4.245128136627197C30.359887655029297,3.8683171366271973,29.591857655029298,3.6799111366271973,28.770097655029296,3.6799111366271973C27.948327655029296,3.6799111366271973,27.177177655029297,3.8683171366271973,26.457677655029297,4.245128136627197C25.738177655029297,4.621940136627197,25.164767655029298,5.164711136627197,24.736407655029296,5.872931136627198C24.3080476550293,6.581151136627197,24.094127655029297,7.403661136627197,24.094127655029297,8.339951136627198C24.094127655029297,9.276241136627197,24.3080476550293,10.113371136627197,24.736407655029296,10.815321136627198C25.164767655029298,11.517801136627197,25.735567655029296,12.057441136627197,26.449327655029297,12.434261136627198C27.162567655029296,12.811071136627197,27.936327655029295,12.999471136627196,28.770097655029296,12.999471136627196C29.603857655029298,12.999471136627196,30.373967655029297,12.811071136627197,31.082517655029296,12.434261136627198C31.790537655029297,12.057441136627197,32.3555976550293,11.517801136627197,32.778217655029295,10.815321136627198C33.200317655029295,10.112851136627198,33.4116276550293,9.287721136627198,33.4116276550293,8.339951136627198C33.4116276550293,7.3921811366271974,33.200317655029295,6.581151136627197,32.778217655029295,5.872931136627198C32.3555976550293,5.164721136627197,31.7874076550293,4.622463136627197,31.074167655029296,4.245129136627197L31.073647655029298,4.245128136627197ZM30.011877655029295,9.993331136627198C29.674817655029297,10.398851136627197,29.255327655029298,10.601341136627198,28.752877655029298,10.601341136627198C28.2504276550293,10.601341136627198,27.813717655029297,10.398851136627197,27.476667655029296,9.993331136627198C27.139607655029298,9.587811136627197,26.971607655029295,9.036691136627198,26.971607655029295,8.339951136627198C26.971607655029295,7.643221136627197,27.140137655029296,7.089481136627198,27.476667655029296,6.678221136627197C27.813197655029295,6.266971136627197,28.238947655029296,6.061341136627197,28.752877655029298,6.061341136627197C29.266807655029297,6.061341136627197,29.674817655029297,6.266971136627197,30.011877655029295,6.678221136627197C30.3484076550293,7.089481136627198,30.516937655029295,7.643741136627197,30.516937655029295,8.339951136627198C30.516937655029295,9.036171136627196,30.3484076550293,9.587811136627197,30.011877655029295,9.993331136627198Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M39.45406394042969,10.601350559196472C38.974573940429686,10.601350559196472,38.64899394042969,10.444260559196472,38.477863940429685,10.130080559196472C38.30672394042969,9.815890559196472,38.22115394042969,9.373850559196471,38.22115394042969,8.802370559196472L38.22109394042969,6.661580559196472L40.92817394042969,6.661580559196472L40.92817394042969,3.7924405591964723L38.22109394042969,3.7924405591964723L38.22115394042969,1.0756405591964722L35.429243940429686,1.0756405591964722L35.43001394042969,3.7919105591964724L33.80241394042969,3.7919105591964724L33.80241394042969,6.661050559196473L35.43001394042969,6.661050559196473L35.429243940429686,9.298690559196473C35.429243940429686,10.680680559196473,35.74334394042969,11.643040559196471,36.371533940429686,12.185340559196472C36.99920394042969,12.728140559196472,37.83297394042969,12.998940559196472,38.87230394042969,12.998940559196472C40.03686394042969,12.998940559196472,40.93897394042969,12.713440559196473,41.57812394042969,12.142540559196473L40.601923940429685,10.206800559196472C40.22521394042969,10.469840559196472,39.84224394042969,10.600830559196472,39.454583940429686,10.600830559196472L39.45406394042969,10.601350559196472Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M22.83565828033447,3.890752236862082L19.177638280334474,3.890752236862082L19.177638280334474,6.2774018386840815L20.043748280334473,6.2774018386840815L20.043748280334473,8.082651838684082C20.043748280334473,8.859761838684083,19.900788280334474,9.46464183868408,19.61538828033447,9.898861838684082C19.329988280334472,10.333081838684082,18.924588280334472,10.549671838684082,18.399698280334473,10.549671838684082C17.874818280334473,10.549671838684082,17.494458280334474,10.335691838684081,17.295148280334473,9.907211838684082C17.09530828033447,9.478731838684082,16.995138280334473,8.910901838684083,16.995138280334473,8.202691838684082L16.995138280334473,3.890751838684082L13.363198280334473,3.890751838684082L13.363198280334473,6.317581838684082L14.203224280334473,6.317581838684082L14.203224280334473,9.145241838684083C14.203224280334473,10.41293183868408,14.491228280334473,11.372711838684083,15.068288280334473,12.023511838684081C15.644828280334472,12.674321838684081,16.435808280334474,12.999991838684082,17.440708280334473,12.999991838684082C18.708058280334473,12.999991838684082,19.63312828033447,12.468691838684082,20.215408280334472,11.406631838684081C20.249838280334473,11.555371838684081,20.289498280334474,11.886251838684082,20.335408280334473,12.400331838684082L20.369848280334473,12.777141838684082L23.67199828033447,12.777141838684082L23.67199828033447,10.551241838684081L22.836178280334472,10.551241838684081L22.836178280334472,3.890752635040082L22.83565828033447,3.890752236862082Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M11.4379,9.96829C11.9858,8.98659,12.2597,7.82745,12.2597,6.49139C12.2597,5.15533,11.9858,4.01393,11.4379,3.03172C10.8901,2.05002,10.1507,1.29901,9.22045,0.779196C8.29016,0.259906,7.26022,0,6.1301,0C4.99998,0,3.9669,0.259906,3.03087,0.779196C2.09485,1.29901,1.35552,2.0495,0.813416,3.03172C0.270791,4.01393,0,5.16681,0,6.49139C0,7.81597,0.270791,8.98659,0.813416,9.96829C1.35552,10.9505,2.09172,11.701,3.022,12.2208C3.95229,12.7406,4.9885,13,6.1301,13L12.8399,13L12.8399,10.5998L11.0367,10.5998C11.1802,10.4009,11.3143,10.1906,11.4384,9.96828L11.4379,9.96829ZM4.55492,9.85712C4.09839,9.54294,3.7415,9.09515,3.4848,8.51271C3.2281,7.93027,3.09975,7.25702,3.09975,6.49139C3.09975,5.72576,3.2281,5.07287,3.4848,4.49617C3.74203,3.91947,4.09839,3.47429,4.55492,3.1601C5.01146,2.84592,5.53634,2.68883,6.1301,2.68883C6.72386,2.68883,7.25135,2.84592,7.71415,3.1601C8.17642,3.47429,8.53591,3.91947,8.79261,4.49617C9.04932,5.07287,9.17767,5.73777,9.17767,6.49139C9.17767,7.24501,9.04932,7.93027,8.79261,8.51271C8.53591,9.09515,8.17642,9.54346,7.71415,9.85712C7.25187,10.1713,6.72386,10.3284,6.1301,10.3284C5.53634,10.3284,5.01093,10.1718,4.55492,9.85712Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>',
          bgColor: "#ffffff90",
          textColor: "#5D5D5D",
        };
      case ButtonState.PROCESSING:
        return {
          icon: Icons.loading,
          text: "正在采集...",
          bgColor: "#ffffff90",
          textColor: "#5D5D5D",
        };
      case ButtonState.SUCCESS:
        return {
          icon: Icons.success,
          text: "采集成功",
          bgColor: "#ffffff90",
          textColor: "#40AD31",
        };
      case ButtonState.ERROR:
        return {
          icon: Icons.error,
          text: this.getErrorMessage(),
          bgColor: "#ffffff90",
          textColor: "#FF4355",
        };
      default:
        // 默认返回空闲状态配置
        return {
          icon: quoteIcon,
          text: '采集到 <svg width="45" height="11" viewBox="0 0 50 13">g><g><path d="M48.65781782836914,11.158756984558106C47.344277828369144,11.158756984558106,46.27879782836914,10.128146984558105,46.27879782836914,8.856406984558106L49.99999782836914,8.856296984558107L49.99999782836914,3.7916669845581055L42.47311782836914,3.7916669845581055L42.47311782836914,9.383086984558105C42.47311782836914,11.380846984558104,44.14619782836914,12.999996984558106,46.21049782836914,12.999996984558106L48.89604782836914,12.999996984558106L48.89604782836914,11.158226984558105L48.65836782836914,11.158226984558105L48.65781782836914,11.158756984558106Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M31.073647655029298,4.245128136627197C30.359887655029297,3.8683171366271973,29.591857655029298,3.6799111366271973,28.770097655029296,3.6799111366271973C27.948327655029296,3.6799111366271973,27.177177655029297,3.8683171366271973,26.457677655029297,4.245128136627197C25.738177655029297,4.621940136627197,25.164767655029298,5.164711136627197,24.736407655029296,5.872931136627198C24.3080476550293,6.581151136627197,24.094127655029297,7.403661136627197,24.094127655029297,8.339951136627198C24.094127655029297,9.276241136627197,24.3080476550293,10.113371136627197,24.736407655029296,10.815321136627198C25.164767655029298,11.517801136627197,25.735567655029296,12.057441136627197,26.449327655029297,12.434261136627198C27.162567655029296,12.811071136627197,27.936327655029295,12.999471136627196,28.770097655029296,12.999471136627196C29.603857655029298,12.999471136627196,30.373967655029297,12.811071136627197,31.082517655029296,12.434261136627198C31.790537655029297,12.057441136627197,32.3555976550293,11.517801136627197,32.778217655029295,10.815321136627198C33.200317655029295,10.112851136627198,33.4116276550293,9.287721136627198,33.4116276550293,8.339951136627198C33.4116276550293,7.3921811366271974,33.200317655029295,6.581151136627197,32.778217655029295,5.872931136627198C32.3555976550293,5.164721136627197,31.7874076550293,4.622463136627197,31.074167655029296,4.245129136627197L31.073647655029298,4.245128136627197ZM30.011877655029295,9.993331136627198C29.674817655029297,10.398851136627197,29.255327655029298,10.601341136627198,28.752877655029298,10.601341136627198C28.2504276550293,10.601341136627198,27.813717655029297,10.398851136627197,27.476667655029296,9.993331136627198C27.139607655029298,9.587811136627197,26.971607655029295,9.036691136627198,26.971607655029295,8.339951136627198C26.971607655029295,7.643221136627197,27.140137655029296,7.089481136627198,27.476667655029296,6.678221136627197C27.813197655029295,6.266971136627197,28.238947655029296,6.061341136627197,28.752877655029298,6.061341136627197C29.266807655029297,6.061341136627197,29.674817655029297,6.266971136627197,30.011877655029295,6.678221136627197C30.3484076550293,7.089481136627198,30.516937655029295,7.643741136627197,30.516937655029295,8.339951136627198C30.516937655029295,9.036171136627196,30.3484076550293,9.587811136627197,30.011877655029295,9.993331136627198Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M39.45406394042969,10.601350559196472C38.974573940429686,10.601350559196472,38.64899394042969,10.444260559196472,38.477863940429685,10.130080559196472C38.30672394042969,9.815890559196472,38.22115394042969,9.373850559196471,38.22115394042969,8.802370559196472L38.22109394042969,6.661580559196472L40.92817394042969,6.661580559196472L40.92817394042969,3.7924405591964723L38.22109394042969,3.7924405591964723L38.22115394042969,1.0756405591964722L35.429243940429686,1.0756405591964722L35.43001394042969,3.7919105591964724L33.80241394042969,3.7919105591964724L33.80241394042969,6.661050559196473L35.43001394042969,6.661050559196473L35.429243940429686,9.298690559196473C35.429243940429686,10.680680559196473,35.74334394042969,11.643040559196471,36.371533940429686,12.185340559196472C36.99920394042969,12.728140559196472,37.83297394042969,12.998940559196472,38.87230394042969,12.998940559196472C40.03686394042969,12.998940559196472,40.93897394042969,12.713440559196473,41.57812394042969,12.142540559196473L40.601923940429685,10.206800559196472C40.22521394042969,10.469840559196472,39.84224394042969,10.600830559196472,39.454583940429686,10.600830559196472L39.45406394042969,10.601350559196472Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M22.83565828033447,3.890752236862082L19.177638280334474,3.890752236862082L19.177638280334474,6.2774018386840815L20.043748280334473,6.2774018386840815L20.043748280334473,8.082651838684082C20.043748280334473,8.859761838684083,19.900788280334474,9.46464183868408,19.61538828033447,9.898861838684082C19.329988280334472,10.333081838684082,18.924588280334472,10.549671838684082,18.399698280334473,10.549671838684082C17.874818280334473,10.549671838684082,17.494458280334474,10.335691838684081,17.295148280334473,9.907211838684082C17.09530828033447,9.478731838684082,16.995138280334473,8.910901838684083,16.995138280334473,8.202691838684082L16.995138280334473,3.890751838684082L13.363198280334473,3.890751838684082L13.363198280334473,6.317581838684082L14.203224280334473,6.317581838684082L14.203224280334473,9.145241838684083C14.203224280334473,10.41293183868408,14.491228280334473,11.372711838684083,15.068288280334473,12.023511838684081C15.644828280334472,12.674321838684081,16.435808280334474,12.999991838684082,17.440708280334473,12.999991838684082C18.708058280334473,12.999991838684082,19.63312828033447,12.468691838684082,20.215408280334472,11.406631838684081C20.249838280334473,11.555371838684081,20.289498280334474,11.886251838684082,20.335408280334473,12.400331838684082L20.369848280334473,12.777141838684082L23.67199828033447,12.777141838684082L23.67199828033447,10.551241838684081L22.836178280334472,10.551241838684081L22.836178280334472,3.890752635040082L22.83565828033447,3.890752236862082Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g><g><path d="M11.4379,9.96829C11.9858,8.98659,12.2597,7.82745,12.2597,6.49139C12.2597,5.15533,11.9858,4.01393,11.4379,3.03172C10.8901,2.05002,10.1507,1.29901,9.22045,0.779196C8.29016,0.259906,7.26022,0,6.1301,0C4.99998,0,3.9669,0.259906,3.03087,0.779196C2.09485,1.29901,1.35552,2.0495,0.813416,3.03172C0.270791,4.01393,0,5.16681,0,6.49139C0,7.81597,0.270791,8.98659,0.813416,9.96829C1.35552,10.9505,2.09172,11.701,3.022,12.2208C3.95229,12.7406,4.9885,13,6.1301,13L12.8399,13L12.8399,10.5998L11.0367,10.5998C11.1802,10.4009,11.3143,10.1906,11.4384,9.96828L11.4379,9.96829ZM4.55492,9.85712C4.09839,9.54294,3.7415,9.09515,3.4848,8.51271C3.2281,7.93027,3.09975,7.25702,3.09975,6.49139C3.09975,5.72576,3.2281,5.07287,3.4848,4.49617C3.74203,3.91947,4.09839,3.47429,4.55492,3.1601C5.01146,2.84592,5.53634,2.68883,6.1301,2.68883C6.72386,2.68883,7.25135,2.84592,7.71415,3.1601C8.17642,3.47429,8.53591,3.91947,8.79261,4.49617C9.04932,5.07287,9.17767,5.73777,9.17767,6.49139C9.17767,7.24501,9.04932,7.93027,8.79261,8.51271C8.53591,9.09515,8.17642,9.54346,7.71415,9.85712C7.25187,10.1713,6.72386,10.3284,6.1301,10.3284C5.53634,10.3284,5.01093,10.1718,4.55492,9.85712Z" fill="#5D5D5D" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></g></svg>',
          bgColor: "#ffffff90",
          textColor: "#5D5D5D",
        };
    }
  }

  /**
   * 获取错误消息
   */
  private getErrorMessage(): string {
    switch (this.currentErrorType) {
      case ErrorType.CONTENT_CONTAINER_NOT_FOUND:
        return "页面内容加载失败，请刷新页面后重试";
      case ErrorType.CONTENT_EMPTY:
        return "页面内容为空，请检查页面完整性";
      case ErrorType.SITE_CONFIG_NOT_FOUND:
        return "不支持该网站，请确认在支持的页面使用";
      case ErrorType.NETWORK_ERROR:
        return "网络连接失败，请检查网络后重试";
      case ErrorType.AUTH_ERROR:
        return "登录已过期，请重新登录";
      case ErrorType.UNKNOWN_ERROR:
      default:
        return "采集失败，请刷新页面重试";
    }
  }

  /**
   * 确保动画样式存在
   */
  private ensureAnimationStyles(): void {
    if (!document.querySelector("#ext-quote-animations")) {
      const style = document.createElement("style");
      style.id = "ext-quote-animations";
      style.textContent = `
        @keyframes spin {
          from { transform: rotate(0deg); }
          to { transform: rotate(360deg); }
        }
      `;
      document.head.appendChild(style);
    }
  }

  /**
   * 确保tooltip样式已添加
   */
  private ensureTooltipStyles(): void {
    const styleId = "ext-quote-tooltip-styles";
    if (document.getElementById(styleId)) {
      return; // 样式已存在
    }

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      .ext-quote-tooltip .tooltip-content {
        padding: 6px 10px;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        font-size: 12px;
        border-radius: 6px;
        white-space: nowrap;
        font-family: -apple-system, BlinkMacSystemFont, sans-serif;
      }
      .ext-quote-tooltip .tooltip-arrow {
        position: absolute;
        top: 100%;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 6px solid transparent;
        border-right: 6px solid transparent;
        border-top: 6px solid rgba(0, 0, 0, 0.8);
      }
    `;
    document.head.appendChild(style);
  }
}

/**
 * 创建分割Quote按钮
 */
export function createQuoteButton(config: SplitButtonConfig): QuoteButton {
  return new QuoteButton(config);
}
