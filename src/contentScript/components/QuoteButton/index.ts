export { QuoteButton, createQuote<PERSON><PERSON>on } from "./QuoteButton";
export { ProjectSelector } from "./ProjectSelector";
export type { ProjectSelectorConfig } from "./ProjectSelector";
export { ButtonState } from "./types";
export type {
  Project,
  ProjectOption,
  ButtonConfig,
  ButtonStateData,
  ButtonCallbacks,
} from "./types";

// 导出通用注入器工厂
export {
  createSiteInjector,
  SITE_CONFIGS,
  type SiteConfig,
  type SiteInjector,
} from "./SiteInjectorFactory";
