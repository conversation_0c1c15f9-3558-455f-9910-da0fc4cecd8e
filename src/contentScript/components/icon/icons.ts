/**
 * 图标管理器
 * 统一管理项目中使用的所有SVG图标
 */

export const Icons = {
  /**
   * 闪电图标 - 用于右侧快速采集按钮
   */
  lightning: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16"><path fill="currentColor" fill-rule="evenodd" d="M8.663 1.027A.5.5 0 0 1 9 1.5V7h3a.5.5 0 0 1 .393.809l-5.5 7A.5.5 0 0 1 6 14.5V9H3a.5.5 0 0 1-.393-.809l5.5-7a.5.5 0 0 1 .556-.164" clip-rule="evenodd"/></svg>`,

  /**
   * Quote图标 - 用于主采集按钮
   */
  quote: `<svg width="14" height="17" viewBox="0 0 14 17"><g><path d="M11.5035,13.6008C9.06035,13.6008,7.07856,11.6981,7.07856,9.35029L14,9.35009L14,0L0,0L0,10.3226C0,14.0108,3.11192,17,6.95152,17L11.9467,17L11.9467,13.5998L11.5046,13.5998L11.5035,13.6008Z" fill="#4034BA" fill-opacity="1" style="mix-blend-mode:passthrough"/></g></svg>`,

  /**
   * 加载中旋转图标
   */
  loading: `<svg width="16" height="16" viewBox="0 0 16 16"><circle cx="8" cy="8" r="6" fill="none" stroke="#4034BA" stroke-width="2" stroke-linecap="round" stroke-dasharray="28" stroke-dashoffset="28"><animateTransform attributeName="transform" type="rotate" values="0 8 8;360 8 8" dur="1s" repeatCount="indefinite"/></circle></svg>`,

  /**
   * 成功图标
   */
  success: `<svg width="16" height="16" viewBox="0 0 16 16"><circle cx="8" cy="8" r="7" fill="#4BB83C" opacity="0"><animate attributeName="opacity" values="0;1" dur="0.3s" fill="freeze"/></circle><path d="M5 8l2 2 4-4" fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" stroke-dasharray="8" stroke-dashoffset="8"><animate attributeName="stroke-dashoffset" values="8;0" dur="0.5s" begin="0.2s" fill="freeze"/></path></svg>`,

  /**
   * 错误图标
   */
  error: `<svg width="16" height="16" viewBox="0 0 16 16"><circle cx="8" cy="8" r="7" fill="#FF4355" opacity="0"><animate attributeName="opacity" values="0;1" dur="0.3s" fill="freeze"/></circle><path d="M5 5l6 6M11 5l-6 6" fill="none" stroke="#ffffff" stroke-width="2" stroke-linecap="round" stroke-dasharray="8" stroke-dashoffset="8"><animate attributeName="stroke-dashoffset" values="8;0" dur="0.5s" begin="0.2s" fill="freeze"/></path></svg>`,

  /**
   * 文件夹图标 - 用于项目列表
   */
  folder: `<svg width="16" height="16" viewBox="0 0 16 16" fill="none"><path d="M2 3C2 2.44772 2.44772 2 3 2H6.58579C6.851 2 7.10536 2.10536 7.29289 2.29289L8.70711 3.70711C8.89464 3.89464 9.149 4 9.41421 4H13C13.5523 4 14 4.44772 14 5V12C14 12.5523 13.5523 13 13 13H3C2.44772 13 2 12.5523 2 12V3Z" fill="currentColor"/></svg>`,

  /**
   * 文件夹减号图标 - 用于项目列表
   */
  folderMinus: `<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M9 13h6"/><path d="M20 20a2 2 0 0 0 2-2V8a2 2 0 0 0-2-2h-7.9a2 2 0 0 1-1.69-.9L9.6 3.9A2 2 0 0 0 7.93 3H4a2 2 0 0 0-2 2v13a2 2 0 0 0 2 2Z"/></svg>`,
} as const;

/**
 * 获取图标HTML字符串
 * @param iconName 图标名称
 * @param className 可选的CSS类名
 * @returns HTML字符串
 */
export function getIcon(
  iconName: keyof typeof Icons,
  className?: string
): string {
  const icon = Icons[iconName];
  if (!icon) {
    console.warn(`[Icons] 未找到图标: ${iconName}`);
    return "";
  }

  if (className) {
    // 为SVG添加class属性
    return icon.replace("<svg", `<svg class="${className}"`);
  }

  return icon;
}

/**
 * 创建图标元素
 * @param iconName 图标名称
 * @param className 可选的CSS类名
 * @returns HTMLElement
 */
export function createIconElement(
  iconName: keyof typeof Icons,
  className?: string
): HTMLElement {
  const container = document.createElement("span");
  container.innerHTML = getIcon(iconName, className);
  return container.firstElementChild as HTMLElement;
}
