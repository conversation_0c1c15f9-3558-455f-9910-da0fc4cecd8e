/**
 * 高亮采集系统核心管理器
 */

import type { HighlightItem, ToolbarPosition } from "./types";
import { SelectionToolbarManager } from "./components/SelectionToolbar";
import { NotificationBarManager } from "./components/NotificationBar";

export class HighlightManager {
  private highlights: HighlightItem[] = [];
  private toolbarManager: SelectionToolbarManager;
  private notificationManager: NotificationBarManager;
  private isInitialized = false;
  private currentSelection: Selection | null = null;
  private hoveredHighlight: HTMLElement | null = null;

  constructor() {
    this.toolbarManager = new SelectionToolbarManager(
      this.handleAddHighlight.bind(this),
      this.handleRemoveHighlight.bind(this)
    );

    this.notificationManager = new NotificationBarManager(
      this.handleClearAll.bind(this)
    );
  }

  /**
   * 初始化高亮管理器
   */
  init() {
    if (this.isInitialized) return;

    console.log("[HighlightManager] 初始化高亮采集系统");

    // 注入样式
    this.injectStyles();

    // 绑定事件监听器
    this.bindEventListeners();

    this.isInitialized = true;
  }

  /**
   * 注入样式
   */
  private injectStyles() {
    const styleId = "quote-highlight-styles";
    if (document.getElementById(styleId)) return;

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* 高亮文本样式 */
      .quote-highlight {
        background-color: #fef3c7 !important;
        border-radius: 2px !important;
        padding: 1px 2px !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight:hover {
        background-color: #fde68a !important;
      }

      /* 工具栏容器 */
      .quote-selection-toolbar {
        position: absolute !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        padding: 8px !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        pointer-events: auto !important;
      }

      /* 工具栏按钮 */
      .quote-toolbar-button {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 32px !important;
        height: 32px !important;
        border: none !important;
        border-radius: 6px !important;
        background: #f3f4f6 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        color: #374151 !important;
      }

      .quote-toolbar-button:hover {
        background: #e5e7eb !important;
        transform: scale(1.05) !important;
      }

      .quote-toolbar-button:active {
        transform: scale(0.95) !important;
      }

      /* Quote 图标装饰 */
      .quote-toolbar-icon {
        width: 16px !important;
        height: 16px !important;
        opacity: 0.6 !important;
      }

      /* 通知栏样式 */
      .quote-notification-bar {
        position: fixed !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 999998 !important;
        background: white !important;
        border: 1px solid #e5e7eb !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        padding: 12px 16px !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        color: #374151 !important;
        pointer-events: auto !important;
        animation: slideUp 0.3s ease-out !important;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      /* 数量显示 */
      .quote-count-display {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
      }

      .quote-count-badge {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        min-width: 24px !important;
        height: 24px !important;
        background: #3b82f6 !important;
        color: white !important;
        font-size: 12px !important;
        font-weight: 600 !important;
        border-radius: 12px !important;
      }

      .quote-count-badge.two-digit {
        border-radius: 8px !important;
        padding: 0 6px !important;
      }

      .quote-count-text {
        font-weight: 500 !important;
        color: #374151 !important;
      }

      /* 通知栏按钮 */
      .quote-notification-button {
        padding: 6px 12px !important;
        border: 1px solid #d1d5db !important;
        border-radius: 6px !important;
        background: white !important;
        color: #374151 !important;
        font-size: 12px !important;
        font-weight: 500 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
      }

      .quote-notification-button:hover {
        background: #f9fafb !important;
        border-color: #9ca3af !important;
      }

      .quote-notification-button.danger {
        color: #dc2626 !important;
        border-color: #fca5a5 !important;
      }

      .quote-notification-button.danger:hover {
        background: #fef2f2 !important;
        border-color: #f87171 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 绑定事件监听器
   */
  private bindEventListeners() {
    // 文本选择事件
    document.addEventListener(
      "selectionchange",
      this.handleSelectionChange.bind(this)
    );

    // 滚动和点击隐藏工具栏
    document.addEventListener("scroll", this.hideToolbar.bind(this), true);
    document.addEventListener("click", this.handleDocumentClick.bind(this));

    // 鼠标事件处理高亮文本hover
    document.addEventListener(
      "mouseenter",
      this.handleMouseEnter.bind(this),
      true
    );
    document.addEventListener(
      "mouseleave",
      this.handleMouseLeave.bind(this),
      true
    );
  }

  /**
   * 处理文本选择变化
   */
  private handleSelectionChange() {
    const selection = window.getSelection();
    if (!selection || selection.rangeCount === 0) {
      this.hideToolbar();
      return;
    }

    const range = selection.getRangeAt(0);
    const selectedText = range.toString().trim();

    // 忽略空选择或过短文本
    if (!selectedText || selectedText.length < 3) {
      this.hideToolbar();
      return;
    }

    // 检查是否选择了高亮文本
    if (this.isSelectionInHighlight(range)) {
      this.hideToolbar();
      return;
    }

    this.currentSelection = selection;
    this.showToolbarForSelection(range);
  }

  /**
   * 检查选择是否在高亮文本内
   */
  private isSelectionInHighlight(range: Range): boolean {
    const container = range.commonAncestorContainer;
    const element =
      container.nodeType === Node.TEXT_NODE
        ? container.parentElement
        : (container as Element);
    return element?.closest(".quote-highlight") !== null;
  }

  /**
   * 为选择显示工具栏
   */
  private showToolbarForSelection(range: Range) {
    const rect = range.getBoundingClientRect();
    const position: ToolbarPosition = {
      x: rect.right + window.scrollX + 5,
      y: rect.bottom + window.scrollY + 5,
    };

    this.toolbarManager.show(position, "add");
  }

  /**
   * 处理鼠标进入事件
   */
  private handleMouseEnter(event: Event) {
    const target = event.target as HTMLElement;
    if (target.classList.contains("quote-highlight")) {
      this.hoveredHighlight = target;
      this.showToolbarForHighlight(target);
    }
  }

  /**
   * 处理鼠标离开事件
   */
  private handleMouseLeave(event: Event) {
    const target = event.target as HTMLElement;
    if (target.classList.contains("quote-highlight")) {
      this.hoveredHighlight = null;
      // 延迟隐藏，避免鼠标移动到工具栏时立即隐藏
      setTimeout(() => {
        if (!this.hoveredHighlight) {
          this.hideToolbar();
        }
      }, 100);
    }
  }

  /**
   * 为高亮文本显示工具栏
   */
  private showToolbarForHighlight(element: HTMLElement) {
    const rect = element.getBoundingClientRect();
    const position: ToolbarPosition = {
      x: rect.right + window.scrollX + 5,
      y: rect.bottom + window.scrollY + 5,
    };

    const highlightId = element.getAttribute("data-highlight-id");
    this.toolbarManager.show(position, "remove", highlightId || undefined);
  }

  /**
   * 处理文档点击
   */
  private handleDocumentClick(event: Event) {
    const target = event.target as HTMLElement;

    // 如果点击的是工具栏或通知栏，不隐藏
    if (
      target.closest(".quote-selection-toolbar") ||
      target.closest(".quote-notification-bar")
    ) {
      return;
    }

    this.hideToolbar();
  }

  /**
   * 隐藏工具栏
   */
  private hideToolbar() {
    this.toolbarManager.hide();
    this.currentSelection = null;
  }

  /**
   * 添加高亮
   */
  private handleAddHighlight() {
    if (!this.currentSelection) return;

    const range = this.currentSelection.getRangeAt(0);
    const selectedText = range.toString().trim();

    if (!selectedText) return;

    // 创建高亮元素
    const highlightId = this.generateId();
    const highlightElement = this.createHighlightElement(
      highlightId,
      selectedText
    );

    try {
      // 替换选中内容
      range.deleteContents();
      range.insertNode(highlightElement);

      // 存储高亮信息
      const highlightItem: HighlightItem = {
        id: highlightId,
        text: selectedText,
        range: range.cloneRange(),
        element: highlightElement,
        timestamp: Date.now(),
        pageUrl: window.location.href,
      };

      this.highlights.push(highlightItem);

      // 更新UI
      this.updateNotificationCount();
      this.hideToolbar();

      // 清除选择
      this.currentSelection.removeAllRanges();

      console.log("[HighlightManager] 添加高亮:", selectedText);
    } catch (error) {
      console.error("[HighlightManager] 添加高亮失败:", error);
    }
  }

  /**
   * 创建高亮元素
   */
  private createHighlightElement(id: string, text: string): HTMLElement {
    const span = document.createElement("span");
    span.className = "quote-highlight";
    span.setAttribute("data-highlight-id", id);
    span.textContent = text;
    return span;
  }

  /**
   * 移除高亮
   */
  private handleRemoveHighlight() {
    if (!this.hoveredHighlight) return;

    const highlightId = this.hoveredHighlight.getAttribute("data-highlight-id");
    if (!highlightId) return;

    this.removeHighlightById(highlightId);
  }

  /**
   * 根据ID移除高亮
   */
  private removeHighlightById(id: string) {
    const index = this.highlights.findIndex((h) => h.id === id);
    if (index === -1) return;

    const highlight = this.highlights[index];

    try {
      // 替换高亮元素为纯文本
      const textNode = document.createTextNode(highlight.text);
      highlight.element.parentNode?.replaceChild(textNode, highlight.element);

      // 从数组中移除
      this.highlights.splice(index, 1);

      // 更新UI
      this.updateNotificationCount();
      this.hideToolbar();

      console.log("[HighlightManager] 移除高亮:", highlight.text);
    } catch (error) {
      console.error("[HighlightManager] 移除高亮失败:", error);
    }
  }

  /**
   * 清除所有高亮
   */
  private handleClearAll() {
    if (this.highlights.length === 0) return;

    // 显示确认对话框
    if (!confirm("确定要清除所有高亮文本吗？此操作不可撤销。")) {
      return;
    }

    // 移除所有高亮元素
    this.highlights.forEach((highlight) => {
      try {
        const textNode = document.createTextNode(highlight.text);
        highlight.element.parentNode?.replaceChild(textNode, highlight.element);
      } catch (error) {
        console.error("[HighlightManager] 清除高亮失败:", error);
      }
    });

    // 清空数组
    this.highlights = [];

    // 隐藏通知栏
    this.notificationManager.hide();

    console.log("[HighlightManager] 清除所有高亮");
  }

  /**
   * 更新通知数量
   */
  private updateNotificationCount() {
    this.notificationManager.updateCount(this.highlights.length);
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `highlight-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 销毁管理器
   */
  destroy() {
    this.toolbarManager.destroy();
    this.notificationManager.destroy();

    // 移除事件监听器
    document.removeEventListener(
      "selectionchange",
      this.handleSelectionChange.bind(this)
    );
    document.removeEventListener("scroll", this.hideToolbar.bind(this), true);
    document.removeEventListener("click", this.handleDocumentClick.bind(this));
    document.removeEventListener(
      "mouseenter",
      this.handleMouseEnter.bind(this),
      true
    );
    document.removeEventListener(
      "mouseleave",
      this.handleMouseLeave.bind(this),
      true
    );

    // 清除所有高亮（不显示确认对话框）
    this.highlights.forEach((highlight) => {
      try {
        const textNode = document.createTextNode(highlight.text);
        highlight.element.parentNode?.replaceChild(textNode, highlight.element);
      } catch (error) {
        console.error("[HighlightManager] 销毁时清除高亮失败:", error);
      }
    });

    this.highlights = [];
    this.isInitialized = false;

    console.log("[HighlightManager] 高亮采集系统已销毁");
  }
}
