/**
 * 高亮采集系统入口
 */

import { HighlightSystem } from "./HighlightSystem";

let highlightSystemInstance: HighlightSystem | null = null;

/**
 * 检查当前页面是否应该启用高亮系统
 */
function shouldEnableHighlightSystem(): boolean {
  const hostname = window.location.hostname.toLowerCase();

  // 排除特殊适配的法律数据库站点
  const excludedSites = [
    // 威科先行
    "law.wkinfo.com.cn",
    // 北大法宝
    "www.pkulaw.com",
    "pkulaw.com",
    // 中国裁判文书网
    "wenshu.court.gov.cn",
  ];

  // 检查是否为排除的域名
  const isExcludedDomain = excludedSites.some(
    (domain) => hostname === domain || hostname.endsWith("." + domain)
  );

  if (isExcludedDomain) {
    console.log("[HighlightSystem] 当前站点已被排除:", hostname);
    return false;
  }

  // 检查是否为镜像站点（包含特定关键词的法律数据库）
  const isLegalMirror =
    (hostname.includes("wkinf") && hostname.includes("law")) ||
    hostname.includes("pkulaw") ||
    hostname.includes("wenshu");

  if (isLegalMirror) {
    console.log(
      "[HighlightSystem] 检测到法律数据库镜像站点，已排除:",
      hostname
    );
    return false;
  }

  console.log("[HighlightSystem] 当前站点支持高亮系统:", hostname);
  return true;
}

/**
 * 初始化高亮系统
 */
export function initHighlightSystem(): void {
  // 检查是否应该启用
  if (!shouldEnableHighlightSystem()) {
    return;
  }

  // 避免重复初始化
  if (highlightSystemInstance) {
    console.log("[HighlightSystem] 系统已经初始化，跳过");
    return;
  }

  try {
    highlightSystemInstance = new HighlightSystem();
    console.log("[HighlightSystem] 系统初始化成功");
  } catch (error) {
    console.error("[HighlightSystem] 系统初始化失败:", error);
  }
}

/**
 * 销毁高亮系统
 */
export function destroyHighlightSystem(): void {
  if (highlightSystemInstance) {
    highlightSystemInstance.destroy();
    highlightSystemInstance = null;
    console.log("[HighlightSystem] 系统已销毁");
  }
}

/**
 * 获取高亮系统实例
 */
export function getHighlightSystemInstance(): HighlightSystem | null {
  return highlightSystemInstance;
}

// 导出类型
export type { HighlightItem, HighlightState, ToolbarPosition } from "./types";
export { HighlightSystem } from "./HighlightSystem";
