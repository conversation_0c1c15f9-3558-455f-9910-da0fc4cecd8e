/**
 * 高亮采集系统入口
 */

import { HighlightSystem } from "./HighlightSystem";
import { isWkinfoPage } from "../sites/wkinfo";
import { isPkulawPage } from "../sites/pkulaw";
import { isWenshuPage } from "../sites/wenshu";

let highlightSystemInstance: HighlightSystem | null = null;

/**
 * 检查当前页面是否应该启用高亮系统
 * 只在法律数据库的案例详情页排除，其他页面允许使用高亮功能
 */
function shouldEnableHighlightSystem(): boolean {
  const hostname = window.location.hostname.toLowerCase();
  const url = window.location.href;

  console.log("[HighlightSystem] 检查页面是否支持高亮系统:", {
    hostname,
    url,
  });

  // 检查是否为威科先行案例详情页
  if (isWkinfoPage()) {
    console.log("[HighlightSystem] 检测到威科先行案例详情页，排除高亮系统");
    return false;
  }

  // 检查是否为北大法宝案例详情页
  if (isPkulawPage()) {
    console.log("[HighlightSystem] 检测到北大法宝案例详情页，排除高亮系统");
    return false;
  }

  // 检查是否为中国裁判文书网案例详情页
  if (isWenshuPage()) {
    console.log(
      "[HighlightSystem] 检测到中国裁判文书网案例详情页，排除高亮系统"
    );
    return false;
  }

  console.log("[HighlightSystem] 当前页面支持高亮系统:", {
    hostname,
    pageType: "非案例详情页",
  });
  return true;
}

/**
 * 初始化高亮系统
 */
export function initHighlightSystem(): void {
  console.log("[HighlightSystem] 开始初始化高亮系统");

  // 检查是否应该启用
  if (!shouldEnableHighlightSystem()) {
    console.log("[HighlightSystem] 当前站点不支持高亮系统");
    return;
  }

  // 避免重复初始化
  if (highlightSystemInstance) {
    console.log("[HighlightSystem] 系统已经初始化，跳过");
    return;
  }

  try {
    console.log("[HighlightSystem] 创建HighlightSystem实例...");
    highlightSystemInstance = new HighlightSystem();
    console.log("[HighlightSystem] 系统初始化成功");
  } catch (error) {
    console.error("[HighlightSystem] 系统初始化失败:", error);
  }
}

/**
 * 销毁高亮系统
 */
export function destroyHighlightSystem(): void {
  if (highlightSystemInstance) {
    highlightSystemInstance.destroy();
    highlightSystemInstance = null;
    console.log("[HighlightSystem] 系统已销毁");
  }
}

/**
 * 获取高亮系统实例
 */
export function getHighlightSystemInstance(): HighlightSystem | null {
  return highlightSystemInstance;
}

// 导出类型
export type { HighlightItem, HighlightState, ToolbarPosition } from "./types";
export { HighlightSystem } from "./HighlightSystem";
