/**
 * 高亮采集系统入口
 */

import { HighlightManager } from './HighlightManager'
import { isWkinfoPage } from '../sites/wkinfo'
import { isPkulawPage } from '../sites/pkulaw'
import { isWenshuPage } from '../sites/wenshu'

/**
 * 检查当前站点是否排除高亮功能
 * @returns 是否排除
 */
export function isExcludedSite(): boolean {
  // 排除已适配的法律数据库站点
  return isWkinfoPage() || isPkulawPage() || isWenshuPage()
}

/**
 * 初始化高亮采集系统
 */
export function initHighlightSystem() {
  // 检查是否为排除站点
  if (isExcludedSite()) {
    console.log('[HighlightSystem] 当前站点已排除高亮功能')
    return
  }

  console.log('[HighlightSystem] 初始化高亮采集系统')
  
  // 创建并初始化高亮管理器
  const highlightManager = new HighlightManager()
  highlightManager.init()
  
  // 保存到全局变量，方便调试
  ;(window as any).__quoteHighlightManager = highlightManager
  
  return highlightManager
}

// 导出类型和组件
export * from './types'
export { HighlightManager } from './HighlightManager'
