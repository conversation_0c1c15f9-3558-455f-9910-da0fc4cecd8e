import { ToolbarPosition } from "./types";
import { getIcon } from "../components/icon/icons";

export type ToolbarMode = "add" | "remove";

export interface ToolbarConfig {
  onHighlight: () => Promise<void>;
  onRemoveHighlight: () => void;
  onStateChange?: (visible: boolean, position?: ToolbarPosition) => void;
}

interface DragState {
  isDragging: boolean;
  startX: number;
  startY: number;
  initialToolbarX: number;
  initialToolbarY: number;
  dragHandle: HTMLElement | null;
}

/**
 * 工具栏管理器 - 负责工具栏的创建、渲染、定位和拖拽功能
 */
export class ToolbarManager {
  private container: HTMLElement | null = null;
  private visible: boolean = false;
  private position: ToolbarPosition = { x: 0, y: 0 };
  private mode: ToolbarMode = "add";
  private targetHighlightId: string | null = null;

  // 拖拽状态管理
  private dragState: DragState = {
    isDragging: false,
    startX: 0,
    startY: 0,
    initialToolbarX: 0,
    initialToolbarY: 0,
    dragHandle: null,
  };

  // 绑定拖拽事件处理器（避免重复绑定问题）
  private boundHandleDragStart = this.handleDragStart.bind(this);
  private boundHandleDragMove = this.handleDragMove.bind(this);
  private boundHandleDragEnd = this.handleDragEnd.bind(this);

  constructor(private config: ToolbarConfig) {
    this.createContainer();
    this.injectStyles();
  }

  /**
   * 创建工具栏容器
   */
  private createContainer(): void {
    this.container = document.createElement("div");
    this.container.id = "quote-selection-toolbar-container";
    this.container.style.position = "absolute";
    this.container.style.zIndex = "999999";
    this.container.style.pointerEvents = "auto";
    document.body.appendChild(this.container);
  }

  /**
   * 注入工具栏样式
   */
  private injectStyles(): void {
    const styleId = "quote-toolbar-styles";
    if (document.getElementById(styleId)) return;

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* 选区工具栏样式 */
      .quote-selection-toolbar {
        position: absolute !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        display: flex !important;
        align-items: center !important;
        padding: 6px !important;
        gap: 4px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        white-space: nowrap !important;
        user-select: none !important;
        pointer-events: auto !important;
        transition: all 0.2s ease !important;
      }

      .quote-selection-toolbar button {
        background: transparent !important;
        border: none !important;
        cursor: pointer !important;
        padding: 8px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
        color: #333 !important;
        font-size: 14px !important;
        line-height: 1 !important;
        min-width: 32px !important;
        height: 32px !important;
      }

      .quote-selection-toolbar button:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-selection-toolbar .quote-icon {
        width: 24px !important;
        height: 24px !important;
        opacity: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 4px !important;
        cursor: move !important;
        user-select: none !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-selection-toolbar .quote-icon:hover {
        /* 移除hover时的背景色块 */
      }

      .quote-selection-toolbar .quote-icon:active {
        cursor: grabbing !important;
      }

      /* 拖拽时的工具栏样式 */
      .quote-selection-toolbar.dragging {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
        transform: scale(1.02) !important;
        transition: none !important;
      }

      .quote-selection-toolbar.dragging .quote-icon {
        background-color: #e3f2fd !important;
        transform: scale(1.1) !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 显示工具栏
   */
  public show(
    position: ToolbarPosition,
    mode: ToolbarMode,
    targetHighlightId?: string
  ): void {
    console.log("[ToolbarManager] 显示工具栏:", {
      position,
      mode,
      targetHighlightId,
    });

    this.visible = true;
    this.position = position;
    this.mode = mode;
    this.targetHighlightId = targetHighlightId || null;

    this.render();
    this.config.onStateChange?.(true, position);
  }

  /**
   * 隐藏工具栏
   */
  public hide(): void {
    // 拖拽时不隐藏工具栏
    if (this.dragState.isDragging) {
      return;
    }

    this.visible = false;
    this.render();
    this.config.onStateChange?.(false);
  }

  /**
   * 滑动工具栏到新位置
   */
  public slideTo(newPosition: ToolbarPosition, highlightId: string): void {
    if (!this.container) return;

    // 获取当前位置
    const currentStyle = this.container.style;
    const currentX = parseInt(currentStyle.left) || 0;
    const currentY = parseInt(currentStyle.top) || 0;

    // 计算位移
    const deltaX = newPosition.x - currentX;
    const deltaY = newPosition.y - currentY;

    console.log("[ToolbarManager] 工具栏滑动:", {
      from: { x: currentX, y: currentY },
      to: newPosition,
      delta: { x: deltaX, y: deltaY },
    });

    // 应用变换动画
    this.container.style.transform = `translate(${deltaX}px, ${deltaY}px)`;

    // 动画完成后更新实际位置
    setTimeout(() => {
      if (this.container) {
        this.container.style.left = `${newPosition.x}px`;
        this.container.style.top = `${newPosition.y}px`;
        this.container.style.transform = "";
      }

      // 更新状态
      this.position = newPosition;
      this.targetHighlightId = highlightId;

      console.log("[ToolbarManager] 工具栏滑动完成");
    }, 300);
  }

  /**
   * 渲染工具栏
   */
  private render(): void {
    if (!this.container) return;

    console.log("[ToolbarManager] 渲染工具栏:", {
      visible: this.visible,
      position: this.position,
      mode: this.mode,
      isDragging: this.dragState.isDragging,
    });

    // 拖拽过程中不重新渲染，避免中断拖拽
    if (this.dragState.isDragging) {
      console.log("[ToolbarManager] 拖拽中，跳过重新渲染");
      return;
    }

    // 清空容器
    this.container.innerHTML = "";

    if (!this.visible) {
      console.log("[ToolbarManager] 工具栏隐藏状态，清空容器");
      return;
    }

    // 设置容器位置
    this.container.style.left = `${this.position.x}px`;
    this.container.style.top = `${this.position.y}px`;

    // 创建工具栏元素
    const toolbar = document.createElement("div");
    toolbar.className = "quote-selection-toolbar";

    // 创建按钮
    const button = document.createElement("button");
    button.title = this.mode === "add" ? "高亮采集" : "取消高亮";

    if (this.mode === "add") {
      button.innerHTML = getIcon("pencilLine");
      button.onclick = () => this.config.onHighlight();
    } else {
      button.innerHTML = getIcon("pencilOff");
      button.onclick = () => this.config.onRemoveHighlight();
    }

    // 创建分割线
    const divider = document.createElement("div");
    divider.className = "quote-toolbar-divider";
    divider.style.cssText = `
      width: 1px;
      height: 20px;
      background-color: #e0e0e0;
      margin: 0 4px;
    `;

    // 创建Quote图标
    const icon = document.createElement("div");
    icon.className = "quote-icon";
    icon.innerHTML = getIcon("quote");

    // 设置拖拽功能
    this.setupQuoteIconDrag(icon);

    toolbar.appendChild(button);
    toolbar.appendChild(divider);
    toolbar.appendChild(icon);
    this.container.appendChild(toolbar);

    console.log("[ToolbarManager] 工具栏DOM渲染完成");
  }

  /**
   * 获取当前状态
   */
  public getState() {
    return {
      visible: this.visible,
      position: this.position,
      mode: this.mode,
      targetHighlightId: this.targetHighlightId,
      isDragging: this.dragState.isDragging,
    };
  }

  /**
   * 初始化Quote图标的拖拽功能
   */
  private setupQuoteIconDrag(icon: HTMLElement): void {
    console.log("[ToolbarManager] 设置Quote图标拖拽功能");

    // 添加调试事件监听器
    icon.addEventListener("mousedown", (e) => {
      console.log("[ToolbarManager] Quote图标 mousedown 事件触发", {
        target: e.target,
        clientX: e.clientX,
        clientY: e.clientY,
      });
    });

    icon.addEventListener("mousedown", this.boundHandleDragStart);
  }

  /**
   * 开始拖拽
   */
  private handleDragStart(event: MouseEvent): void {
    event.preventDefault();
    event.stopPropagation();

    if (!this.container) return;

    this.dragState.isDragging = true;
    this.dragState.startX = event.clientX;
    this.dragState.startY = event.clientY;
    this.dragState.dragHandle = event.target as HTMLElement;

    // 获取工具栏当前位置
    const rect = this.container.getBoundingClientRect();
    this.dragState.initialToolbarX = rect.left + window.scrollX;
    this.dragState.initialToolbarY = rect.top + window.scrollY;

    // 添加全局事件监听
    document.addEventListener("mousemove", this.boundHandleDragMove);
    document.addEventListener("mouseup", this.boundHandleDragEnd);

    // 添加拖拽样式
    this.container.classList.add("dragging");

    console.log("[ToolbarManager] 开始拖拽工具栏", {
      startX: this.dragState.startX,
      startY: this.dragState.startY,
      initialX: this.dragState.initialToolbarX,
      initialY: this.dragState.initialToolbarY,
      isDragging: this.dragState.isDragging,
    });
  }

  /**
   * 拖拽移动
   */
  private handleDragMove(event: MouseEvent): void {
    console.log("[ToolbarManager] handleDragMove 被调用", {
      isDragging: this.dragState.isDragging,
      hasContainer: !!this.container,
      clientX: event.clientX,
      clientY: event.clientY,
    });

    if (!this.dragState.isDragging || !this.container) {
      console.log("[ToolbarManager] handleDragMove 提前返回");
      return;
    }

    event.preventDefault();

    // 直接更新位置，不使用requestAnimationFrame（避免延迟）
    this.updateToolbarPosition(event);
  }

  /**
   * 更新工具栏位置
   */
  private updateToolbarPosition(event: MouseEvent): void {
    if (!this.container) return;

    // 计算鼠标移动距离
    const deltaX = event.clientX - this.dragState.startX;
    const deltaY = event.clientY - this.dragState.startY;

    // 计算新位置
    let newX = this.dragState.initialToolbarX + deltaX;
    let newY = this.dragState.initialToolbarY + deltaY;

    // 边界约束
    const constrainedPosition = this.constrainToViewport(newX, newY);
    newX = constrainedPosition.x;
    newY = constrainedPosition.y;

    // 应用新位置到工具栏容器
    this.container.style.left = `${newX}px`;
    this.container.style.top = `${newY}px`;
    this.container.style.position = "absolute";

    // 实时更新位置状态
    this.position = { x: newX, y: newY };

    // 调试日志
    console.log("[ToolbarManager] 拖拽更新位置:", {
      newX,
      newY,
      deltaX,
      deltaY,
      containerStyle: {
        left: this.container.style.left,
        top: this.container.style.top,
        position: this.container.style.position,
      },
    });
  }

  /**
   * 边界约束算法
   */
  private constrainToViewport(x: number, y: number): { x: number; y: number } {
    if (!this.container) return { x, y };

    const toolbarRect = this.container.getBoundingClientRect();
    const scrollX = window.scrollX;
    const scrollY = window.scrollY;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    return {
      x: Math.max(
        scrollX,
        Math.min(scrollX + viewportWidth - toolbarRect.width, x)
      ),
      y: Math.max(
        scrollY,
        Math.min(scrollY + viewportHeight - toolbarRect.height, y)
      ),
    };
  }

  /**
   * 结束拖拽
   */
  private handleDragEnd(event: MouseEvent): void {
    if (!this.dragState.isDragging) return;

    event.preventDefault();

    // 获取最终位置并更新状态
    if (this.container) {
      const rect = this.container.getBoundingClientRect();
      this.position = {
        x: rect.left + window.scrollX,
        y: rect.top + window.scrollY,
      };
    }

    // 清理状态
    this.dragState.isDragging = false;
    this.dragState.dragHandle = null;

    // 移除全局事件监听
    document.removeEventListener("mousemove", this.boundHandleDragMove);
    document.removeEventListener("mouseup", this.boundHandleDragEnd);

    // 恢复样式
    if (this.container) {
      this.container.classList.remove("dragging");
    }

    console.log("[ToolbarManager] 结束拖拽工具栏，最终位置:", this.position);

    // 通知状态变化
    this.config.onStateChange?.(true, this.position);
  }

  /**
   * 销毁工具栏
   */
  public destroy(): void {
    // 清理拖拽事件监听器
    document.removeEventListener("mousemove", this.boundHandleDragMove);
    document.removeEventListener("mouseup", this.boundHandleDragEnd);

    if (this.container) {
      this.container.remove();
      this.container = null;
    }

    // 移除样式
    const style = document.getElementById("quote-toolbar-styles");
    if (style) {
      style.remove();
    }

    console.log("[ToolbarManager] 工具栏已销毁");
  }
}
