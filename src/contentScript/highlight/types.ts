/**
 * 高亮采集系统类型定义
 */

export interface HighlightItem {
  id: string;
  clip_id?: string; // 后端返回的clip_id
  text: string;
  element: HTMLElement; // 主要元素（第一个）
  elements?: HTMLElement[]; // 所有相关元素（用于跨元素高亮）
  timestamp: number;
  pageUrl: string;
  synced?: boolean; // 是否已同步到后端
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface HighlightState {
  items: HighlightItem[];
  toolbarVisible: boolean;
  toolbarPosition: ToolbarPosition;
  toolbarMode: "add" | "remove";
  targetHighlightId?: string;
  notificationVisible: boolean;
  currentClipId?: string; // 当前页面的clip_id
  inboxProjectId?: string; // INBOX项目ID缓存
}

export interface TextFragment {
  textNode: Text;
  startOffset: number;
  endOffset: number;
  container: Element; // 最近的安全容器元素
}
