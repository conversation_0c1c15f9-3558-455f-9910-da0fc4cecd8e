/**
 * 高亮采集系统类型定义
 */

export interface HighlightItem {
  id: string;
  text: string;
  element: HTMLElement;
  timestamp: number;
  pageUrl: string;
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface HighlightState {
  items: HighlightItem[];
  toolbarVisible: boolean;
  toolbarPosition: ToolbarPosition;
  toolbarMode: 'add' | 'remove';
  targetHighlightId?: string;
  notificationVisible: boolean;
}
