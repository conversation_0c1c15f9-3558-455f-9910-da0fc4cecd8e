/**
 * 高亮采集系统类型定义
 */

export interface HighlightItem {
  id: string
  text: string
  range: Range
  element: HTMLElement
  timestamp: number
  pageUrl: string
}

export interface ToolbarPosition {
  x: number
  y: number
}

export interface ToolbarState {
  visible: boolean
  mode: 'add' | 'remove'
  position: ToolbarPosition
  targetHighlightId?: string
}

export interface NotificationState {
  visible: boolean
  count: number
}

export type HighlightEventType = 
  | 'highlight-added'
  | 'highlight-removed'
  | 'highlights-cleared'
  | 'count-updated'
