/**
 * 高亮采集系统类型定义
 */

export interface HighlightItem {
  id: string;
  text: string;
  element: HTMLElement; // 主要元素（第一个）
  elements?: HTMLElement[]; // 所有相关元素（用于跨元素高亮）
  timestamp: number;
  pageUrl: string;
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface HighlightState {
  items: HighlightItem[];
  toolbarVisible: boolean;
  toolbarPosition: ToolbarPosition;
  toolbarMode: "add" | "remove";
  targetHighlightId?: string;
  notificationVisible: boolean;
}

export interface SelectionRegion {
  type: "table" | "normal";
  nodes: Text[];
  range: Range;
}
