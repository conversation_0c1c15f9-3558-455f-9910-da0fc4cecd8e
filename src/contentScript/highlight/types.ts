/**
 * 高亮采集系统类型定义
 */

export interface HighlightItem {
  id: string;
  text: string;
  element: HTMLElement; // 主要元素（第一个）
  elements?: HTMLElement[]; // 所有相关元素（用于跨元素高亮）
  timestamp: number;
  pageUrl: string;
  layer?: number; // 新增：层级信息
  ranges?: TextRange[]; // 新增：支持多个不连续范围
}

export interface ToolbarPosition {
  x: number;
  y: number;
}

export interface HighlightState {
  items: HighlightItem[];
  toolbarVisible: boolean;
  toolbarPosition: ToolbarPosition;
  toolbarMode: "add" | "remove";
  targetHighlightId?: string;
  notificationVisible: boolean;
}

export interface TextFragment {
  textNode: Text;
  startOffset: number;
  endOffset: number;
  container: Element; // 最近的安全容器元素
}

// 新增：文本范围定义
export interface TextRange {
  startContainer: Node;
  startOffset: number;
  endContainer: Node;
  endOffset: number;
  textContent: string;
}

// 新增：重叠分析结果
export interface OverlapSegment {
  textNode: Text;
  startOffset: number;
  endOffset: number;
  existingLayers: string[]; // 该段已存在的高亮层
}

// 新增：高亮层级信息
export interface HighlightLayer {
  highlightId: string;
  layer: number;
  opacity: number;
  zIndex: number;
}
