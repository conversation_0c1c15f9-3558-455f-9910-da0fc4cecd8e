import React from 'react';
import { ToolbarPosition } from '../types';

interface SelectionToolbarProps {
  visible: boolean;
  position: ToolbarPosition;
  mode: 'add' | 'remove';
  onHighlight: () => void;
  onRemove: () => void;
}

export const SelectionToolbar: React.FC<SelectionToolbarProps> = ({
  visible,
  position,
  mode,
  onHighlight,
  onRemove,
}) => {
  if (!visible) return null;

  const handleClick = () => {
    if (mode === 'add') {
      onHighlight();
    } else {
      onRemove();
    }
  };

  return (
    <div
      className="quote-selection-toolbar"
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      <button onClick={handleClick} title={mode === 'add' ? '高亮采集' : '取消高亮'}>
        {mode === 'add' ? (
          // 高亮采集图标
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2v-4" />
            <path d="M14 3l7 7-10 10H4v-7L14 3z" />
          </svg>
        ) : (
          // 取消高亮图标
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
            <path d="M18 6L6 18" />
            <path d="M6 6l12 12" />
          </svg>
        )}
      </button>
      
      {/* Quote装饰图标 */}
      <div className="quote-icon">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
          <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
        </svg>
      </div>
    </div>
  );
};
