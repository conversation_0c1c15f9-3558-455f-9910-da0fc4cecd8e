import React from 'react'
import { createRoot } from 'react-dom/client'
import type { ToolbarState } from '../types'

interface SelectionToolbarProps {
  state: ToolbarState
  onHighlight: () => void
  onRemoveHighlight: () => void
}

const SelectionToolbar: React.FC<SelectionToolbarProps> = ({
  state,
  onHighlight,
  onRemoveHighlight
}) => {
  if (!state.visible) return null

  const handleButtonClick = () => {
    if (state.mode === 'add') {
      onHighlight()
    } else {
      onRemoveHighlight()
    }
  }

  return (
    <div 
      className="quote-selection-toolbar"
      style={{
        left: `${state.position.x}px`,
        top: `${state.position.y}px`,
      }}
    >
      <button
        className="quote-toolbar-button"
        onClick={handleButtonClick}
        title={state.mode === 'add' ? '高亮采集' : '取消高亮'}
      >
        {state.mode === 'add' ? (
          // 高亮采集图标
          <svg className="quote-toolbar-icon" viewBox="0 0 16 16" fill="currentColor">
            <path d="M3.5 2A1.5 1.5 0 0 0 2 3.5v9A1.5 1.5 0 0 0 3.5 14h9a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 12.5 2h-9zM11 5.5a.5.5 0 0 1-.5.5h-2v2a.5.5 0 0 1-1 0v-2h-2a.5.5 0 0 1 0-1h2v-2a.5.5 0 0 1 1 0v2h2a.5.5 0 0 1 .5.5z"/>
          </svg>
        ) : (
          // 取消高亮图标
          <svg className="quote-toolbar-icon" viewBox="0 0 16 16" fill="currentColor">
            <path d="M3.5 2A1.5 1.5 0 0 0 2 3.5v9A1.5 1.5 0 0 0 3.5 14h9a1.5 1.5 0 0 0 1.5-1.5v-9A1.5 1.5 0 0 0 12.5 2h-9zM11 7.5a.5.5 0 0 1-.5.5h-5a.5.5 0 0 1 0-1h5a.5.5 0 0 1 .5.5z"/>
          </svg>
        )}
      </button>
      
      {/* Quote 装饰图标 */}
      <div className="quote-toolbar-icon">
        <svg viewBox="0 0 16 16" fill="currentColor">
          <path d="M12 12a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1h-1.388c0-.351.021-.703.062-1.054.062-.372.166-.703.31-.992.145-.29.331-.517.559-.683.227-.186.516-.279.868-.279V3c-.579 0-1.085.124-1.52.372a3.322 3.322 0 0 0-1.085.992 4.92 4.92 0 0 0-.62 1.458A7.712 7.712 0 0 0 9 7.558V11a1 1 0 0 0 1 1h2Zm-6 0a1 1 0 0 0 1-1V8.558a1 1 0 0 0-1-1H4.612c0-.351.021-.703.062-1.054.062-.372.166-.703.31-.992.145-.29.331-.517.559-.683.227-.186.516-.279.868-.279V3c-.579 0-1.085.124-1.52.372a3.322 3.322 0 0 0-1.085.992 4.92 4.92 0 0 0-.62 1.458A7.712 7.712 0 0 0 3 7.558V11a1 1 0 0 0 1 1h2Z"/>
        </svg>
      </div>
    </div>
  )
}

export class SelectionToolbarManager {
  private container: HTMLDivElement | null = null
  private root: any = null
  private currentState: ToolbarState = {
    visible: false,
    mode: 'add',
    position: { x: 0, y: 0 }
  }

  constructor(
    private onHighlight: () => void,
    private onRemoveHighlight: () => void
  ) {
    this.createContainer()
  }

  private createContainer() {
    this.container = document.createElement('div')
    this.container.style.position = 'absolute'
    this.container.style.top = '0'
    this.container.style.left = '0'
    this.container.style.pointerEvents = 'none'
    this.container.style.zIndex = '999999'
    document.body.appendChild(this.container)
    
    this.root = createRoot(this.container)
  }

  show(position: { x: number; y: number }, mode: 'add' | 'remove', targetHighlightId?: string) {
    this.currentState = {
      visible: true,
      mode,
      position,
      targetHighlightId
    }
    this.render()
  }

  hide() {
    this.currentState.visible = false
    this.render()
  }

  private render() {
    if (!this.root) return
    
    this.root.render(
      <SelectionToolbar
        state={this.currentState}
        onHighlight={this.onHighlight}
        onRemoveHighlight={this.onRemoveHighlight}
      />
    )
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    this.container = null
    this.root = null
  }
}
