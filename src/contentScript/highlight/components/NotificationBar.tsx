import React from 'react'
import { createRoot } from 'react-dom/client'
import type { NotificationState } from '../types'

interface NotificationBarProps {
  state: NotificationState
  onClear: () => void
}

const NotificationBar: React.FC<NotificationBarProps> = ({
  state,
  onClear
}) => {
  if (!state.visible || state.count === 0) return null

  const getCountBadgeClass = () => {
    if (state.count >= 10) {
      return 'quote-count-badge two-digit'
    }
    return 'quote-count-badge'
  }

  const getCountText = () => {
    if (state.count > 99) {
      return '99+'
    }
    return state.count.toString()
  }

  return (
    <div className="quote-notification-bar">
      <div className="quote-count-display">
        <div className={getCountBadgeClass()}>
          {getCountText()}
        </div>
        <span className="quote-count-text">已采集到知识库</span>
      </div>
      
      <div style={{ display: 'flex', gap: '8px' }}>
        <button
          className="quote-notification-button danger"
          onClick={onClear}
        >
          清除
        </button>
        
        <button
          className="quote-notification-button"
          onClick={() => {
            // TODO: 实现项目选择功能
            console.log('选择项目功能待实现')
          }}
        >
          选择project
        </button>
      </div>
    </div>
  )
}

export class NotificationBarManager {
  private container: HTMLDivElement | null = null
  private root: any = null
  private currentState: NotificationState = {
    visible: false,
    count: 0
  }

  constructor(private onClear: () => void) {
    this.createContainer()
  }

  private createContainer() {
    this.container = document.createElement('div')
    this.container.style.position = 'fixed'
    this.container.style.top = '0'
    this.container.style.left = '0'
    this.container.style.pointerEvents = 'none'
    this.container.style.zIndex = '999998'
    document.body.appendChild(this.container)
    
    this.root = createRoot(this.container)
  }

  updateCount(count: number) {
    this.currentState = {
      visible: count > 0,
      count
    }
    this.render()
  }

  hide() {
    this.currentState.visible = false
    this.render()
  }

  private render() {
    if (!this.root) return
    
    this.root.render(
      <NotificationBar
        state={this.currentState}
        onClear={this.onClear}
      />
    )
  }

  destroy() {
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
    }
    this.container = null
    this.root = null
  }
}
