import React, { useState } from 'react';

interface NotificationBarProps {
  visible: boolean;
  count: number;
  onClear: () => void;
  onSelectProject: () => void;
}

interface ConfirmDialogProps {
  visible: boolean;
  onConfirm: () => void;
  onCancel: () => void;
}

const ConfirmDialog: React.FC<ConfirmDialogProps> = ({ visible, onConfirm, onCancel }) => {
  if (!visible) return null;

  return (
    <>
      <div className="quote-confirm-dialog-overlay" onClick={onCancel} />
      <div className="quote-confirm-dialog">
        <h3>确认清除</h3>
        <p>确定要清除页面中所有高亮文本吗？此操作不可撤销。</p>
        <div className="quote-confirm-dialog-actions">
          <button className="quote-confirm-dialog-btn" onClick={onCancel}>
            取消
          </button>
          <button className="quote-confirm-dialog-btn primary" onClick={onConfirm}>
            确认清除
          </button>
        </div>
      </div>
    </>
  );
};

export const NotificationBar: React.FC<NotificationBarProps> = ({
  visible,
  count,
  onClear,
  onSelectProject,
}) => {
  const [showConfirm, setShowConfirm] = useState(false);

  if (!visible) return null;

  const handleClearClick = () => {
    setShowConfirm(true);
  };

  const handleConfirmClear = () => {
    setShowConfirm(false);
    onClear();
  };

  const handleCancelClear = () => {
    setShowConfirm(false);
  };

  const getCountDisplay = () => {
    if (count > 99) return '99+';
    return count.toString();
  };

  const getCountBadgeClass = () => {
    if (count >= 10) return 'quote-count-badge two-digit';
    return 'quote-count-badge';
  };

  return (
    <>
      <div className="quote-notification-bar">
        <div className="quote-notification-count">
          <div className={getCountBadgeClass()}>
            {getCountDisplay()}
          </div>
          <span>已采集到知识库</span>
        </div>
        
        <div className="quote-notification-actions">
          <button 
            className="quote-notification-btn danger" 
            onClick={handleClearClick}
          >
            清除
          </button>
          <button 
            className="quote-notification-btn" 
            onClick={onSelectProject}
          >
            选择project
          </button>
        </div>
      </div>

      <ConfirmDialog
        visible={showConfirm}
        onConfirm={handleConfirmClear}
        onCancel={handleCancelClear}
      />
    </>
  );
};
