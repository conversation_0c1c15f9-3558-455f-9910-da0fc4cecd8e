/**
 * 高亮采集系统样式
 */

/* 高亮文本样式 */
.quote-highlight {
  background-color: #fff3cd !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  position: relative !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.quote-highlight:hover {
  background-color: #ffeaa7 !important;
}

/* 选区工具栏样式 */
.quote-selection-toolbar {
  position: absolute !important;
  z-index: 999999 !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1 !important;
}

.quote-selection-toolbar button {
  background: none !important;
  border: none !important;
  cursor: pointer !important;
  padding: 6px !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: background-color 0.2s ease !important;
}

.quote-selection-toolbar button:hover {
  background-color: #f5f5f5 !important;
}

.quote-selection-toolbar .quote-icon {
  width: 16px !important;
  height: 16px !important;
  opacity: 0.6 !important;
}

/* 底部通知栏样式 */
.quote-notification-bar {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 999999 !important;
  background: white !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 12px 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  line-height: 1 !important;
  max-width: 400px !important;
  animation: slideUp 0.3s ease-out !important;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

.quote-notification-count {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  color: #333 !important;
}

.quote-count-badge {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  background-color: #007bff !important;
  color: white !important;
  font-weight: 600 !important;
  font-size: 12px !important;
  min-width: 20px !important;
  height: 20px !important;
  border-radius: 10px !important;
}

.quote-count-badge.two-digit {
  border-radius: 10px !important;
  padding: 0 6px !important;
}

.quote-notification-actions {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.quote-notification-btn {
  background: none !important;
  border: 1px solid #e0e0e0 !important;
  border-radius: 6px !important;
  padding: 6px 12px !important;
  cursor: pointer !important;
  font-size: 12px !important;
  color: #666 !important;
  transition: all 0.2s ease !important;
}

.quote-notification-btn:hover {
  background-color: #f5f5f5 !important;
  border-color: #ccc !important;
}

.quote-notification-btn.danger {
  color: #dc3545 !important;
  border-color: #dc3545 !important;
}

.quote-notification-btn.danger:hover {
  background-color: #dc3545 !important;
  color: white !important;
}

/* 确认删除弹窗样式 */
.quote-confirm-dialog {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999999 !important;
  background: white !important;
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
  padding: 24px !important;
  max-width: 400px !important;
  width: 90% !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
}

.quote-confirm-dialog-overlay {
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: 9999998 !important;
}

.quote-confirm-dialog h3 {
  margin: 0 0 12px 0 !important;
  font-size: 16px !important;
  font-weight: 600 !important;
  color: #333 !important;
}

.quote-confirm-dialog p {
  margin: 0 0 20px 0 !important;
  font-size: 14px !important;
  color: #666 !important;
  line-height: 1.4 !important;
}

.quote-confirm-dialog-actions {
  display: flex !important;
  justify-content: flex-end !important;
  gap: 12px !important;
}

.quote-confirm-dialog-btn {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  border: 1px solid #e0e0e0 !important;
  background: white !important;
  cursor: pointer !important;
  font-size: 14px !important;
  transition: all 0.2s ease !important;
}

.quote-confirm-dialog-btn.primary {
  background: #dc3545 !important;
  color: white !important;
  border-color: #dc3545 !important;
}

.quote-confirm-dialog-btn:hover {
  background-color: #f5f5f5 !important;
}

.quote-confirm-dialog-btn.primary:hover {
  background-color: #c82333 !important;
}
