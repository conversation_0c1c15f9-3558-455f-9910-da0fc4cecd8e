/**
 * 高亮采集系统样式
 */

/* 高亮文本样式 */
.quote-highlight {
  background-color: #fef3c7 !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
  cursor: pointer !important;
  transition: background-color 0.2s ease !important;
}

.quote-highlight:hover {
  background-color: #fde68a !important;
}

/* 工具栏容器 */
.quote-selection-toolbar {
  position: absolute !important;
  z-index: 999999 !important;
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  padding: 8px !important;
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  pointer-events: auto !important;
}

/* 工具栏按钮 */
.quote-toolbar-button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 32px !important;
  height: 32px !important;
  border: none !important;
  border-radius: 6px !important;
  background: #f3f4f6 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  color: #374151 !important;
}

.quote-toolbar-button:hover {
  background: #e5e7eb !important;
  transform: scale(1.05) !important;
}

.quote-toolbar-button:active {
  transform: scale(0.95) !important;
}

/* Quote 图标装饰 */
.quote-toolbar-icon {
  width: 16px !important;
  height: 16px !important;
  opacity: 0.6 !important;
}

/* 通知栏样式 */
.quote-notification-bar {
  position: fixed !important;
  bottom: 20px !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  z-index: 999998 !important;
  background: white !important;
  border: 1px solid #e5e7eb !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
  padding: 12px 16px !important;
  display: flex !important;
  align-items: center !important;
  gap: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
  font-size: 14px !important;
  color: #374151 !important;
  pointer-events: auto !important;
  animation: slideUp 0.3s ease-out !important;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}

/* 数量显示 */
.quote-count-display {
  display: flex !important;
  align-items: center !important;
  gap: 8px !important;
}

.quote-count-badge {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 24px !important;
  height: 24px !important;
  background: #3b82f6 !important;
  color: white !important;
  font-size: 12px !important;
  font-weight: 600 !important;
  border-radius: 12px !important;
}

.quote-count-badge.two-digit {
  border-radius: 8px !important;
  padding: 0 6px !important;
}

.quote-count-text {
  font-weight: 500 !important;
  color: #374151 !important;
}

/* 通知栏按钮 */
.quote-notification-button {
  padding: 6px 12px !important;
  border: 1px solid #d1d5db !important;
  border-radius: 6px !important;
  background: white !important;
  color: #374151 !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

.quote-notification-button:hover {
  background: #f9fafb !important;
  border-color: #9ca3af !important;
}

.quote-notification-button.danger {
  color: #dc2626 !important;
  border-color: #fca5a5 !important;
}

.quote-notification-button.danger:hover {
  background: #fef2f2 !important;
  border-color: #f87171 !important;
}
