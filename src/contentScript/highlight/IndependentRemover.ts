/**
 * 独立高亮删除管理器
 * 支持删除重叠高亮中的单个层，不影响其他层
 */
export class IndependentRemover {
  
  /**
   * 删除独立高亮，不影响其他高亮
   */
  public removeIndependentHighlight(highlightId: string): void {
    console.log("[IndependentRemover] 删除独立高亮，不影响其他高亮:", highlightId);
    
    // 1. 获取该高亮的所有片段
    const segments = this.getHighlightSegments(highlightId);
    
    console.log("[IndependentRemover] 找到高亮片段:", {
      highlightId,
      segmentCount: segments.length
    });
    
    // 2. 按DOM顺序处理每个片段
    segments.forEach((segment, index) => {
      this.removeSegmentSafely(segment, highlightId, index);
    });
    
    console.log("[IndependentRemover] 删除完成:", highlightId);
  }

  /**
   * 获取高亮的所有片段
   */
  private getHighlightSegments(highlightId: string): HTMLElement[] {
    return Array.from(
      document.querySelectorAll(`[data-highlight-id="${highlightId}"]`)
    ) as HTMLElement[];
  }

  /**
   * 安全删除单个片段
   */
  private removeSegmentSafely(segment: HTMLElement, highlightId: string, index: number): void {
    const existingLayers = segment.getAttribute('data-existing-layers');
    
    console.log("[IndependentRemover] 处理片段:", {
      highlightId,
      segmentIndex: index,
      hasExistingLayers: !!existingLayers,
      existingLayers: existingLayers?.split(',') || []
    });
    
    if (existingLayers) {
      // 重叠区域：只移除当前层，保留其他层
      this.removeLayerFromSegment(segment, highlightId);
    } else {
      // 独立区域：直接替换为文本
      this.replaceWithText(segment);
    }
  }

  /**
   * 从重叠片段中移除指定层
   */
  private removeLayerFromSegment(segment: HTMLElement, highlightId: string): void {
    const existingLayers = segment.getAttribute('data-existing-layers')?.split(',') || [];
    const currentHighlightId = segment.getAttribute('data-highlight-id');
    
    console.log("[IndependentRemover] 移除层级:", {
      targetId: highlightId,
      currentId: currentHighlightId,
      existingLayers
    });
    
    if (currentHighlightId === highlightId) {
      // 当前显示的就是要删除的层
      if (existingLayers.length > 0) {
        // 还有其他层，切换到第一个其他层
        const nextLayerId = existingLayers[0];
        const remainingLayers = existingLayers.slice(1);
        
        segment.setAttribute('data-highlight-id', nextLayerId);
        
        if (remainingLayers.length > 0) {
          segment.setAttribute('data-existing-layers', remainingLayers.join(','));
        } else {
          segment.removeAttribute('data-existing-layers');
          segment.classList.remove('quote-highlight-layered');
        }
        
        // 重新应用样式
        this.reapplyLayeredStyles(segment, remainingLayers.length);
        
        console.log("[IndependentRemover] 切换到其他层:", {
          newCurrentId: nextLayerId,
          remainingLayers
        });
      } else {
        // 没有其他层了，恢复为普通文本
        this.replaceWithText(segment);
      }
    } else {
      // 要删除的层在existing layers中
      const remainingLayers = existingLayers.filter(id => id !== highlightId);
      
      if (remainingLayers.length > 0) {
        segment.setAttribute('data-existing-layers', remainingLayers.join(','));
        this.reapplyLayeredStyles(segment, remainingLayers.length);
        
        console.log("[IndependentRemover] 从existing layers中移除:", {
          removedId: highlightId,
          remainingLayers
        });
      } else {
        // 没有其他层了，移除layered标记
        segment.removeAttribute('data-existing-layers');
        segment.classList.remove('quote-highlight-layered');
        this.reapplyLayeredStyles(segment, 0);
        
        console.log("[IndependentRemover] 移除layered标记");
      }
    }
  }

  /**
   * 重新应用层级样式
   */
  private reapplyLayeredStyles(segment: HTMLElement, remainingLayerCount: number): void {
    const layer = parseInt(segment.getAttribute('data-layer') || '1');
    
    // 重新计算透明度
    const baseOpacity = 0.3;
    const layerOpacity = Math.max(0.1, baseOpacity - (remainingLayerCount * 0.05));
    
    if (remainingLayerCount > 0) {
      // 仍有重叠层
      segment.style.backgroundColor = `rgba(255, 193, 7, ${layerOpacity})`;
      segment.style.borderBottom = `2px solid rgba(255, 193, 7, 0.8)`;
    } else {
      // 恢复为单层样式
      segment.style.backgroundColor = `rgba(255, 235, 59, ${layerOpacity})`;
      segment.style.borderBottom = '';
    }
    
    console.log("[IndependentRemover] 重新应用样式:", {
      layer,
      remainingLayerCount,
      opacity: layerOpacity
    });
  }

  /**
   * 替换为普通文本
   */
  private replaceWithText(segment: HTMLElement): void {
    const parent = segment.parentNode;
    if (parent) {
      const textContent = segment.textContent || '';
      const textNode = document.createTextNode(textContent);
      parent.replaceChild(textNode, segment);
      parent.normalize(); // 合并相邻文本节点
      
      console.log("[IndependentRemover] 替换为文本:", {
        text: textContent.substring(0, 20) + (textContent.length > 20 ? '...' : '')
      });
    }
  }

  /**
   * 检查高亮是否存在
   */
  public highlightExists(highlightId: string): boolean {
    const segments = this.getHighlightSegments(highlightId);
    return segments.length > 0;
  }

  /**
   * 获取高亮的层级信息
   */
  public getHighlightLayerInfo(highlightId: string): {
    segmentCount: number;
    hasOverlaps: boolean;
    overlapCount: number;
  } {
    const segments = this.getHighlightSegments(highlightId);
    const overlappedSegments = segments.filter(s => 
      s.getAttribute('data-existing-layers')
    );
    
    return {
      segmentCount: segments.length,
      hasOverlaps: overlappedSegments.length > 0,
      overlapCount: overlappedSegments.length
    };
  }

  /**
   * 清理孤立的文本节点
   */
  public cleanupOrphanedTextNodes(): void {
    // 查找并合并相邻的文本节点
    const walker = document.createTreeWalker(
      document.body,
      NodeFilter.SHOW_TEXT,
      null
    );
    
    const textNodes: Text[] = [];
    let node;
    while (node = walker.nextNode()) {
      textNodes.push(node as Text);
    }
    
    // 按父节点分组并合并相邻文本节点
    const parentGroups = new Map<Node, Text[]>();
    textNodes.forEach(textNode => {
      const parent = textNode.parentNode;
      if (parent) {
        if (!parentGroups.has(parent)) {
          parentGroups.set(parent, []);
        }
        parentGroups.get(parent)!.push(textNode);
      }
    });
    
    parentGroups.forEach((nodes, parent) => {
      if (nodes.length > 1) {
        parent.normalize();
      }
    });
    
    console.log("[IndependentRemover] 清理孤立文本节点完成");
  }
}
