import { TextFragment } from "./types";

/**
 * 文本高亮算法引擎
 * 负责处理复杂的文本选择和高亮创建逻辑
 */
export class TextHighlighter {
  /**
   * 创建高亮元素的主入口方法
   */
  public createHighlight(range: Range, highlightId: string): HTMLElement[] {
    console.log("[TextHighlighter] 使用智能文本片段算法");

    // 使用智能算法，借鉴Remio的成功经验
    return this.createSmartHighlight(range, highlightId);
  }

  /**
   * 智能高亮算法 - 精确文本边界识别
   */
  private createSmartHighlight(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    const highlightedElements: HTMLElement[] = [];

    try {
      // 1. 获取选择范围内的所有有效文本片段
      const textFragments = this.getTextFragments(range);

      console.log("[TextHighlighter] 找到文本片段数量:", textFragments.length);

      textFragments.forEach((fragment, index) => {
        const span = this.wrapTextFragment(fragment, highlightId, index);
        if (span) {
          highlightedElements.push(span);
        }
      });

      return highlightedElements;
    } catch (error) {
      console.error("[TextHighlighter] 智能高亮算法失败:", error);

      // 降级到简单方法
      return this.fallbackToSimpleHighlight(range, highlightId);
    }
  }

  /**
   * 获取选择范围内的所有有效文本片段
   */
  private getTextFragments(range: Range): TextFragment[] {
    const fragments: TextFragment[] = [];
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          const textNode = node as Text;

          // 排除空白字符节点
          if (!textNode.textContent?.trim()) {
            return NodeFilter.FILTER_REJECT;
          }

          // 检查是否与选择范围相交
          return range.intersectsNode(textNode)
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_REJECT;
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      const textNode = node as Text;
      const fragment = this.calculateTextFragment(range, textNode);
      if (fragment) {
        fragments.push(fragment);
      }
    }

    return fragments;
  }

  /**
   * 计算文本节点与选择范围的交集片段
   */
  private calculateTextFragment(
    range: Range,
    textNode: Text
  ): TextFragment | null {
    try {
      // 创建文本节点的完整范围
      const nodeRange = document.createRange();
      nodeRange.selectNodeContents(textNode);

      // 计算交集的起始和结束位置
      let startOffset = 0;
      let endOffset = textNode.textContent?.length || 0;

      // 如果选择范围的起点在这个文本节点内
      if (range.startContainer === textNode) {
        startOffset = Math.max(startOffset, range.startOffset);
      } else if (range.comparePoint(textNode, 0) > 0) {
        // 选择范围在文本节点之后开始
        return null;
      }

      // 如果选择范围的终点在这个文本节点内
      if (range.endContainer === textNode) {
        endOffset = Math.min(endOffset, range.endOffset);
      } else if (
        range.comparePoint(textNode, textNode.textContent?.length || 0) < 0
      ) {
        // 选择范围在文本节点之前结束
        return null;
      }

      // 验证范围有效性
      if (startOffset >= endOffset) {
        return null;
      }

      // 找到最近的安全容器（不是表格结构元素）
      const container = this.findSafeContainer(textNode);

      return {
        textNode,
        startOffset,
        endOffset,
        container,
      };
    } catch (error) {
      console.warn("[TextHighlighter] 计算文本片段失败:", error);
      return null;
    }
  }

  /**
   * 找到安全的容器元素（不是表格结构元素）
   */
  private findSafeContainer(textNode: Text): Element {
    let current = textNode.parentElement;

    // 向上查找，直到找到一个安全的容器（不是表格结构元素）
    while (current) {
      const tagName = current.tagName.toLowerCase();

      // 如果是表格结构元素，继续向上查找
      if (["table", "tbody", "thead", "tfoot", "tr"].includes(tagName)) {
        current = current.parentElement;
        continue;
      }

      // 找到安全的容器
      return current;
    }

    // 如果没找到，返回最近的父元素
    return textNode.parentElement || document.body;
  }

  /**
   * 包装文本片段为高亮span
   */
  private wrapTextFragment(
    fragment: TextFragment,
    highlightId: string,
    index: number
  ): HTMLElement | null {
    try {
      // 直接使用安全的文本分割方法，避免surroundContents的限制
      return this.safeWrapTextFragment(fragment, highlightId, index);
    } catch (error) {
      console.error("[TextHighlighter] 文本包装失败:", error);
      return null;
    }
  }

  /**
   * 安全的文本分割包装方法（借鉴Remio的成功策略）
   */
  private safeWrapTextFragment(
    fragment: TextFragment,
    highlightId: string,
    index: number
  ): HTMLElement | null {
    const { textNode, startOffset, endOffset } = fragment;
    const text = textNode.textContent || "";

    // 分割文本为三部分
    const beforeText = text.substring(0, startOffset);
    const highlightText = text.substring(startOffset, endOffset);
    const afterText = text.substring(endOffset);

    // 创建高亮span
    const span = document.createElement("span");
    span.className = "quote-highlight";
    span.setAttribute("data-highlight-id", highlightId);
    span.setAttribute("data-fragment-index", index.toString());
    span.textContent = highlightText;

    // 获取父节点
    const parent = textNode.parentNode;
    if (!parent) {
      console.warn("[TextHighlighter] 文本节点没有父节点");
      return null;
    }

    // 安全的DOM操作：使用DocumentFragment一次性替换
    const documentFragment = document.createDocumentFragment();

    // 添加前缀文本
    if (beforeText) {
      documentFragment.appendChild(document.createTextNode(beforeText));
    }

    // 添加高亮span
    documentFragment.appendChild(span);

    // 添加后缀文本
    if (afterText) {
      documentFragment.appendChild(document.createTextNode(afterText));
    }

    // 一次性替换原始文本节点
    parent.replaceChild(documentFragment, textNode);

    console.log("[TextHighlighter] 安全创建文本片段高亮:", {
      beforeText,
      highlightText,
      afterText,
      fragmentIndex: index,
    });

    return span;
  }

  /**
   * 简单高亮降级方法
   */
  private fallbackToSimpleHighlight(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    try {
      // 尝试最简单的包装方法
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);

      range.surroundContents(span);
      return [span];
    } catch (error) {
      console.error("[TextHighlighter] 简单高亮降级也失败:", error);
      return [];
    }
  }
}
