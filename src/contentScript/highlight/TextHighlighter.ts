import { TextFragment } from "./types";

/**
 * 文本高亮算法引擎
 * 负责处理复杂的文本选择和高亮创建逻辑
 */
export class TextHighlighter {
  /**
   * 创建高亮元素的主入口方法
   */
  public createHighlight(range: Range, highlightId: string): HTMLElement[] {
    console.log("[TextHighlighter] 使用智能文本片段算法");

    // 使用智能算法，借鉴Remio的成功经验
    return this.createSmartHighlight(range, highlightId);
  }

  /**
   * 智能高亮算法 - 精确文本边界识别
   */
  private createSmartHighlight(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    const highlightedElements: HTMLElement[] = [];

    try {
      // 1. 获取选择范围内的所有有效文本片段
      const textFragments = this.getTextFragments(range);

      console.log("[TextHighlighter] 找到文本片段数量:", textFragments.length);

      textFragments.forEach((fragment, index) => {
        const span = this.wrapTextFragment(fragment, highlightId, index);
        if (span) {
          highlightedElements.push(span);
        }
      });

      return highlightedElements;
    } catch (error) {
      console.error("[TextHighlighter] 智能高亮算法失败:", error);

      // 降级到简单方法
      return this.fallbackToSimpleHighlight(range, highlightId);
    }
  }

  /**
   * 获取选择范围内的所有有效文本片段
   */
  private getTextFragments(range: Range): TextFragment[] {
    const fragments: TextFragment[] = [];
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          const textNode = node as Text;

          // 排除空白字符节点
          if (!textNode.textContent?.trim()) {
            return NodeFilter.FILTER_REJECT;
          }

          // 检查是否与选择范围相交
          return range.intersectsNode(textNode)
            ? NodeFilter.FILTER_ACCEPT
            : NodeFilter.FILTER_REJECT;
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      const textNode = node as Text;
      const fragment = this.calculateTextFragment(range, textNode);
      if (fragment) {
        fragments.push(fragment);
      }
    }

    return fragments;
  }

  /**
   * 计算文本节点与选择范围的交集片段
   */
  private calculateTextFragment(
    range: Range,
    textNode: Text
  ): TextFragment | null {
    try {
      const textLength = textNode.textContent?.length || 0;

      // 计算交集的起始和结束位置
      let startOffset = 0;
      let endOffset = textLength;

      console.log("[TextHighlighter] 计算文本片段:", {
        textContent: textNode.textContent?.substring(0, 50) + "...",
        textLength,
        rangeStart: {
          container: range.startContainer,
          offset: range.startOffset,
        },
        rangeEnd: { container: range.endContainer, offset: range.endOffset },
        isStartInThisNode: range.startContainer === textNode,
        isEndInThisNode: range.endContainer === textNode,
      });

      // 如果选择范围的起点在这个文本节点内
      if (range.startContainer === textNode) {
        startOffset = Math.max(0, range.startOffset);
        console.log(
          "[TextHighlighter] 起点在当前节点内，startOffset:",
          startOffset
        );
      } else {
        // 检查文本节点是否完全在选择范围内
        try {
          // 如果文本节点的起始点在选择范围之后，没有交集
          if (range.comparePoint(textNode, 0) < 0) {
            console.log("[TextHighlighter] 文本节点完全在选择范围之后，跳过");
            return null;
          }
          // 否则文本节点的开始部分在选择范围内，startOffset保持0
          console.log("[TextHighlighter] 文本节点开始部分在选择范围内");
        } catch (error) {
          console.warn(
            "[TextHighlighter] comparePoint检查失败，使用默认逻辑:",
            error
          );
          // 降级：如果comparePoint失败，假设有交集
        }
      }

      // 如果选择范围的终点在这个文本节点内
      if (range.endContainer === textNode) {
        endOffset = Math.min(textLength, range.endOffset);
        console.log(
          "[TextHighlighter] 终点在当前节点内，endOffset:",
          endOffset
        );
      } else {
        // 检查文本节点是否完全在选择范围内
        try {
          // 如果文本节点的结束点在选择范围之前，没有交集
          if (range.comparePoint(textNode, textLength) > 0) {
            console.log("[TextHighlighter] 文本节点完全在选择范围之前，跳过");
            return null;
          }
          // 否则文本节点的结束部分在选择范围内，endOffset保持textLength
          console.log("[TextHighlighter] 文本节点结束部分在选择范围内");
        } catch (error) {
          console.warn(
            "[TextHighlighter] comparePoint检查失败，使用默认逻辑:",
            error
          );
          // 降级：如果comparePoint失败，假设有交集
        }
      }

      // 验证范围有效性
      if (
        startOffset >= endOffset ||
        startOffset < 0 ||
        endOffset > textLength
      ) {
        console.log("[TextHighlighter] 范围无效:", {
          startOffset,
          endOffset,
          textLength,
        });
        return null;
      }

      // 找到最近的安全容器（不是表格结构元素）
      const container = this.findSafeContainer(textNode);

      const fragment = {
        textNode,
        startOffset,
        endOffset,
        container,
      };

      console.log("[TextHighlighter] 成功创建文本片段:", {
        text: textNode.textContent?.substring(startOffset, endOffset),
        startOffset,
        endOffset,
      });

      return fragment;
    } catch (error) {
      console.warn("[TextHighlighter] 计算文本片段失败:", error);
      return null;
    }
  }

  /**
   * 找到安全的容器元素（不是表格结构元素）
   */
  private findSafeContainer(textNode: Text): Element {
    let current = textNode.parentElement;

    // 向上查找，直到找到一个安全的容器（不是表格结构元素）
    while (current) {
      const tagName = current.tagName.toLowerCase();

      // 如果是表格结构元素，继续向上查找
      if (["table", "tbody", "thead", "tfoot", "tr"].includes(tagName)) {
        current = current.parentElement;
        continue;
      }

      // 找到安全的容器
      return current;
    }

    // 如果没找到，返回最近的父元素
    return textNode.parentElement || document.body;
  }

  /**
   * 包装文本片段为高亮span
   */
  private wrapTextFragment(
    fragment: TextFragment,
    highlightId: string,
    index: number
  ): HTMLElement | null {
    try {
      const { textNode, startOffset, endOffset } = fragment;

      // 创建新的范围来包装这个文本片段
      const fragmentRange = document.createRange();
      fragmentRange.setStart(textNode, startOffset);
      fragmentRange.setEnd(textNode, endOffset);

      // 创建高亮span
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-fragment-index", index.toString());

      // 使用surroundContents包装（这里应该是安全的，因为我们只处理文本片段）
      fragmentRange.surroundContents(span);

      console.log("[TextHighlighter] 创建文本片段高亮:", {
        text: span.textContent,
        fragmentIndex: index,
      });

      return span;
    } catch (error) {
      console.warn("[TextHighlighter] 包装文本片段失败:", error);

      // 降级处理：使用文本分割方法
      return this.fallbackWrapTextFragment(fragment, highlightId, index);
    }
  }

  /**
   * 降级处理：使用文本分割方法包装片段
   */
  private fallbackWrapTextFragment(
    fragment: TextFragment,
    highlightId: string,
    index: number
  ): HTMLElement | null {
    try {
      const { textNode, startOffset, endOffset } = fragment;
      const text = textNode.textContent || "";

      // 分割文本节点
      const beforeText = text.substring(0, startOffset);
      const highlightText = text.substring(startOffset, endOffset);
      const afterText = text.substring(endOffset);

      // 创建新的文本节点和高亮span
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-fragment-index", index.toString());
      span.textContent = highlightText;

      // 替换原始文本节点
      const parent = textNode.parentNode;
      if (parent) {
        // 插入前面的文本
        if (beforeText) {
          parent.insertBefore(document.createTextNode(beforeText), textNode);
        }

        // 插入高亮span
        parent.insertBefore(span, textNode);

        // 插入后面的文本
        if (afterText) {
          parent.insertBefore(document.createTextNode(afterText), textNode);
        }

        // 移除原始文本节点
        parent.removeChild(textNode);
      }

      return span;
    } catch (error) {
      console.error("[TextHighlighter] 降级文本包装失败:", error);
      return null;
    }
  }

  /**
   * 简单高亮降级方法
   */
  private fallbackToSimpleHighlight(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    try {
      // 尝试最简单的包装方法
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);

      range.surroundContents(span);
      return [span];
    } catch (error) {
      console.error("[TextHighlighter] 简单高亮降级也失败:", error);
      return [];
    }
  }
}
