import { ToolbarPosition } from "./types";

/**
 * 工具栏位置计算器
 * 负责计算工具栏的最佳显示位置
 */
export class PositionCalculator {
  // 工具栏尺寸预估
  private readonly TOOLBAR_WIDTH = 120;
  private readonly TOOLBAR_HEIGHT = 40;
  private readonly MIN_MARGIN = 10;

  /**
   * 基于元素边界计算工具栏位置（用于hover事件）
   * 工具栏右侧顶部对齐方式
   */
  public calculateToolbarPosition(rect: DOMRect): ToolbarPosition {
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const scrollY = window.scrollY || document.documentElement.scrollTop;
    const viewportWidth = window.innerWidth;

    // 默认位置：文本右侧，顶部对齐
    let x = rect.right + scrollX + 5; // 文本右边缘 + 5px间距（工具栏左边缘）
    let y = rect.top + scrollY; // 顶部对齐，无间距

    // 边界检测：如果工具栏超出右边界，显示在文本左侧
    if (x + this.TOOLBAR_WIDTH > viewportWidth + scrollX) {
      x = rect.left + scrollX - this.TOOLBAR_WIDTH - 5;
    }

    return { x, y };
  }

  /**
   * 基于鼠标位置计算工具栏位置（用于文本选择）
   */
  public calculateToolbarPositionFromMouse(
    mousePos: { x: number; y: number },
    selection: Selection
  ): ToolbarPosition {
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const scrollY = window.scrollY || document.documentElement.scrollTop;
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    // 基础位置：鼠标位置 + 偏移
    let x = mousePos.x + this.MIN_MARGIN; // 鼠标右侧
    let y = mousePos.y - this.TOOLBAR_HEIGHT; // 鼠标上方

    // 智能边界调整
    const adjustedPosition = this.adjustForBoundaries(
      { x, y },
      {
        mousePos,
        viewportWidth,
        viewportHeight,
        scrollX,
        scrollY,
      }
    );

    console.log("[PositionCalculator] 工具栏位置计算:", {
      mousePos,
      calculated: {
        x: adjustedPosition.x + scrollX,
        y: adjustedPosition.y + scrollY,
      },
      viewport: { width: viewportWidth, height: viewportHeight },
      scroll: { x: scrollX, y: scrollY },
    });

    return {
      x: adjustedPosition.x + scrollX,
      y: adjustedPosition.y + scrollY,
    };
  }

  /**
   * 智能边界调整
   */
  private adjustForBoundaries(
    position: { x: number; y: number },
    constraints: {
      mousePos: { x: number; y: number };
      viewportWidth: number;
      viewportHeight: number;
      scrollX: number;
      scrollY: number;
    }
  ): { x: number; y: number } {
    let { x, y } = position;
    const { mousePos, viewportWidth, viewportHeight } = constraints;

    // 右边界检测
    if (x + this.TOOLBAR_WIDTH > viewportWidth) {
      x = mousePos.x - this.TOOLBAR_WIDTH - this.MIN_MARGIN; // 显示在鼠标左侧
    }

    // 上边界检测
    if (y < 0) {
      y = mousePos.y + this.MIN_MARGIN; // 显示在鼠标下方
    }

    // 下边界检测
    if (y + this.TOOLBAR_HEIGHT > viewportHeight) {
      y = mousePos.y - this.TOOLBAR_HEIGHT - this.MIN_MARGIN; // 显示在鼠标上方
    }

    // 左边界检测
    if (x < 0) {
      x = this.MIN_MARGIN; // 最小边距
    }

    return { x, y };
  }

  /**
   * 检查位置是否在视口内
   */
  public isPositionValid(
    position: { x: number; y: number },
    viewport: { width: number; height: number }
  ): boolean {
    return (
      position.x >= 0 &&
      position.x + this.TOOLBAR_WIDTH <= viewport.width &&
      position.y >= 0 &&
      position.y + this.TOOLBAR_HEIGHT <= viewport.height
    );
  }

  /**
   * 获取候选位置列表（按优先级排序）
   */
  public getCandidatePositions(
    mousePos: { x: number; y: number },
    selectionRect?: DOMRect
  ): Array<{ x: number; y: number; priority: number }> {
    const candidates = [
      // 1. 鼠标右上方（默认首选）
      {
        x: mousePos.x + this.MIN_MARGIN,
        y: mousePos.y - this.TOOLBAR_HEIGHT,
        priority: 1,
      },
      // 2. 鼠标右下方
      {
        x: mousePos.x + this.MIN_MARGIN,
        y: mousePos.y + this.MIN_MARGIN,
        priority: 2,
      },
      // 3. 鼠标左上方
      {
        x: mousePos.x - this.TOOLBAR_WIDTH - this.MIN_MARGIN,
        y: mousePos.y - this.TOOLBAR_HEIGHT,
        priority: 3,
      },
      // 4. 鼠标左下方
      {
        x: mousePos.x - this.TOOLBAR_WIDTH - this.MIN_MARGIN,
        y: mousePos.y + this.MIN_MARGIN,
        priority: 4,
      },
    ];

    // 如果有选择范围信息，添加基于选择范围的候选位置
    if (selectionRect) {
      candidates.push({
        x: selectionRect.right + 5,
        y: selectionRect.bottom + 5,
        priority: 5, // 兜底方案
      });
    }

    return candidates.sort((a, b) => a.priority - b.priority);
  }

  /**
   * 智能选择最佳位置
   */
  public selectBestPosition(
    mousePos: { x: number; y: number },
    viewport: { width: number; height: number },
    selectionRect?: DOMRect
  ): { x: number; y: number } {
    const candidates = this.getCandidatePositions(mousePos, selectionRect);

    // 选择第一个不会超出视口的位置
    for (const candidate of candidates) {
      if (this.isPositionValid(candidate, viewport)) {
        return candidate;
      }
    }

    // 如果所有候选位置都不合适，使用调整后的鼠标位置
    return this.adjustForBoundaries(
      { x: mousePos.x, y: mousePos.y },
      {
        mousePos,
        viewportWidth: viewport.width,
        viewportHeight: viewport.height,
        scrollX: 0,
        scrollY: 0,
      }
    );
  }
}
