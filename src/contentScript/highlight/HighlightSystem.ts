import {
  HighlightItem,
  HighlightState,
  ToolbarPosition,
  SelectionRegion,
} from "./types";

/**
 * 高亮采集系统核心管理器
 */
export class HighlightSystem {
  private state: HighlightState;
  private toolbarContainer: HTMLElement | null = null;
  private notificationContainer: HTMLElement | null = null;

  constructor() {
    this.state = {
      items: [],
      toolbarVisible: false,
      toolbarPosition: { x: 0, y: 0 },
      toolbarMode: "add",
      notificationVisible: false,
    };

    this.init();

    // 添加到全局对象以便调试
    (window as any).highlightSystemInstance = this;

    // 添加测试方法
    (window as any).testHighlightSystem = () => {
      console.log("[HighlightSystem] 测试方法调用");
      this.showToolbar({ x: 100, y: 100 }, "add");
    };
  }

  /**
   * 初始化系统
   */
  private init(): void {
    console.log("[HighlightSystem] 开始初始化...");

    // 确保DOM已加载
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.doInit();
      });
    } else {
      this.doInit();
    }
  }

  /**
   * 执行实际初始化
   */
  private doInit(): void {
    console.log("[HighlightSystem] 执行实际初始化...");
    this.createContainers();
    this.bindEvents();
    this.loadStoredHighlights();
    console.log("[HighlightSystem] 初始化完成");
  }

  /**
   * 创建UI容器
   */
  private createContainers(): void {
    // 创建工具栏容器
    this.toolbarContainer = document.createElement("div");
    this.toolbarContainer.id = "quote-selection-toolbar-container";
    document.body.appendChild(this.toolbarContainer);

    // 创建通知栏容器
    this.notificationContainer = document.createElement("div");
    this.notificationContainer.id = "quote-notification-container";
    document.body.appendChild(this.notificationContainer);

    // 注入样式
    this.injectStyles();
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    const styleId = "quote-highlight-styles";
    if (document.getElementById(styleId)) return;

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* 高亮文本样式 */
      .quote-highlight {
        background-color: #fff3cd !important;
        border-radius: 2px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight:hover {
        background-color: #ffeaa7 !important;
      }

      /* 表格内高亮的特殊样式 */
      .quote-highlight-table {
        background-color: #fff3cd !important;
        display: inline !important;
        vertical-align: baseline !important;
        line-height: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        font-family: inherit !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        border-radius: 0 !important;
        position: static !important;
        width: auto !important;
        height: auto !important;
        max-width: none !important;
        max-height: none !important;
        min-width: 0 !important;
        min-height: 0 !important;
        box-sizing: content-box !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight-table:hover {
        background-color: #ffeaa7 !important;
      }

      /* 确保表格单元格保持原有样式 */
      table .quote-highlight-table,
      td .quote-highlight-table,
      th .quote-highlight-table {
        display: inline !important;
        vertical-align: baseline !important;
      }

      /* 表格内高亮的特殊样式 */
      .quote-highlight-table {
        display: inline !important;
        vertical-align: baseline !important;
        line-height: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background-color: #fff3cd !important;
        border-radius: 0 !important;
        position: relative !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight-table:hover {
        background-color: #ffeaa7 !important;
      }

      /* 确保表格单元格保持原有样式 */
      td .quote-highlight-table,
      th .quote-highlight-table {
        width: auto !important;
        height: auto !important;
        max-width: none !important;
        max-height: none !important;
        white-space: inherit !important;
      }

      /* 选区工具栏样式 */
      .quote-selection-toolbar {
        position: absolute !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        padding: 8px !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
      }

      .quote-selection-toolbar button {
        background: none !important;
        border: none !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-selection-toolbar button:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-selection-toolbar .quote-icon {
        width: 16px !important;
        height: 16px !important;
        opacity: 0.6 !important;
      }

      /* 底部通知栏样式 */
      .quote-notification-bar {
        position: fixed !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        padding: 12px 16px !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
        max-width: 400px !important;
        animation: slideUp 0.3s ease-out !important;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      .quote-notification-count {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        color: #333 !important;
      }

      .quote-count-badge {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: #007bff !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        min-width: 20px !important;
        height: 20px !important;
        border-radius: 10px !important;
      }

      .quote-count-badge.two-digit {
        border-radius: 10px !important;
        padding: 0 6px !important;
      }

      .quote-notification-actions {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
      }

      .quote-notification-btn {
        background: none !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        padding: 6px 12px !important;
        cursor: pointer !important;
        font-size: 12px !important;
        color: #666 !important;
        transition: all 0.2s ease !important;
      }

      .quote-notification-btn:hover {
        background-color: #f5f5f5 !important;
        border-color: #ccc !important;
      }

      .quote-notification-btn.danger {
        color: #dc3545 !important;
        border-color: #dc3545 !important;
      }

      .quote-notification-btn.danger:hover {
        background-color: #dc3545 !important;
        color: white !important;
      }

      /* 确认删除弹窗样式 */
      .quote-confirm-dialog {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 9999999 !important;
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }

      .quote-confirm-dialog-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(0, 0, 0, 0.5) !important;
        z-index: 9999998 !important;
      }

      .quote-confirm-dialog h3 {
        margin: 0 0 12px 0 !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #333 !important;
      }

      .quote-confirm-dialog p {
        margin: 0 0 20px 0 !important;
        font-size: 14px !important;
        color: #666 !important;
        line-height: 1.4 !important;
      }

      .quote-confirm-dialog-actions {
        display: flex !important;
        justify-content: flex-end !important;
        gap: 12px !important;
      }

      .quote-confirm-dialog-btn {
        padding: 8px 16px !important;
        border-radius: 6px !important;
        border: 1px solid #e0e0e0 !important;
        background: white !important;
        cursor: pointer !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
      }

      .quote-confirm-dialog-btn.primary {
        background: #dc3545 !important;
        color: white !important;
        border-color: #dc3545 !important;
      }

      .quote-confirm-dialog-btn:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-confirm-dialog-btn.primary:hover {
        background-color: #c82333 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 文本选择事件
    document.addEventListener("mouseup", this.handleMouseUp.bind(this));
    document.addEventListener("keyup", this.handleKeyUp.bind(this));

    // 滚动和点击事件（隐藏工具栏）
    document.addEventListener("scroll", this.hideToolbar.bind(this));
    document.addEventListener("click", this.handleDocumentClick.bind(this));

    // 高亮文本hover事件
    document.addEventListener("mouseover", this.handleMouseOver.bind(this));
    document.addEventListener("mouseout", this.handleMouseOut.bind(this));
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp(_event: MouseEvent): void {
    setTimeout(() => {
      const selection = window.getSelection();
      console.log("[HighlightSystem] 鼠标抬起，检查选择:", {
        hasSelection: !!selection,
        isCollapsed: selection?.isCollapsed,
        selectedText: selection?.toString(),
      });

      if (!selection || selection.isCollapsed) {
        this.hideToolbar();
        return;
      }

      const selectedText = selection.toString().trim();
      if (selectedText.length === 0) {
        this.hideToolbar();
        return;
      }

      // 计算工具栏位置
      const range = selection.getRangeAt(0);
      const rect = range.getBoundingClientRect();
      const position = this.calculateToolbarPosition(rect);

      console.log("[HighlightSystem] 显示工具栏:", {
        selectedText,
        position,
        rect: { x: rect.x, y: rect.y, width: rect.width, height: rect.height },
      });

      this.showToolbar(position, "add");
    }, 10);
  }

  /**
   * 处理键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    if (event.key === "Escape") {
      this.hideToolbar();
    }
  }

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    // 如果点击的是工具栏或通知栏，不隐藏
    if (
      target.closest("#quote-selection-toolbar-container") ||
      target.closest("#quote-notification-container")
    ) {
      return;
    }

    this.hideToolbar();
  }

  /**
   * 处理鼠标悬停事件
   */
  private handleMouseOver(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (target.classList.contains("quote-highlight")) {
      const highlightId = target.getAttribute("data-highlight-id");
      if (highlightId) {
        const rect = target.getBoundingClientRect();
        const position = this.calculateToolbarPosition(rect);
        this.showToolbar(position, "remove", highlightId);
      }
    }
  }

  /**
   * 处理鼠标离开事件
   */
  private handleMouseOut(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const relatedTarget = event.relatedTarget as HTMLElement;

    // 如果鼠标移动到工具栏，不隐藏
    if (
      relatedTarget &&
      relatedTarget.closest("#quote-selection-toolbar-container")
    ) {
      return;
    }

    if (target.classList.contains("quote-highlight")) {
      // 延迟隐藏，给用户时间移动到工具栏
      setTimeout(() => {
        if (!this.isMouseOverToolbar()) {
          this.hideToolbar();
        }
      }, 100);
    }
  }

  /**
   * 检查鼠标是否在工具栏上
   */
  private isMouseOverToolbar(): boolean {
    // 简化实现，实际使用中可以通过事件参数传递鼠标位置
    const toolbar = document.querySelector(
      "#quote-selection-toolbar-container .quote-selection-toolbar"
    );
    return !!toolbar;
  }

  /**
   * 计算工具栏位置
   */
  private calculateToolbarPosition(rect: DOMRect): ToolbarPosition {
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const scrollY = window.scrollY || document.documentElement.scrollTop;

    return {
      x: rect.right + scrollX + 5,
      y: rect.bottom + scrollY + 5,
    };
  }

  /**
   * 显示工具栏
   */
  public showToolbar(
    position: ToolbarPosition,
    mode: "add" | "remove",
    targetHighlightId?: string
  ): void {
    console.log("[HighlightSystem] showToolbar 调用:", {
      position,
      mode,
      targetHighlightId,
      toolbarContainer: !!this.toolbarContainer,
    });

    this.state.toolbarVisible = true;
    this.state.toolbarPosition = position;
    this.state.toolbarMode = mode;
    this.state.targetHighlightId = targetHighlightId;

    this.renderToolbar();
  }

  /**
   * 隐藏工具栏
   */
  private hideToolbar(): void {
    this.state.toolbarVisible = false;
    this.renderToolbar();
  }

  /**
   * 渲染工具栏
   */
  private renderToolbar(): void {
    console.log("[HighlightSystem] renderToolbar 调用:", {
      hasToolbarContainer: !!this.toolbarContainer,
      toolbarVisible: this.state.toolbarVisible,
      position: this.state.toolbarPosition,
      mode: this.state.toolbarMode,
    });

    if (!this.toolbarContainer) {
      console.error("[HighlightSystem] toolbarContainer 不存在！");
      return;
    }

    // 清空容器
    this.toolbarContainer.innerHTML = "";

    if (!this.state.toolbarVisible) {
      console.log("[HighlightSystem] 工具栏隐藏状态，清空容器");
      return;
    }

    // 直接创建DOM元素而不使用React
    const toolbar = document.createElement("div");
    toolbar.className = "quote-selection-toolbar";
    toolbar.style.left = `${this.state.toolbarPosition.x}px`;
    toolbar.style.top = `${this.state.toolbarPosition.y}px`;

    // 创建按钮
    const button = document.createElement("button");
    button.title = this.state.toolbarMode === "add" ? "高亮采集" : "取消高亮";

    if (this.state.toolbarMode === "add") {
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2v-4" />
          <path d="M14 3l7 7-10 10H4v-7L14 3z" />
        </svg>
      `;
      button.onclick = this.handleHighlight.bind(this);
    } else {
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18" />
          <path d="M6 6l12 12" />
        </svg>
      `;
      button.onclick = this.handleRemoveHighlight.bind(this);
    }

    // 创建装饰图标
    const icon = document.createElement("div");
    icon.className = "quote-icon";
    icon.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
        <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
      </svg>
    `;

    toolbar.appendChild(button);
    toolbar.appendChild(icon);
    this.toolbarContainer.appendChild(toolbar);

    console.log("[HighlightSystem] 工具栏DOM渲染完成");
  }

  /**
   * 渲染通知栏
   */
  private renderNotification(): void {
    if (!this.notificationContainer) return;

    // 清空容器
    this.notificationContainer.innerHTML = "";

    if (!this.state.notificationVisible) {
      return;
    }

    // 直接创建DOM元素
    const notificationBar = document.createElement("div");
    notificationBar.className = "quote-notification-bar";

    // 创建计数部分
    const countSection = document.createElement("div");
    countSection.className = "quote-notification-count";

    const badge = document.createElement("div");
    const count = this.state.items.length;
    badge.className =
      count >= 10 ? "quote-count-badge two-digit" : "quote-count-badge";
    badge.textContent = count > 99 ? "99+" : count.toString();

    const text = document.createElement("span");
    text.textContent = "已采集到知识库";

    countSection.appendChild(badge);
    countSection.appendChild(text);

    // 创建操作按钮部分
    const actionsSection = document.createElement("div");
    actionsSection.className = "quote-notification-actions";

    const clearBtn = document.createElement("button");
    clearBtn.className = "quote-notification-btn danger";
    clearBtn.textContent = "清除";
    clearBtn.onclick = () => {
      // 简化版确认对话框
      if (confirm("确定要清除页面中所有高亮文本吗？此操作不可撤销。")) {
        this.handleClearAll();
      }
    };

    const projectBtn = document.createElement("button");
    projectBtn.className = "quote-notification-btn";
    projectBtn.textContent = "选择project";
    projectBtn.onclick = this.handleSelectProject.bind(this);

    actionsSection.appendChild(clearBtn);
    actionsSection.appendChild(projectBtn);

    notificationBar.appendChild(countSection);
    notificationBar.appendChild(actionsSection);
    this.notificationContainer.appendChild(notificationBar);
  }

  /**
   * 处理高亮操作 - 支持跨元素边界
   */
  private handleHighlight(): void {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (selectedText.length === 0) return;

    const range = selection.getRangeAt(0);
    const highlightId = this.generateId();

    try {
      const highlightedElements = this.createHighlightSpans(range, highlightId);

      if (highlightedElements.length === 0) {
        console.warn("[HighlightSystem] 无法创建高亮元素");
        return;
      }

      // 保存到状态 - 使用第一个元素作为主要引用
      const item: HighlightItem = {
        id: highlightId,
        text: selectedText,
        element: highlightedElements[0], // 主要元素
        elements: highlightedElements, // 所有相关元素
        timestamp: Date.now(),
        pageUrl: window.location.href,
      };

      this.state.items.push(item);
      this.saveToStorage();
      this.updateNotificationVisibility();
      this.hideToolbar();
      selection.removeAllRanges();

      console.log("[HighlightSystem] 添加高亮:", {
        text: selectedText,
        elementsCount: highlightedElements.length,
      });
    } catch (error) {
      console.error("[HighlightSystem] 高亮失败:", error);
    }
  }

  /**
   * 创建高亮span元素 - 智能分区域处理
   */
  private createHighlightSpans(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    const highlightedElements: HTMLElement[] = [];

    // 方法1: 尝试简单的surroundContents（适用于单一文本节点）
    if (this.canUseSurroundContents(range)) {
      try {
        const span = document.createElement("span");
        // 检查是否在表格内
        const isInTable = this.isRangeInTable(range);
        span.className = isInTable
          ? "quote-highlight quote-highlight-table"
          : "quote-highlight";
        span.setAttribute("data-highlight-id", highlightId);

        if (isInTable) {
          this.applyTableSafeStyles(span);
        }

        range.surroundContents(span);
        return [span];
      } catch (error) {
        console.log("[HighlightSystem] surroundContents失败，使用分区域方法");
      }
    }

    // 方法2: 智能分区域处理
    const regions = this.analyzeSelectionRegions(range);

    console.log(
      "[HighlightSystem] 分析到",
      regions.length,
      "个区域:",
      regions.map((r) => r.type)
    );

    regions.forEach((region, regionIndex) => {
      let regionElements: HTMLElement[] = [];

      if (region.type === "table") {
        // 表格区域：使用表格专用算法
        regionElements = this.createTableRegionHighlight(
          region.range,
          highlightId,
          regionIndex
        );
      } else {
        // 普通区域：使用标准算法
        regionElements = this.createNormalRegionHighlight(
          region.range,
          highlightId,
          regionIndex
        );
      }

      highlightedElements.push(...regionElements);
    });

    return highlightedElements;
  }

  /**
   * 分析选择范围，识别不同类型的区域
   */
  private analyzeSelectionRegions(range: Range): SelectionRegion[] {
    const regions: SelectionRegion[] = [];
    const textNodes = this.getTextNodesInRange(range);

    let currentRegion: SelectionRegion | null = null;

    textNodes.forEach((textNode) => {
      const nodeRegionType = this.getNodeRegionType(textNode);

      if (!currentRegion || currentRegion.type !== nodeRegionType) {
        // 开始新区域
        if (currentRegion) {
          regions.push(currentRegion);
        }

        currentRegion = {
          type: nodeRegionType,
          nodes: [textNode],
          range: document.createRange(),
        };
        currentRegion.range.setStartBefore(textNode);
      } else {
        // 继续当前区域
        currentRegion.nodes.push(textNode);
      }

      if (currentRegion) {
        currentRegion.range.setEndAfter(textNode);
      }
    });

    if (currentRegion) {
      regions.push(currentRegion);
    }

    return regions;
  }

  /**
   * 确定文本节点所属的区域类型
   */
  private getNodeRegionType(textNode: Text): "table" | "normal" {
    // 检查是否在表格内
    let parent = textNode.parentNode;
    while (parent && parent !== document.body) {
      if (parent.nodeName === "TABLE") {
        return "table";
      }
      parent = parent.parentNode;
    }
    return "normal";
  }

  /**
   * 检查范围是否在表格内
   */
  private isRangeInTable(range: Range): boolean {
    let container: Node | null = range.startContainer;
    while (container && container !== document.body) {
      if (container.nodeName === "TABLE") {
        return true;
      }
      container = container.parentNode;
    }
    return false;
  }

  /**
   * 为表格内的span应用安全样式
   */
  private applyTableSafeStyles(span: HTMLElement): void {
    span.style.display = "inline";
    span.style.verticalAlign = "baseline";
    span.style.lineHeight = "inherit";
    span.style.margin = "0";
    span.style.padding = "0";
    span.style.border = "none";
    span.style.borderRadius = "0";
    span.style.position = "static";
  }

  /**
   * 表格区域专用高亮
   */
  private createTableRegionHighlight(
    range: Range,
    highlightId: string,
    regionIndex: number
  ): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const textNodes = this.getTextNodesInRange(range);

    textNodes.forEach((textNode, nodeIndex) => {
      const nodeRange = this.getIntersectionRange(range, textNode);
      if (nodeRange && !nodeRange.collapsed) {
        const span = this.createTableSafeSpan(
          nodeRange,
          highlightId,
          regionIndex,
          nodeIndex
        );
        if (span) {
          elements.push(span);
        }
      }
    });

    return elements;
  }

  /**
   * 创建表格安全的span元素
   */
  private createTableSafeSpan(
    range: Range,
    highlightId: string,
    regionIndex: number,
    nodeIndex: number
  ): HTMLElement | null {
    try {
      const span = document.createElement("span");
      span.className = "quote-highlight quote-highlight-table";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-highlight-region", regionIndex.toString());
      span.setAttribute("data-highlight-part", nodeIndex.toString());

      // 应用表格安全样式
      this.applyTableSafeStyles(span);

      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);

      return span;
    } catch (error) {
      console.warn("[HighlightSystem] 表格安全span创建失败:", error);
      return null;
    }
  }

  /**
   * 普通区域高亮（保持原有逻辑）
   */
  private createNormalRegionHighlight(
    range: Range,
    highlightId: string,
    regionIndex: number
  ): HTMLElement[] {
    const elements: HTMLElement[] = [];
    const textNodes = this.getTextNodesInRange(range);

    textNodes.forEach((textNode, nodeIndex) => {
      const nodeRange = this.getIntersectionRange(range, textNode);
      if (nodeRange && !nodeRange.collapsed) {
        const span = this.createNormalSpan(
          nodeRange,
          highlightId,
          regionIndex,
          nodeIndex
        );
        if (span) {
          elements.push(span);
        }
      }
    });

    return elements;
  }

  /**
   * 创建普通span元素
   */
  private createNormalSpan(
    range: Range,
    highlightId: string,
    regionIndex: number,
    nodeIndex: number
  ): HTMLElement | null {
    try {
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-highlight-region", regionIndex.toString());
      span.setAttribute("data-highlight-part", nodeIndex.toString());

      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);

      return span;
    } catch (error) {
      console.warn("[HighlightSystem] 普通span创建失败:", error);
      return null;
    }
  }

  /**
   * 检查选择范围是否涉及表格元素
   */
  private isTableSelection(range: Range): boolean {
    const commonAncestor = range.commonAncestorContainer;

    // 检查是否在表格内
    const tableElement = this.findAncestorByTagName(commonAncestor, "TABLE");
    if (!tableElement) return false;

    // 检查是否跨越多个单元格
    const startCell =
      this.findAncestorByTagName(range.startContainer, "TD") ||
      this.findAncestorByTagName(range.startContainer, "TH");
    const endCell =
      this.findAncestorByTagName(range.endContainer, "TD") ||
      this.findAncestorByTagName(range.endContainer, "TH");

    console.log("[HighlightSystem] 表格选择检测:", {
      hasTable: !!tableElement,
      startCell: startCell?.tagName,
      endCell: endCell?.tagName,
      isCrossCell: startCell !== endCell,
    });

    return startCell !== endCell; // 跨单元格选择
  }

  /**
   * 查找指定标签名的祖先元素
   */
  private findAncestorByTagName(node: Node, tagName: string): Element | null {
    let current = node.nodeType === Node.TEXT_NODE ? node.parentNode : node;

    while (current && current.nodeType === Node.ELEMENT_NODE) {
      if ((current as Element).tagName === tagName) {
        return current as Element;
      }
      current = current.parentNode;
    }

    return null;
  }

  /**
   * 表格专用高亮方法
   */
  private createTableHighlight(
    range: Range,
    highlightId: string
  ): HTMLElement[] {
    const highlightedElements: HTMLElement[] = [];

    try {
      // 获取所有涉及的表格单元格
      const cells = this.getCellsInRange(range);
      console.log("[HighlightSystem] 表格单元格数量:", cells.length);

      cells.forEach((cell, index) => {
        const cellRange = this.getIntersectionRangeForCell(range, cell);
        if (cellRange && !cellRange.collapsed) {
          // 为每个单元格创建高亮，但保持单元格结构不变
          const span = this.createCellHighlight(cellRange, highlightId, index);
          if (span) {
            highlightedElements.push(span);
          }
        }
      });
    } catch (error) {
      console.error("[HighlightSystem] 表格高亮创建失败:", error);
    }

    return highlightedElements;
  }

  /**
   * 获取范围内的所有表格单元格
   */
  private getCellsInRange(range: Range): Element[] {
    const cells: Element[] = [];
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_ELEMENT,
      {
        acceptNode: (node) => {
          const element = node as Element;
          if (element.tagName === "TD" || element.tagName === "TH") {
            try {
              return range.intersectsNode(element)
                ? NodeFilter.FILTER_ACCEPT
                : NodeFilter.FILTER_REJECT;
            } catch {
              return NodeFilter.FILTER_REJECT;
            }
          }
          return NodeFilter.FILTER_SKIP;
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      cells.push(node as Element);
    }

    return cells;
  }

  /**
   * 获取范围与单元格的交集
   */
  private getIntersectionRangeForCell(
    range: Range,
    cell: Element
  ): Range | null {
    try {
      const cellRange = document.createRange();
      cellRange.selectNodeContents(cell);

      const intersection = range.cloneRange();

      // 调整起点
      if (
        intersection.compareBoundaryPoints(Range.START_TO_START, cellRange) < 0
      ) {
        intersection.setStart(cellRange.startContainer, cellRange.startOffset);
      }

      // 调整终点
      if (intersection.compareBoundaryPoints(Range.END_TO_END, cellRange) > 0) {
        intersection.setEnd(cellRange.endContainer, cellRange.endOffset);
      }

      return intersection.collapsed ? null : intersection;
    } catch {
      return null;
    }
  }

  /**
   * 创建单元格内的高亮，保持表格结构
   */
  private createCellHighlight(
    range: Range,
    highlightId: string,
    partIndex: number
  ): HTMLElement | null {
    try {
      // 确保只在单元格内部操作，不破坏表格结构
      const span = document.createElement("span");
      span.className = "quote-highlight quote-highlight-table";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-highlight-part", partIndex.toString());

      // 使用更保守的包装方式
      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);

      console.log("[HighlightSystem] 创建表格单元格高亮:", {
        partIndex,
        textContent: span.textContent,
      });

      return span;
    } catch (error) {
      console.warn("[HighlightSystem] 表格单元格高亮失败:", error);
      return null;
    }
  }

  /**
   * 检查是否可以使用简单的surroundContents方法
   */
  private canUseSurroundContents(range: Range): boolean {
    try {
      // 检查起点和终点是否在同一个文本节点
      if (
        range.startContainer === range.endContainer &&
        range.startContainer.nodeType === Node.TEXT_NODE
      ) {
        return true;
      }

      // 检查是否只包含一个完整的元素
      if (
        range.startContainer === range.endContainer &&
        range.startOffset === 0 &&
        range.endOffset === (range.startContainer as Element).childNodes?.length
      ) {
        return true;
      }

      return false;
    } catch {
      return false;
    }
  }

  /**
   * 获取范围内的所有文本节点
   */
  private getTextNodesInRange(range: Range): Text[] {
    const textNodes: Text[] = [];
    const walker = document.createTreeWalker(
      range.commonAncestorContainer,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          try {
            // 检查节点是否与范围相交
            return range.intersectsNode(node)
              ? NodeFilter.FILTER_ACCEPT
              : NodeFilter.FILTER_REJECT;
          } catch {
            return NodeFilter.FILTER_REJECT;
          }
        },
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      const textNode = node as Text;
      // 额外检查：确保文本节点确实在范围内
      if (this.isTextNodeInRange(textNode, range)) {
        textNodes.push(textNode);
      }
    }

    return textNodes;
  }

  /**
   * 检查文本节点是否在范围内
   */
  private isTextNodeInRange(textNode: Text, range: Range): boolean {
    try {
      const nodeRange = document.createRange();
      nodeRange.selectNodeContents(textNode);

      // 检查是否有交集
      return (
        range.compareBoundaryPoints(Range.END_TO_START, nodeRange) <= 0 &&
        range.compareBoundaryPoints(Range.START_TO_END, nodeRange) >= 0
      );
    } catch {
      return false;
    }
  }

  /**
   * 获取范围与文本节点的交集
   */
  private getIntersectionRange(range: Range, textNode: Text): Range | null {
    try {
      const nodeRange = document.createRange();
      nodeRange.selectNodeContents(textNode);

      const intersection = range.cloneRange();

      // 调整起点
      if (
        intersection.compareBoundaryPoints(Range.START_TO_START, nodeRange) < 0
      ) {
        intersection.setStart(nodeRange.startContainer, nodeRange.startOffset);
      }

      // 调整终点
      if (intersection.compareBoundaryPoints(Range.END_TO_END, nodeRange) > 0) {
        intersection.setEnd(nodeRange.endContainer, nodeRange.endOffset);
      }

      return intersection.collapsed ? null : intersection;
    } catch {
      return null;
    }
  }

  /**
   * 用span包装范围
   */
  private wrapRangeWithSpan(
    range: Range,
    highlightId: string,
    partIndex: number
  ): HTMLElement | null {
    try {
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);
      span.setAttribute("data-highlight-part", partIndex.toString());

      // 使用extractContents + insertNode的方式
      const contents = range.extractContents();
      span.appendChild(contents);
      range.insertNode(span);

      return span;
    } catch (error) {
      console.warn("[HighlightSystem] 无法包装范围:", error);
      return null;
    }
  }

  /**
   * 处理移除高亮操作 - 支持多元素高亮
   */
  private handleRemoveHighlight(): void {
    if (!this.state.targetHighlightId) return;

    const itemIndex = this.state.items.findIndex(
      (item) => item.id === this.state.targetHighlightId
    );
    if (itemIndex === -1) return;

    const item = this.state.items[itemIndex];

    // 移除所有相关的高亮元素
    const elementsToRemove = item.elements || [item.element];

    elementsToRemove.forEach((element, index) => {
      const parent = element.parentNode;
      if (parent) {
        // 获取元素的文本内容
        const textContent = element.textContent || "";
        parent.replaceChild(document.createTextNode(textContent), element);

        // 只在最后一个元素处理完后进行normalize，避免影响其他元素的处理
        if (index === elementsToRemove.length - 1) {
          parent.normalize(); // 合并相邻的文本节点
        }
      }
    });

    // 从状态中移除
    this.state.items.splice(itemIndex, 1);
    this.saveToStorage();

    // 更新UI
    this.updateNotificationVisibility();
    this.hideToolbar();

    console.log("[HighlightSystem] 移除高亮:", {
      text: item.text,
      elementsCount: elementsToRemove.length,
    });
  }

  /**
   * 处理清除所有高亮 - 支持多元素高亮
   */
  private handleClearAll(): void {
    this.state.items.forEach((item) => {
      const elementsToRemove = item.elements || [item.element];

      elementsToRemove.forEach((element) => {
        const parent = element.parentNode;
        if (parent) {
          const textContent = element.textContent || "";
          parent.replaceChild(document.createTextNode(textContent), element);
        }
      });
    });

    // 在所有元素移除后统一进行normalize
    const processedParents = new Set<Node>();
    this.state.items.forEach((item) => {
      const elementsToRemove = item.elements || [item.element];
      elementsToRemove.forEach((element) => {
        if (element.parentNode && !processedParents.has(element.parentNode)) {
          element.parentNode.normalize();
          processedParents.add(element.parentNode);
        }
      });
    });

    this.state.items = [];
    this.saveToStorage();
    this.updateNotificationVisibility();

    console.log("[HighlightSystem] 清除所有高亮");
  }

  /**
   * 处理选择项目（暂时只是占位）
   */
  private handleSelectProject(): void {
    console.log("[HighlightSystem] 选择项目功能暂未实现");
    // TODO: 实现项目选择功能
  }

  /**
   * 更新通知栏可见性
   */
  private updateNotificationVisibility(): void {
    this.state.notificationVisible = this.state.items.length > 0;
    this.renderNotification();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `highlight_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage(): void {
    const data = this.state.items.map((item) => ({
      id: item.id,
      text: item.text,
      timestamp: item.timestamp,
      pageUrl: item.pageUrl,
    }));

    localStorage.setItem("quote_highlights", JSON.stringify(data));
  }

  /**
   * 从本地存储加载
   */
  private loadStoredHighlights(): void {
    try {
      const stored = localStorage.getItem("quote_highlights");
      if (!stored) return;

      const data = JSON.parse(stored);
      // 注意：页面刷新后DOM元素会丢失，这里只是演示
      // 实际应用中需要更复杂的恢复机制
      console.log("[HighlightSystem] 加载存储的高亮数据:", data);
    } catch (error) {
      console.error("[HighlightSystem] 加载存储数据失败:", error);
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 移除事件监听器
    document.removeEventListener("mouseup", this.handleMouseUp.bind(this));
    document.removeEventListener("keyup", this.handleKeyUp.bind(this));
    document.removeEventListener("scroll", this.hideToolbar.bind(this));
    document.removeEventListener("click", this.handleDocumentClick.bind(this));
    document.removeEventListener("mouseover", this.handleMouseOver.bind(this));
    document.removeEventListener("mouseout", this.handleMouseOut.bind(this));

    // 清理DOM
    if (this.toolbarContainer) {
      this.toolbarContainer.remove();
    }
    if (this.notificationContainer) {
      this.notificationContainer.remove();
    }

    console.log("[HighlightSystem] 系统已销毁");
  }
}
