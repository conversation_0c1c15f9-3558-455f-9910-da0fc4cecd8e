import { HighlightItem, HighlightState, ToolbarPosition } from "./types";

/**
 * 高亮采集系统核心管理器
 */
export class HighlightSystem {
  private state: HighlightState;
  private toolbarContainer: HTMLElement | null = null;
  private notificationContainer: HTMLElement | null = null;

  // 性能优化相关
  private selectionDebounceTimer: number | null = null;
  private renderDebounceTimer: number | null = null;
  private lastSelectionText: string = "";
  private isRendering: boolean = false;
  private lastHoverTime: number = 0;
  private hoverThrottleDelay: number = 100; // 100ms节流延迟
  private saveDebounceTimer: number | null = null;
  private saveDebounceDelay: number = 300; // 300ms存储防抖延迟

  constructor() {
    this.state = {
      items: [],
      toolbarVisible: false,
      toolbarPosition: { x: 0, y: 0 },
      toolbarMode: "add",
      notificationVisible: false,
    };

    this.init();

    // 添加到全局对象以便调试
    (window as any).highlightSystemInstance = this;

    // 添加测试方法
    (window as any).testHighlightSystem = () => {
      console.log("[HighlightSystem] 测试方法调用");
      this.showToolbar({ x: 100, y: 100 }, "add");
    };
  }

  /**
   * 初始化系统
   */
  private init(): void {
    console.log("[HighlightSystem] 开始初始化...");

    // 确保DOM已加载
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.doInit();
      });
    } else {
      this.doInit();
    }
  }

  /**
   * 执行实际初始化
   */
  private doInit(): void {
    console.log("[HighlightSystem] 执行实际初始化...");
    this.createContainers();
    this.bindEvents();
    this.loadStoredHighlights();
    console.log("[HighlightSystem] 初始化完成");
  }

  /**
   * 创建UI容器
   */
  private createContainers(): void {
    // 创建工具栏容器
    this.toolbarContainer = document.createElement("div");
    this.toolbarContainer.id = "quote-selection-toolbar-container";
    document.body.appendChild(this.toolbarContainer);

    // 创建通知栏容器
    this.notificationContainer = document.createElement("div");
    this.notificationContainer.id = "quote-notification-container";
    document.body.appendChild(this.notificationContainer);

    // 注入样式
    this.injectStyles();
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    const styleId = "quote-highlight-styles";
    if (document.getElementById(styleId)) return;

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* 高亮文本样式 */
      .quote-highlight {
        background-color: #fff3cd !important;
        border-radius: 2px !important;
        padding: 1px 2px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight:hover {
        background-color: #ffeaa7 !important;
      }

      /* 选区工具栏样式 */
      .quote-selection-toolbar {
        position: absolute !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        padding: 8px !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
      }

      .quote-selection-toolbar button {
        background: none !important;
        border: none !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-selection-toolbar button:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-selection-toolbar .quote-icon {
        width: 16px !important;
        height: 16px !important;
        opacity: 0.6 !important;
      }

      /* 底部通知栏样式 */
      .quote-notification-bar {
        position: fixed !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        padding: 12px 16px !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
        max-width: 400px !important;
        animation: slideUp 0.3s ease-out !important;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      .quote-notification-count {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        color: #333 !important;
      }

      .quote-count-badge {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: #007bff !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        min-width: 20px !important;
        height: 20px !important;
        border-radius: 10px !important;
      }

      .quote-count-badge.two-digit {
        border-radius: 10px !important;
        padding: 0 6px !important;
      }

      .quote-notification-actions {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
      }

      .quote-notification-btn {
        background: none !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        padding: 6px 12px !important;
        cursor: pointer !important;
        font-size: 12px !important;
        color: #666 !important;
        transition: all 0.2s ease !important;
      }

      .quote-notification-btn:hover {
        background-color: #f5f5f5 !important;
        border-color: #ccc !important;
      }

      .quote-notification-btn.danger {
        color: #dc3545 !important;
        border-color: #dc3545 !important;
      }

      .quote-notification-btn.danger:hover {
        background-color: #dc3545 !important;
        color: white !important;
      }

      /* 确认删除弹窗样式 */
      .quote-confirm-dialog {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 9999999 !important;
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }

      .quote-confirm-dialog-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(0, 0, 0, 0.5) !important;
        z-index: 9999998 !important;
      }

      .quote-confirm-dialog h3 {
        margin: 0 0 12px 0 !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #333 !important;
      }

      .quote-confirm-dialog p {
        margin: 0 0 20px 0 !important;
        font-size: 14px !important;
        color: #666 !important;
        line-height: 1.4 !important;
      }

      .quote-confirm-dialog-actions {
        display: flex !important;
        justify-content: flex-end !important;
        gap: 12px !important;
      }

      .quote-confirm-dialog-btn {
        padding: 8px 16px !important;
        border-radius: 6px !important;
        border: 1px solid #e0e0e0 !important;
        background: white !important;
        cursor: pointer !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
      }

      .quote-confirm-dialog-btn.primary {
        background: #dc3545 !important;
        color: white !important;
        border-color: #dc3545 !important;
      }

      .quote-confirm-dialog-btn:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-confirm-dialog-btn.primary:hover {
        background-color: #c82333 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 文本选择事件
    document.addEventListener("mouseup", this.handleMouseUp.bind(this));
    document.addEventListener("keyup", this.handleKeyUp.bind(this));

    // 滚动和点击事件（隐藏工具栏）
    document.addEventListener("scroll", this.hideToolbar.bind(this));
    document.addEventListener("click", this.handleDocumentClick.bind(this));

    // 高亮文本hover事件
    document.addEventListener("mouseover", this.handleMouseOver.bind(this));
    document.addEventListener("mouseout", this.handleMouseOut.bind(this));
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp(_event: MouseEvent): void {
    // 清除之前的防抖定时器
    if (this.selectionDebounceTimer) {
      clearTimeout(this.selectionDebounceTimer);
    }

    // 使用防抖处理，避免频繁触发
    this.selectionDebounceTimer = window.setTimeout(() => {
      this.processSelection();
    }, 150); // 150ms防抖延迟
  }

  /**
   * 处理文本选择逻辑
   */
  private processSelection(): void {
    const selection = window.getSelection();

    if (!selection || selection.isCollapsed) {
      this.hideToolbar();
      return;
    }

    const selectedText = selection.toString().trim();
    if (selectedText.length === 0) {
      this.hideToolbar();
      return;
    }

    // 检查是否与上次选择的文本相同，避免重复处理
    if (selectedText === this.lastSelectionText) {
      return;
    }
    this.lastSelectionText = selectedText;

    console.log("[HighlightSystem] 处理文本选择:", {
      selectedText:
        selectedText.substring(0, 50) + (selectedText.length > 50 ? "..." : ""),
      textLength: selectedText.length,
    });

    // 计算工具栏位置
    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();
    const position = this.calculateToolbarPosition(rect);

    this.showToolbar(position, "add");
  }

  /**
   * 处理键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    if (event.key === "Escape") {
      this.hideToolbar();
    }
  }

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    // 如果点击的是工具栏或通知栏，不隐藏
    if (
      target.closest("#quote-selection-toolbar-container") ||
      target.closest("#quote-notification-container")
    ) {
      return;
    }

    this.hideToolbar();
  }

  /**
   * 处理鼠标悬停事件（带节流优化）
   */
  private handleMouseOver(event: MouseEvent): void {
    const now = Date.now();

    // 节流处理，避免频繁触发
    if (now - this.lastHoverTime < this.hoverThrottleDelay) {
      return;
    }
    this.lastHoverTime = now;

    const target = event.target as HTMLElement;
    if (target.classList.contains("quote-highlight")) {
      const highlightId = target.getAttribute("data-highlight-id");
      if (highlightId) {
        const rect = target.getBoundingClientRect();
        const position = this.calculateToolbarPosition(rect);
        this.showToolbar(position, "remove", highlightId);
      }
    }
  }

  /**
   * 处理鼠标离开事件
   */
  private handleMouseOut(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const relatedTarget = event.relatedTarget as HTMLElement;

    // 如果鼠标移动到工具栏，不隐藏
    if (
      relatedTarget &&
      relatedTarget.closest("#quote-selection-toolbar-container")
    ) {
      return;
    }

    if (target.classList.contains("quote-highlight")) {
      // 延迟隐藏，给用户时间移动到工具栏
      setTimeout(() => {
        if (!this.isMouseOverToolbar()) {
          this.hideToolbar();
        }
      }, 100);
    }
  }

  /**
   * 检查鼠标是否在工具栏上
   */
  private isMouseOverToolbar(): boolean {
    // 简化实现，实际使用中可以通过事件参数传递鼠标位置
    const toolbar = document.querySelector(
      "#quote-selection-toolbar-container .quote-selection-toolbar"
    );
    return !!toolbar;
  }

  /**
   * 计算工具栏位置
   */
  private calculateToolbarPosition(rect: DOMRect): ToolbarPosition {
    const scrollX = window.scrollX || document.documentElement.scrollLeft;
    const scrollY = window.scrollY || document.documentElement.scrollTop;

    return {
      x: rect.right + scrollX + 5,
      y: rect.bottom + scrollY + 5,
    };
  }

  /**
   * 显示工具栏
   */
  public showToolbar(
    position: ToolbarPosition,
    mode: "add" | "remove",
    targetHighlightId?: string
  ): void {
    console.log("[HighlightSystem] showToolbar 调用:", {
      position,
      mode,
      targetHighlightId,
      toolbarContainer: !!this.toolbarContainer,
    });

    this.state.toolbarVisible = true;
    this.state.toolbarPosition = position;
    this.state.toolbarMode = mode;
    this.state.targetHighlightId = targetHighlightId;

    this.renderToolbar();
  }

  /**
   * 隐藏工具栏
   */
  private hideToolbar(): void {
    this.state.toolbarVisible = false;
    this.renderToolbar();
  }

  /**
   * 渲染工具栏（带防抖优化）
   */
  private renderToolbar(): void {
    // 清除之前的渲染防抖定时器
    if (this.renderDebounceTimer) {
      clearTimeout(this.renderDebounceTimer);
    }

    // 使用防抖处理，避免频繁渲染
    this.renderDebounceTimer = window.setTimeout(() => {
      this.doRenderToolbar();
    }, 50); // 50ms防抖延迟
  }

  /**
   * 执行实际的工具栏渲染
   */
  private doRenderToolbar(): void {
    // 防止重复渲染
    if (this.isRendering) {
      return;
    }
    this.isRendering = true;

    try {
      if (!this.toolbarContainer) {
        console.error("[HighlightSystem] toolbarContainer 不存在！");
        return;
      }

      // 清空容器
      this.toolbarContainer.innerHTML = "";

      if (!this.state.toolbarVisible) {
        return;
      }

      // 使用DocumentFragment优化DOM操作
      const fragment = document.createDocumentFragment();
      const toolbar = this.createToolbarElement();
      fragment.appendChild(toolbar);

      // 一次性添加到DOM
      this.toolbarContainer.appendChild(fragment);
    } finally {
      this.isRendering = false;
    }
  }

  /**
   * 创建工具栏元素
   */
  private createToolbarElement(): HTMLElement {
    const toolbar = document.createElement("div");
    toolbar.className = "quote-selection-toolbar";
    toolbar.style.left = `${this.state.toolbarPosition.x}px`;
    toolbar.style.top = `${this.state.toolbarPosition.y}px`;

    // 创建按钮
    const button = this.createToolbarButton();
    const icon = this.createQuoteIcon();

    toolbar.appendChild(button);
    toolbar.appendChild(icon);

    return toolbar;
  }

  /**
   * 创建工具栏按钮
   */
  private createToolbarButton(): HTMLElement {
    const button = document.createElement("button");
    button.title = this.state.toolbarMode === "add" ? "高亮采集" : "取消高亮";

    if (this.state.toolbarMode === "add") {
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 11H5a2 2 0 0 0-2 2v7a2 2 0 0 0 2 2h7a2 2 0 0 0 2-2v-4" />
          <path d="M14 3l7 7-10 10H4v-7L14 3z" />
        </svg>
      `;
      button.onclick = this.handleHighlight.bind(this);
    } else {
      button.innerHTML = `
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M18 6L6 18" />
          <path d="M6 6l12 12" />
        </svg>
      `;
      button.onclick = this.handleRemoveHighlight.bind(this);
    }

    return button;
  }

  /**
   * 创建Quote装饰图标
   */
  private createQuoteIcon(): HTMLElement {
    const icon = document.createElement("div");
    icon.className = "quote-icon";
    icon.innerHTML = `
      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z" />
        <path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z" />
      </svg>
    `;
    return icon;
  }

  /**
   * 批量更新操作（性能优化）
   */
  private batchUpdate(updateFn: () => void): void {
    // 使用requestAnimationFrame确保在下一帧执行
    requestAnimationFrame(() => {
      updateFn();
    });
  }

  /**
   * 渲染通知栏
   */
  private renderNotification(): void {
    if (!this.notificationContainer) return;

    // 清空容器
    this.notificationContainer.innerHTML = "";

    if (!this.state.notificationVisible) {
      return;
    }

    // 直接创建DOM元素
    const notificationBar = document.createElement("div");
    notificationBar.className = "quote-notification-bar";

    // 创建计数部分
    const countSection = document.createElement("div");
    countSection.className = "quote-notification-count";

    const badge = document.createElement("div");
    const count = this.state.items.length;
    badge.className =
      count >= 10 ? "quote-count-badge two-digit" : "quote-count-badge";
    badge.textContent = count > 99 ? "99+" : count.toString();

    const text = document.createElement("span");
    text.textContent = "已采集到知识库";

    countSection.appendChild(badge);
    countSection.appendChild(text);

    // 创建操作按钮部分
    const actionsSection = document.createElement("div");
    actionsSection.className = "quote-notification-actions";

    const clearBtn = document.createElement("button");
    clearBtn.className = "quote-notification-btn danger";
    clearBtn.textContent = "清除";
    clearBtn.onclick = () => {
      // 简化版确认对话框
      if (confirm("确定要清除页面中所有高亮文本吗？此操作不可撤销。")) {
        this.handleClearAll();
      }
    };

    const projectBtn = document.createElement("button");
    projectBtn.className = "quote-notification-btn";
    projectBtn.textContent = "选择project";
    projectBtn.onclick = this.handleSelectProject.bind(this);

    actionsSection.appendChild(clearBtn);
    actionsSection.appendChild(projectBtn);

    notificationBar.appendChild(countSection);
    notificationBar.appendChild(actionsSection);
    this.notificationContainer.appendChild(notificationBar);
  }

  /**
   * 处理高亮操作（优化版）
   */
  private handleHighlight(): void {
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (selectedText.length === 0) return;

    // 性能优化：检查文本长度限制
    if (selectedText.length > 5000) {
      console.warn("[HighlightSystem] 选中文本过长，跳过高亮");
      return;
    }

    const range = selection.getRangeAt(0);
    const highlightId = this.generateId();

    try {
      // 使用DocumentFragment优化DOM操作
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", highlightId);

      // 批量DOM操作
      const fragment = document.createDocumentFragment();
      fragment.appendChild(range.extractContents());
      span.appendChild(fragment);
      range.insertNode(span);

      // 保存到状态
      const item: HighlightItem = {
        id: highlightId,
        text: selectedText,
        element: span,
        timestamp: Date.now(),
        pageUrl: window.location.href,
      };

      this.state.items.push(item);

      // 批量更新
      this.batchUpdate(() => {
        this.saveToStorage();
        this.updateNotificationVisibility();
        this.hideToolbar();
      });

      // 清除选择
      selection.removeAllRanges();

      console.log(
        "[HighlightSystem] 添加高亮:",
        selectedText.substring(0, 50) + (selectedText.length > 50 ? "..." : "")
      );
    } catch (error) {
      console.error("[HighlightSystem] 高亮失败:", error);
    }
  }

  /**
   * 处理移除高亮操作
   */
  private handleRemoveHighlight(): void {
    if (!this.state.targetHighlightId) return;

    const itemIndex = this.state.items.findIndex(
      (item) => item.id === this.state.targetHighlightId
    );
    if (itemIndex === -1) return;

    const item = this.state.items[itemIndex];

    // 移除DOM元素的高亮样式
    const parent = item.element.parentNode;
    if (parent) {
      parent.replaceChild(document.createTextNode(item.text), item.element);
      parent.normalize(); // 合并相邻的文本节点
    }

    // 从状态中移除
    this.state.items.splice(itemIndex, 1);
    this.saveToStorage();

    // 更新UI
    this.updateNotificationVisibility();
    this.hideToolbar();

    console.log("[HighlightSystem] 移除高亮:", item.text);
  }

  /**
   * 处理清除所有高亮（优化版）
   */
  private handleClearAll(): void {
    if (this.state.items.length === 0) {
      return;
    }

    console.log(`[HighlightSystem] 开始清除 ${this.state.items.length} 个高亮`);

    // 批量DOM操作优化
    const elementsToProcess = [...this.state.items]; // 创建副本避免迭代时修改

    // 分批处理大量高亮，避免阻塞UI
    const batchSize = 50;
    let currentIndex = 0;

    const processBatch = () => {
      const endIndex = Math.min(
        currentIndex + batchSize,
        elementsToProcess.length
      );

      for (let i = currentIndex; i < endIndex; i++) {
        const item = elementsToProcess[i];
        const parent = item.element.parentNode;
        if (parent) {
          parent.replaceChild(document.createTextNode(item.text), item.element);
          parent.normalize();
        }
      }

      currentIndex = endIndex;

      if (currentIndex < elementsToProcess.length) {
        // 继续处理下一批
        requestAnimationFrame(processBatch);
      } else {
        // 所有处理完成
        this.state.items = [];
        this.batchUpdate(() => {
          this.saveToStorage();
          this.updateNotificationVisibility();
        });
        console.log("[HighlightSystem] 清除所有高亮完成");
      }
    };

    // 开始批量处理
    processBatch();
  }

  /**
   * 处理选择项目（暂时只是占位）
   */
  private handleSelectProject(): void {
    console.log("[HighlightSystem] 选择项目功能暂未实现");
    // TODO: 实现项目选择功能
  }

  /**
   * 更新通知栏可见性
   */
  private updateNotificationVisibility(): void {
    this.state.notificationVisible = this.state.items.length > 0;
    this.renderNotification();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `highlight_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 保存到本地存储（带防抖优化）
   */
  private saveToStorage(): void {
    // 清除之前的防抖定时器
    if (this.saveDebounceTimer) {
      clearTimeout(this.saveDebounceTimer);
    }

    // 使用防抖处理，避免频繁存储
    this.saveDebounceTimer = window.setTimeout(() => {
      this.doSaveToStorage();
    }, this.saveDebounceDelay);
  }

  /**
   * 执行实际的存储操作
   */
  private doSaveToStorage(): void {
    try {
      const data = this.state.items.map((item) => ({
        id: item.id,
        text:
          item.text.length > 1000
            ? item.text.substring(0, 1000) + "..."
            : item.text, // 限制存储的文本长度
        timestamp: item.timestamp,
        pageUrl: item.pageUrl,
      }));

      localStorage.setItem("quote_highlights", JSON.stringify(data));
    } catch (error) {
      console.error("[HighlightSystem] 存储失败:", error);
    }
  }

  /**
   * 从本地存储加载
   */
  private loadStoredHighlights(): void {
    try {
      const stored = localStorage.getItem("quote_highlights");
      if (!stored) return;

      const data = JSON.parse(stored);
      // 注意：页面刷新后DOM元素会丢失，这里只是演示
      // 实际应用中需要更复杂的恢复机制
      console.log("[HighlightSystem] 加载存储的高亮数据:", data);
    } catch (error) {
      console.error("[HighlightSystem] 加载存储数据失败:", error);
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 清理所有定时器
    if (this.selectionDebounceTimer) {
      clearTimeout(this.selectionDebounceTimer);
      this.selectionDebounceTimer = null;
    }
    if (this.renderDebounceTimer) {
      clearTimeout(this.renderDebounceTimer);
      this.renderDebounceTimer = null;
    }
    if (this.saveDebounceTimer) {
      clearTimeout(this.saveDebounceTimer);
      this.saveDebounceTimer = null;
    }

    // 移除事件监听器
    document.removeEventListener("mouseup", this.handleMouseUp.bind(this));
    document.removeEventListener("keyup", this.handleKeyUp.bind(this));
    document.removeEventListener("scroll", this.hideToolbar.bind(this));
    document.removeEventListener("click", this.handleDocumentClick.bind(this));
    document.removeEventListener("mouseover", this.handleMouseOver.bind(this));
    document.removeEventListener("mouseout", this.handleMouseOut.bind(this));

    // 清理DOM
    if (this.toolbarContainer) {
      this.toolbarContainer.remove();
    }
    if (this.notificationContainer) {
      this.notificationContainer.remove();
    }

    // 清理全局引用
    delete (window as any).highlightSystemInstance;
    delete (window as any).testHighlightSystem;

    console.log("[HighlightSystem] 系统已销毁");
  }
}
