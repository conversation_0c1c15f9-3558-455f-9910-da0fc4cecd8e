import { HighlightItem, HighlightState, ToolbarPosition } from "./types";
import { TextHighlighter } from "./TextHighlighter";
import { PositionCalculator } from "./PositionCalculator";
import { AuthService } from "../services/AuthService";
import { ToolbarManager, ToolbarConfig } from "./ToolbarManager";

/**
 * 高亮采集系统核心管理器
 */
export class HighlightSystem {
  private state: HighlightState;
  private notificationContainer: HTMLElement | null = null;

  // 拆分出的组件实例
  private textHighlighter = new TextHighlighter();
  private positionCalculator = new PositionCalculator();
  private toolbarManager: ToolbarManager;

  // hover状态管理
  private hoverState = {
    currentHighlightId: null as string | null,
    isTransitioning: false,
    transitionTimeout: null as number | null,
  };

  constructor() {
    this.state = {
      items: [],
      toolbarVisible: false,
      toolbarPosition: { x: 0, y: 0 },
      toolbarMode: "add",
      notificationVisible: false,
    };

    // 初始化工具栏管理器
    const toolbarConfig: ToolbarConfig = {
      onHighlight: () => this.handleHighlight(),
      onRemoveHighlight: () => this.handleRemoveHighlight(),
      onStateChange: (visible, position) => {
        this.state.toolbarVisible = visible;
        if (position) {
          this.state.toolbarPosition = position;
        }
      },
    };
    this.toolbarManager = new ToolbarManager(toolbarConfig);

    this.init();

    // 添加到全局对象以便调试
    (window as any).highlightSystemInstance = this;

    // 添加测试方法
    (window as any).testHighlightSystem = () => {
      console.log("[HighlightSystem] 测试方法调用");
      this.showToolbar({ x: 100, y: 100 }, "add");
    };
  }

  /**
   * 初始化系统
   */
  private init(): void {
    console.log("[HighlightSystem] 开始初始化...");

    // 确保DOM已加载
    if (document.readyState === "loading") {
      document.addEventListener("DOMContentLoaded", () => {
        this.doInit();
      });
    } else {
      this.doInit();
    }
  }

  /**
   * 执行实际初始化
   */
  private doInit(): void {
    console.log("[HighlightSystem] 执行实际初始化...");
    this.createContainers();
    this.bindEvents();
    this.loadStoredHighlights();
    console.log("[HighlightSystem] 初始化完成");
  }

  /**
   * 创建UI容器
   */
  private createContainers(): void {
    // 工具栏容器现在由ToolbarManager管理

    // 创建通知栏容器
    this.notificationContainer = document.createElement("div");
    this.notificationContainer.id = "quote-notification-container";
    document.body.appendChild(this.notificationContainer);

    // 注入样式
    this.injectStyles();
  }

  /**
   * 注入样式
   */
  private injectStyles(): void {
    const styleId = "quote-highlight-styles";
    if (document.getElementById(styleId)) return;

    const style = document.createElement("style");
    style.id = styleId;
    style.textContent = `
      /* 高亮文本样式 */
      .quote-highlight {
        background-color: #fff3cd !important;
        border-radius: 2px !important;
        position: relative !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      /* 移除单个span的hover效果，改为统一控制 */
      .quote-highlight:hover {
        /* 保持原有背景色变化，但移除transform避免单独缩放 */
        background-color: #ffeaa7 !important;
      }

      /* 统一的hover状态样式 */
      .quote-highlight-hovered {
        background-color: #ffeaa7 !important;
        transform: scale(1.02) !important;
        transition: all 0.2s ease !important;
      }

      /* 表格内高亮的特殊样式 */
      .quote-highlight-table {
        display: inline !important;
        vertical-align: baseline !important;
        line-height: inherit !important;
        font-size: inherit !important;
        font-weight: inherit !important;
        margin: 0 !important;
        padding: 0 !important;
        border: none !important;
        background-color: #fff3cd !important;
        border-radius: 0 !important;
        position: relative !important;
        cursor: pointer !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-highlight-table:hover {
        background-color: #ffeaa7 !important;
      }

      /* 确保表格单元格保持原有样式 */
      td .quote-highlight-table,
      th .quote-highlight-table {
        width: auto !important;
        height: auto !important;
        max-width: none !important;
        max-height: none !important;
        white-space: inherit !important;
      }

      /* 选区工具栏样式 */
      .quote-selection-toolbar {
        position: absolute !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 8px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
        padding: 8px !important;
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        will-change: transform !important;
      }

      .quote-selection-toolbar button {
        background: none !important;
        border: none !important;
        cursor: pointer !important;
        padding: 6px !important;
        border-radius: 4px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-selection-toolbar button:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-selection-toolbar .quote-icon {
        width: 24px !important;
        height: 24px !important;
        opacity: 1 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        border-radius: 4px !important;
        cursor: move !important;
        user-select: none !important;
        transition: background-color 0.2s ease !important;
      }

      .quote-selection-toolbar .quote-icon:hover {
        /* 移除hover时的背景色块 */
      }

      .quote-selection-toolbar .quote-icon:active {
        cursor: grabbing !important;
      }

      /* 拖拽时的工具栏样式 */
      .quote-selection-toolbar.dragging {
        box-shadow: 0 8px 24px rgba(0, 0, 0, 0.3) !important;
        transform: scale(1.02) !important;
        transition: none !important;
      }

      .quote-selection-toolbar.dragging .quote-icon {
        background-color: #e3f2fd !important;
        transform: scale(1.1) !important;
      }

      /* 底部通知栏样式 */
      .quote-notification-bar {
        position: fixed !important;
        bottom: 20px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        z-index: 999999 !important;
        background: white !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        padding: 12px 16px !important;
        display: flex !important;
        align-items: center !important;
        gap: 12px !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1 !important;
        max-width: 400px !important;
        animation: slideUp 0.3s ease-out !important;
      }

      @keyframes slideUp {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      .quote-notification-count {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
        color: #333 !important;
      }

      .quote-count-badge {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        background-color: #007bff !important;
        color: white !important;
        font-weight: 600 !important;
        font-size: 12px !important;
        min-width: 20px !important;
        height: 20px !important;
        border-radius: 10px !important;
      }

      .quote-count-badge.two-digit {
        border-radius: 10px !important;
        padding: 0 6px !important;
      }

      .quote-notification-actions {
        display: flex !important;
        align-items: center !important;
        gap: 8px !important;
      }

      .quote-notification-btn {
        background: none !important;
        border: 1px solid #e0e0e0 !important;
        border-radius: 6px !important;
        padding: 6px 12px !important;
        cursor: pointer !important;
        font-size: 12px !important;
        color: #666 !important;
        transition: all 0.2s ease !important;
      }

      .quote-notification-btn:hover {
        background-color: #f5f5f5 !important;
        border-color: #ccc !important;
      }

      .quote-notification-btn.danger {
        color: #dc3545 !important;
        border-color: #dc3545 !important;
      }

      .quote-notification-btn.danger:hover {
        background-color: #dc3545 !important;
        color: white !important;
      }

      /* 确认删除弹窗样式 */
      .quote-confirm-dialog {
        position: fixed !important;
        top: 50% !important;
        left: 50% !important;
        transform: translate(-50%, -50%) !important;
        z-index: 9999999 !important;
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
        padding: 24px !important;
        max-width: 400px !important;
        width: 90% !important;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
      }

      .quote-confirm-dialog-overlay {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        right: 0 !important;
        bottom: 0 !important;
        background: rgba(0, 0, 0, 0.5) !important;
        z-index: 9999998 !important;
      }

      .quote-confirm-dialog h3 {
        margin: 0 0 12px 0 !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        color: #333 !important;
      }

      .quote-confirm-dialog p {
        margin: 0 0 20px 0 !important;
        font-size: 14px !important;
        color: #666 !important;
        line-height: 1.4 !important;
      }

      .quote-confirm-dialog-actions {
        display: flex !important;
        justify-content: flex-end !important;
        gap: 12px !important;
      }

      .quote-confirm-dialog-btn {
        padding: 8px 16px !important;
        border-radius: 6px !important;
        border: 1px solid #e0e0e0 !important;
        background: white !important;
        cursor: pointer !important;
        font-size: 14px !important;
        transition: all 0.2s ease !important;
      }

      .quote-confirm-dialog-btn.primary {
        background: #dc3545 !important;
        color: white !important;
        border-color: #dc3545 !important;
      }

      .quote-confirm-dialog-btn:hover {
        background-color: #f5f5f5 !important;
      }

      .quote-confirm-dialog-btn.primary:hover {
        background-color: #c82333 !important;
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 绑定事件监听器
   */
  private bindEvents(): void {
    // 文本选择事件
    document.addEventListener("mouseup", this.handleMouseUp.bind(this));
    document.addEventListener("keyup", this.handleKeyUp.bind(this));

    // 滚动和点击事件（隐藏工具栏）
    document.addEventListener("scroll", this.hideToolbar.bind(this));
    document.addEventListener("click", this.handleDocumentClick.bind(this));

    // 高亮文本hover事件
    document.addEventListener("mouseover", this.handleMouseOver.bind(this));
    document.addEventListener("mouseout", this.handleMouseOut.bind(this));
  }

  /**
   * 处理鼠标抬起事件
   */
  private handleMouseUp(event: MouseEvent): void {
    // 保存鼠标抬起的位置
    const mousePosition = {
      x: event.clientX,
      y: event.clientY,
    };

    setTimeout(() => {
      const selection = window.getSelection();
      console.log("[HighlightSystem] 鼠标抬起，检查选择:", {
        hasSelection: !!selection,
        isCollapsed: selection?.isCollapsed,
        selectedText: selection?.toString(),
        mousePosition,
      });

      if (!selection || selection.isCollapsed) {
        this.hideToolbar();
        return;
      }

      const selectedText = selection.toString().trim();
      if (selectedText.length === 0) {
        this.hideToolbar();
        return;
      }

      // 使用鼠标位置计算工具栏位置
      const position =
        this.positionCalculator.calculateToolbarPositionFromMouse(
          mousePosition,
          selection
        );

      console.log("[HighlightSystem] 显示工具栏:", {
        selectedText,
        position,
        mousePosition,
      });

      this.showToolbar(position, "add");
    }, 10);
  }

  /**
   * 处理键盘事件
   */
  private handleKeyUp(event: KeyboardEvent): void {
    if (event.key === "Escape") {
      this.hideToolbar();
    }
  }

  /**
   * 处理文档点击事件
   */
  private handleDocumentClick(event: MouseEvent): void {
    const target = event.target as HTMLElement;

    // 如果点击的是工具栏或通知栏，不隐藏
    if (
      target.closest("#quote-selection-toolbar-container") ||
      target.closest("#quote-notification-container")
    ) {
      return;
    }

    this.hideToolbar();
  }

  /**
   * 处理鼠标悬停事件
   */
  private handleMouseOver(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    if (target.classList.contains("quote-highlight")) {
      const highlightId = target.getAttribute("data-highlight-id");
      if (highlightId) {
        this.handleHighlightHover(target, highlightId);
      }
    }
  }

  /**
   * 处理高亮元素hover逻辑（统一处理同一高亮ID的所有片段）
   */
  private handleHighlightHover(
    element: HTMLElement,
    highlightId: string
  ): void {
    // 获取同一高亮ID的所有片段
    const allFragments = this.getAllHighlightFragments(highlightId);
    this.handleUnifiedHighlightHover(allFragments, highlightId);
  }

  /**
   * 获取同一高亮ID的所有片段
   */
  private getAllHighlightFragments(highlightId: string): HTMLElement[] {
    return Array.from(
      document.querySelectorAll(`[data-highlight-id="${highlightId}"]`)
    ) as HTMLElement[];
  }

  /**
   * 统一处理高亮hover逻辑
   */
  private handleUnifiedHighlightHover(
    fragments: HTMLElement[],
    highlightId: string
  ): void {
    // 清除之前的延迟隐藏
    if (this.hoverState.transitionTimeout) {
      clearTimeout(this.hoverState.transitionTimeout);
      this.hoverState.transitionTimeout = null;
    }

    // 为所有片段添加hover状态
    this.setFragmentsHoverState(fragments, true);

    // 基于统一边界计算工具栏位置
    const unifiedRect = this.calculateUnifiedBoundingRect(fragments);
    const position =
      this.positionCalculator.calculateToolbarPosition(unifiedRect);

    if (this.hoverState.currentHighlightId === highlightId) {
      // 同一个高亮，不需要动作
      return;
    }

    if (this.hoverState.currentHighlightId && this.state.toolbarVisible) {
      // 从一个高亮移动到另一个高亮：滑动动效
      console.log("[HighlightSystem] 滑动工具栏到新位置:", {
        from: this.hoverState.currentHighlightId,
        to: highlightId,
      });
      this.slideToolbarTo(position, highlightId);
    } else {
      // 首次显示：直接显示
      console.log("[HighlightSystem] 首次显示hover工具栏:", highlightId);
      this.showToolbar(position, "remove", highlightId);
    }

    this.hoverState.currentHighlightId = highlightId;
  }

  // 拖拽相关方法已移除，现在由ToolbarManager处理

  // 所有拖拽相关方法已移除，现在由ToolbarManager处理

  /**
   * 计算所有片段的统一边界矩形
   */
  private calculateUnifiedBoundingRect(fragments: HTMLElement[]): DOMRect {
    if (fragments.length === 0) return new DOMRect();

    // 计算所有片段的统一边界
    let minLeft = Infinity;
    let minTop = Infinity;
    let maxRight = -Infinity;
    let maxBottom = -Infinity;

    fragments.forEach((fragment) => {
      const rect = fragment.getBoundingClientRect();
      minLeft = Math.min(minLeft, rect.left);
      minTop = Math.min(minTop, rect.top);
      maxRight = Math.max(maxRight, rect.right);
      maxBottom = Math.max(maxBottom, rect.bottom);
    });

    console.log("[HighlightSystem] 统一边界计算:", {
      fragmentCount: fragments.length,
      unifiedBounds: { minLeft, minTop, maxRight, maxBottom },
    });

    return new DOMRect(minLeft, minTop, maxRight - minLeft, maxBottom - minTop);
  }

  /**
   * 设置片段的hover状态
   */
  private setFragmentsHoverState(
    fragments: HTMLElement[],
    isHovered: boolean
  ): void {
    fragments.forEach((fragment) => {
      if (isHovered) {
        fragment.classList.add("quote-highlight-hovered");
      } else {
        fragment.classList.remove("quote-highlight-hovered");
      }
    });

    console.log("[HighlightSystem] 设置片段hover状态:", {
      fragmentCount: fragments.length,
      isHovered,
    });
  }

  /**
   * 滑动工具栏到新位置
   */
  private slideToolbarTo(
    newPosition: ToolbarPosition,
    highlightId: string
  ): void {
    this.hoverState.isTransitioning = true;

    // 使用ToolbarManager的滑动功能
    this.toolbarManager.slideTo(newPosition, highlightId);

    // 更新状态
    this.state.toolbarPosition = newPosition;
    this.state.targetHighlightId = highlightId;

    // 动画完成后清理状态
    setTimeout(() => {
      this.hoverState.isTransitioning = false;
      console.log("[HighlightSystem] 工具栏滑动完成");
    }, 300);
  }

  /**
   * 处理鼠标离开事件
   */
  private handleMouseOut(event: MouseEvent): void {
    const target = event.target as HTMLElement;
    const relatedTarget = event.relatedTarget as HTMLElement;

    // 如果鼠标移动到工具栏，不隐藏
    if (
      relatedTarget &&
      relatedTarget.closest("#quote-selection-toolbar-container")
    ) {
      return;
    }

    // 如果鼠标移动到同一高亮的其他片段，不处理
    if (
      target.classList.contains("quote-highlight") &&
      relatedTarget &&
      relatedTarget.classList.contains("quote-highlight")
    ) {
      const targetId = target.getAttribute("data-highlight-id");
      const relatedId = relatedTarget.getAttribute("data-highlight-id");
      if (targetId === relatedId) {
        return; // 同一高亮内部移动，不处理
      }
    }

    if (target.classList.contains("quote-highlight")) {
      const highlightId = target.getAttribute("data-highlight-id");
      if (highlightId) {
        // 移除所有片段的hover状态
        const fragments = this.getAllHighlightFragments(highlightId);
        this.setFragmentsHoverState(fragments, false);

        // 清除当前hover状态
        this.hoverState.currentHighlightId = null;

        // 延迟隐藏工具栏
        this.hoverState.transitionTimeout = window.setTimeout(() => {
          if (!this.isMouseOverToolbar() && !this.hoverState.isTransitioning) {
            console.log("[HighlightSystem] 隐藏hover工具栏");
            this.hideToolbar();
          }
        }, 200);
      }
    }
  }

  /**
   * 检查鼠标是否在工具栏上
   */
  private isMouseOverToolbar(): boolean {
    // 简化实现，实际使用中可以通过事件参数传递鼠标位置
    const toolbar = document.querySelector(
      "#quote-selection-toolbar-container .quote-selection-toolbar"
    );
    return !!toolbar;
  }

  /**
   * 显示工具栏
   */
  public showToolbar(
    position: ToolbarPosition,
    mode: "add" | "remove",
    targetHighlightId?: string
  ): void {
    console.log("[HighlightSystem] showToolbar 调用:", {
      position,
      mode,
      targetHighlightId,
    });

    this.state.toolbarVisible = true;
    this.state.toolbarPosition = position;
    this.state.toolbarMode = mode;
    this.state.targetHighlightId = targetHighlightId;

    // 使用ToolbarManager显示工具栏
    this.toolbarManager.show(position, mode, targetHighlightId);
  }

  /**
   * 隐藏工具栏
   */
  private hideToolbar(): void {
    this.state.toolbarVisible = false;

    // 使用ToolbarManager隐藏工具栏
    this.toolbarManager.hide();
  }

  // renderToolbar方法已移除，现在由ToolbarManager处理

  /**
   * 渲染通知栏
   */
  private renderNotification(): void {
    if (!this.notificationContainer) return;

    // 清空容器
    this.notificationContainer.innerHTML = "";

    if (!this.state.notificationVisible) {
      return;
    }

    // 直接创建DOM元素
    const notificationBar = document.createElement("div");
    notificationBar.className = "quote-notification-bar";

    // 创建计数部分
    const countSection = document.createElement("div");
    countSection.className = "quote-notification-count";

    const badge = document.createElement("div");
    const count = this.state.items.length;
    badge.className =
      count >= 10 ? "quote-count-badge two-digit" : "quote-count-badge";
    badge.textContent = count > 99 ? "99+" : count.toString();

    const text = document.createElement("span");
    text.textContent = "已采集到知识库";

    countSection.appendChild(badge);
    countSection.appendChild(text);

    // 创建操作按钮部分
    const actionsSection = document.createElement("div");
    actionsSection.className = "quote-notification-actions";

    const clearBtn = document.createElement("button");
    clearBtn.className = "quote-notification-btn danger";
    clearBtn.textContent = "清除";
    clearBtn.onclick = () => {
      // 简化版确认对话框
      if (confirm("确定要清除页面中所有高亮文本吗？此操作不可撤销。")) {
        this.handleClearAll();
      }
    };

    const projectBtn = document.createElement("button");
    projectBtn.className = "quote-notification-btn";
    projectBtn.textContent = "选择project";
    projectBtn.onclick = this.handleSelectProject.bind(this);

    actionsSection.appendChild(clearBtn);
    actionsSection.appendChild(projectBtn);

    notificationBar.appendChild(countSection);
    notificationBar.appendChild(actionsSection);
    this.notificationContainer.appendChild(notificationBar);
  }

  /**
   * 处理高亮操作 - 支持跨元素边界和认证检查
   */
  private async handleHighlight(): Promise<void> {
    // 1. 基础验证（保持原有逻辑）
    const selection = window.getSelection();
    if (!selection || selection.isCollapsed) return;

    const selectedText = selection.toString().trim();
    if (selectedText.length === 0) return;

    // 2. 认证检查（新增）
    console.log("[HighlightSystem] 检查用户认证状态");

    try {
      const authStatus = await AuthService.checkAuthenticationStatus();

      if (!authStatus.isAuthenticated) {
        console.log("[HighlightSystem] 用户未登录，显示登录提示浮窗");

        const userChoice = await AuthService.showLoginPrompt();

        if (userChoice === "cancel") {
          console.log("[HighlightSystem] 用户取消登录，停止高亮流程");
          return;
        } else {
          console.log("[HighlightSystem] 用户选择登录，登录页面已打开");
          return;
        }
      }

      console.log("[HighlightSystem] 用户已登录，继续高亮流程");
    } catch (error) {
      console.error("[HighlightSystem] 认证检查失败:", error);
      // 认证检查失败时停止流程，确保安全性
      return;
    }

    // 3. 继续原有的高亮创建逻辑
    const range = selection.getRangeAt(0);
    const highlightId = this.generateId();

    try {
      const highlightedElements = this.textHighlighter.createHighlight(
        range,
        highlightId
      );

      if (highlightedElements.length === 0) {
        console.warn("[HighlightSystem] 无法创建高亮元素");
        return;
      }

      // 保存到状态 - 使用第一个元素作为主要引用
      const item: HighlightItem = {
        id: highlightId,
        text: selectedText,
        element: highlightedElements[0], // 主要元素
        elements: highlightedElements, // 所有相关元素
        timestamp: Date.now(),
        pageUrl: window.location.href,
      };

      this.state.items.push(item);
      this.saveToStorage();
      this.updateNotificationVisibility();
      this.hideToolbar();
      selection.removeAllRanges();

      console.log("[HighlightSystem] 添加高亮成功:", {
        text: selectedText,
        elementsCount: highlightedElements.length,
      });
    } catch (error) {
      console.error("[HighlightSystem] 创建高亮失败:", error);
    }
  }

  /**
   * 处理移除高亮操作 - 支持多元素高亮
   */
  private handleRemoveHighlight(): void {
    if (!this.state.targetHighlightId) return;

    const itemIndex = this.state.items.findIndex(
      (item) => item.id === this.state.targetHighlightId
    );
    if (itemIndex === -1) return;

    const item = this.state.items[itemIndex];

    // 移除所有相关的高亮元素
    const elementsToRemove = item.elements || [item.element];

    elementsToRemove.forEach((element, index) => {
      const parent = element.parentNode;
      if (parent) {
        // 获取元素的文本内容
        const textContent = element.textContent || "";
        parent.replaceChild(document.createTextNode(textContent), element);

        // 只在最后一个元素处理完后进行normalize，避免影响其他元素的处理
        if (index === elementsToRemove.length - 1) {
          parent.normalize(); // 合并相邻的文本节点
        }
      }
    });

    // 从状态中移除
    this.state.items.splice(itemIndex, 1);
    this.saveToStorage();

    // 更新UI
    this.updateNotificationVisibility();
    this.hideToolbar();

    console.log("[HighlightSystem] 移除高亮:", {
      text: item.text,
      elementsCount: elementsToRemove.length,
    });
  }

  /**
   * 处理清除所有高亮 - 支持多元素高亮
   */
  private handleClearAll(): void {
    this.state.items.forEach((item) => {
      const elementsToRemove = item.elements || [item.element];

      elementsToRemove.forEach((element) => {
        const parent = element.parentNode;
        if (parent) {
          const textContent = element.textContent || "";
          parent.replaceChild(document.createTextNode(textContent), element);
        }
      });
    });

    // 在所有元素移除后统一进行normalize
    const processedParents = new Set<Node>();
    this.state.items.forEach((item) => {
      const elementsToRemove = item.elements || [item.element];
      elementsToRemove.forEach((element) => {
        if (element.parentNode && !processedParents.has(element.parentNode)) {
          element.parentNode.normalize();
          processedParents.add(element.parentNode);
        }
      });
    });

    this.state.items = [];
    this.saveToStorage();
    this.updateNotificationVisibility();

    console.log("[HighlightSystem] 清除所有高亮");
  }

  /**
   * 处理选择项目（暂时只是占位）
   */
  private handleSelectProject(): void {
    console.log("[HighlightSystem] 选择项目功能暂未实现");
    // TODO: 实现项目选择功能
  }

  /**
   * 更新通知栏可见性
   */
  private updateNotificationVisibility(): void {
    const count = this.state.items.length;
    const shouldShow = count > 0;

    // 检查是否需要完整重新渲染
    if (this.state.notificationVisible !== shouldShow) {
      // 显示状态变化，需要完整渲染
      this.state.notificationVisible = shouldShow;
      this.renderNotification();
    } else if (shouldShow) {
      // 只是数量变化，只更新数字
      this.updateNotificationCount(count);
    }
  }

  /**
   * 只更新通知栏中的数字部分
   */
  private updateNotificationCount(count: number): void {
    if (!this.notificationContainer) return;

    const countElement = this.notificationContainer.querySelector(
      ".quote-notification-count .quote-count-badge"
    ) as HTMLElement;
    if (countElement) {
      // 检查数字是否真的发生了变化
      const currentText = countElement.textContent;
      const newText = count > 99 ? "99+" : count.toString();

      if (currentText !== newText) {
        console.log("[HighlightSystem] 更新通知栏数字:", {
          from: currentText,
          to: newText,
        });

        // 添加平滑的数字变化动画
        countElement.style.transition = "transform 0.15s ease";
        countElement.style.transform = "scale(1.15)";
        countElement.textContent = newText;

        // 更新样式类（处理两位数的情况）
        countElement.className =
          count >= 10 ? "quote-count-badge two-digit" : "quote-count-badge";

        setTimeout(() => {
          countElement.style.transform = "scale(1)";
        }, 150);
      }
      return;
    }

    // 如果数字元素不存在，才进行完整渲染
    console.log("[HighlightSystem] 数字元素不存在，进行完整渲染");
    this.renderNotification();
  }

  /**
   * 生成唯一ID
   */
  private generateId(): string {
    return `highlight_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  /**
   * 保存到本地存储
   */
  private saveToStorage(): void {
    const data = this.state.items.map((item) => ({
      id: item.id,
      text: item.text,
      timestamp: item.timestamp,
      pageUrl: item.pageUrl,
    }));

    localStorage.setItem("quote_highlights", JSON.stringify(data));
  }

  /**
   * 从本地存储加载
   */
  private loadStoredHighlights(): void {
    try {
      const stored = localStorage.getItem("quote_highlights");
      if (!stored) return;

      const data = JSON.parse(stored);
      // 注意：页面刷新后DOM元素会丢失，这里只是演示
      // 实际应用中需要更复杂的恢复机制
      console.log("[HighlightSystem] 加载存储的高亮数据:", data);
    } catch (error) {
      console.error("[HighlightSystem] 加载存储数据失败:", error);
    }
  }

  /**
   * 销毁系统
   */
  public destroy(): void {
    // 移除事件监听器
    document.removeEventListener("mouseup", this.handleMouseUp.bind(this));
    document.removeEventListener("keyup", this.handleKeyUp.bind(this));
    document.removeEventListener("scroll", this.hideToolbar.bind(this));
    document.removeEventListener("click", this.handleDocumentClick.bind(this));
    document.removeEventListener("mouseover", this.handleMouseOver.bind(this));
    document.removeEventListener("mouseout", this.handleMouseOut.bind(this));

    // 清理DOM
    this.toolbarManager.destroy();
    if (this.notificationContainer) {
      this.notificationContainer.remove();
    }

    console.log("[HighlightSystem] 系统已销毁");
  }
}
