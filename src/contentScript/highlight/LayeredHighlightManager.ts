import { OverlapSegment, HighlightLayer, TextRange } from "./types";

/**
 * 多层独立高亮管理器
 * 支持重叠高亮的独立创建和删除
 */
export class LayeredHighlightManager {
  private maxLayer: number = 0;
  private layerMap: Map<string, number> = new Map();

  /**
   * 创建独立的多层高亮
   */
  public createLayeredHighlight(range: Range, highlightId: string): HTMLElement[] {
    console.log("[LayeredHighlight] 创建独立高亮，不影响现有高亮");
    
    // 1. 分析重叠情况
    const overlapAnalysis = this.analyzeOverlaps(range);
    
    // 2. 分配新的层级
    const newLayer = ++this.maxLayer;
    this.layerMap.set(highlightId, newLayer);
    
    // 3. 创建分段高亮
    const segments = this.createHighlightSegments(overlapAnalysis, highlightId, newLayer);
    
    console.log("[LayeredHighlight] 创建完成:", {
      highlightId,
      layer: newLayer,
      segmentCount: segments.length,
      overlapCount: overlapAnalysis.filter(s => s.existingLayers.length > 0).length
    });
    
    return segments;
  }

  /**
   * 分析选择范围与现有高亮的重叠情况
   */
  private analyzeOverlaps(newRange: Range): OverlapSegment[] {
    const segments: OverlapSegment[] = [];
    
    // 获取新选择范围内的所有文本节点
    const textNodes = this.getTextNodesInRange(newRange);
    
    textNodes.forEach(textNode => {
      const nodeSegments = this.analyzeNodeOverlaps(textNode, newRange);
      segments.push(...nodeSegments);
    });
    
    console.log("[LayeredHighlight] 重叠分析完成:", {
      totalSegments: segments.length,
      overlapSegments: segments.filter(s => s.existingLayers.length > 0).length
    });
    
    return segments;
  }

  /**
   * 分析单个文本节点的重叠情况
   */
  private analyzeNodeOverlaps(textNode: Text, newRange: Range): OverlapSegment[] {
    const segments: OverlapSegment[] = [];
    const nodeText = textNode.textContent || '';
    
    // 计算新选择在此文本节点中的范围
    let newStart = 0;
    let newEnd = nodeText.length;
    
    if (newRange.startContainer === textNode) {
      newStart = newRange.startOffset;
    } else if (newRange.comparePoint(textNode, 0) > 0) {
      return []; // 选择范围在此节点之后开始
    }
    
    if (newRange.endContainer === textNode) {
      newEnd = newRange.endOffset;
    } else if (newRange.comparePoint(textNode, nodeText.length) < 0) {
      return []; // 选择范围在此节点之前结束
    }
    
    // 查找此文本节点中的现有高亮
    const existingHighlights = this.findExistingHighlightsInNode(textNode);
    
    if (existingHighlights.length === 0) {
      // 无重叠，直接返回整个段
      segments.push({
        textNode,
        startOffset: newStart,
        endOffset: newEnd,
        existingLayers: []
      });
      return segments;
    }
    
    // 有现有高亮，需要分段处理
    return this.createSegmentsWithOverlaps(textNode, newStart, newEnd, existingHighlights);
  }

  /**
   * 创建包含重叠信息的分段
   */
  private createSegmentsWithOverlaps(
    textNode: Text,
    newStart: number,
    newEnd: number,
    existingHighlights: Array<{startOffset: number, endOffset: number, highlightId: string}>
  ): OverlapSegment[] {
    const segments: OverlapSegment[] = [];
    const sortedHighlights = existingHighlights.sort((a, b) => a.startOffset - b.startOffset);
    
    let currentPos = newStart;
    
    sortedHighlights.forEach(existing => {
      const overlapStart = Math.max(currentPos, existing.startOffset);
      const overlapEnd = Math.min(newEnd, existing.endOffset);
      
      if (overlapStart < overlapEnd) {
        // 添加重叠前的非重叠段
        if (currentPos < overlapStart) {
          segments.push({
            textNode,
            startOffset: currentPos,
            endOffset: overlapStart,
            existingLayers: []
          });
        }
        
        // 添加重叠段
        segments.push({
          textNode,
          startOffset: overlapStart,
          endOffset: overlapEnd,
          existingLayers: [existing.highlightId]
        });
        
        currentPos = overlapEnd;
      }
    });
    
    // 添加剩余的非重叠段
    if (currentPos < newEnd) {
      segments.push({
        textNode,
        startOffset: currentPos,
        endOffset: newEnd,
        existingLayers: []
      });
    }
    
    return segments;
  }

  /**
   * 获取范围内的所有文本节点
   */
  private getTextNodesInRange(range: Range): Text[] {
    const textNodes: Text[] = [];
    
    if (range.commonAncestorContainer.nodeType === Node.TEXT_NODE) {
      // 单文本节点选择
      textNodes.push(range.commonAncestorContainer as Text);
    } else {
      // 多文本节点选择
      const walker = document.createTreeWalker(
        range.commonAncestorContainer,
        NodeFilter.SHOW_TEXT,
        {
          acceptNode: (node) => {
            const textNode = node as Text;
            if (!textNode.textContent?.trim()) {
              return NodeFilter.FILTER_REJECT;
            }
            return range.intersectsNode(textNode) 
              ? NodeFilter.FILTER_ACCEPT 
              : NodeFilter.FILTER_REJECT;
          }
        }
      );
      
      let node;
      while (node = walker.nextNode()) {
        textNodes.push(node as Text);
      }
    }
    
    return textNodes;
  }

  /**
   * 查找文本节点中的现有高亮
   */
  private findExistingHighlightsInNode(textNode: Text): Array<{startOffset: number, endOffset: number, highlightId: string}> {
    const highlights: Array<{startOffset: number, endOffset: number, highlightId: string}> = [];
    
    // 查找包含此文本节点的高亮span
    let current = textNode.parentElement;
    while (current) {
      if (current.classList.contains('quote-highlight')) {
        const highlightId = current.getAttribute('data-highlight-id');
        if (highlightId) {
          // 计算高亮在文本节点中的范围
          const range = this.calculateHighlightRangeInTextNode(current, textNode);
          if (range) {
            highlights.push({
              startOffset: range.startOffset,
              endOffset: range.endOffset,
              highlightId
            });
          }
        }
      }
      current = current.parentElement;
    }
    
    return highlights;
  }

  /**
   * 计算高亮元素在文本节点中的范围
   */
  private calculateHighlightRangeInTextNode(
    highlightElement: Element, 
    textNode: Text
  ): {startOffset: number, endOffset: number} | null {
    // 简化实现：如果文本节点在高亮元素内，返回全范围
    if (highlightElement.contains(textNode)) {
      return {
        startOffset: 0,
        endOffset: textNode.textContent?.length || 0
      };
    }
    return null;
  }

  /**
   * 创建分段高亮元素
   */
  private createHighlightSegments(
    segments: OverlapSegment[], 
    highlightId: string, 
    layer: number
  ): HTMLElement[] {
    const elements: HTMLElement[] = [];
    
    segments.forEach((segment, index) => {
      const element = this.createSegmentElement(segment, highlightId, layer, index);
      if (element) {
        elements.push(element);
      }
    });
    
    return elements;
  }

  /**
   * 创建单个分段元素
   */
  private createSegmentElement(
    segment: OverlapSegment,
    highlightId: string,
    layer: number,
    index: number
  ): HTMLElement | null {
    const { textNode, startOffset, endOffset, existingLayers } = segment;
    const text = textNode.textContent || '';
    
    // 分割文本
    const beforeText = text.substring(0, startOffset);
    const segmentText = text.substring(startOffset, endOffset);
    const afterText = text.substring(endOffset);
    
    // 创建新的高亮span
    const span = document.createElement('span');
    span.className = 'quote-highlight';
    span.setAttribute('data-highlight-id', highlightId);
    span.setAttribute('data-segment-index', index.toString());
    span.setAttribute('data-layer', layer.toString());
    span.textContent = segmentText;
    
    // 设置多层样式
    this.applyLayeredStyles(span, layer, existingLayers.length);
    
    // 如果有重叠，添加特殊标记
    if (existingLayers.length > 0) {
      span.classList.add('quote-highlight-layered');
      span.setAttribute('data-existing-layers', existingLayers.join(','));
    }
    
    // 替换文本节点
    const parent = textNode.parentNode;
    if (!parent) return null;
    
    const fragment = document.createDocumentFragment();
    
    if (beforeText) {
      fragment.appendChild(document.createTextNode(beforeText));
    }
    
    fragment.appendChild(span);
    
    if (afterText) {
      fragment.appendChild(document.createTextNode(afterText));
    }
    
    parent.replaceChild(fragment, textNode);
    
    console.log("[LayeredHighlight] 创建分段元素:", {
      highlightId,
      segmentIndex: index,
      layer,
      hasOverlap: existingLayers.length > 0,
      text: segmentText
    });
    
    return span;
  }

  /**
   * 应用多层样式
   */
  private applyLayeredStyles(span: HTMLElement, layer: number, existingLayerCount: number): void {
    // 基础样式
    span.style.position = 'relative';
    span.style.zIndex = (1000 + layer).toString();
    
    // 根据层级调整透明度和颜色
    const baseOpacity = 0.3;
    const layerOpacity = Math.max(0.1, baseOpacity - (existingLayerCount * 0.05));
    
    if (existingLayerCount > 0) {
      // 重叠区域使用不同的颜色方案
      span.style.backgroundColor = `rgba(255, 193, 7, ${layerOpacity})`; // 橙色系
      span.style.borderBottom = `2px solid rgba(255, 193, 7, 0.8)`;
    } else {
      // 非重叠区域使用默认颜色
      span.style.backgroundColor = `rgba(255, 235, 59, ${layerOpacity})`; // 黄色系
    }
  }

  /**
   * 获取高亮的层级
   */
  public getHighlightLayer(highlightId: string): number | undefined {
    return this.layerMap.get(highlightId);
  }

  /**
   * 移除高亮层级记录
   */
  public removeHighlightLayer(highlightId: string): void {
    this.layerMap.delete(highlightId);
  }
}
