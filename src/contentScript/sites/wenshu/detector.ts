/**
 * 中国裁判文书网站点检测器
 * 用于检测当前页面是否为中国裁判文书网站点
 */

/**
 * 检测当前页面是否为中国裁判文书网站点
 * @returns 是否为中国裁判文书网站点
 */
export function isWenshuSite(): boolean {
  const hostname = window.location.hostname;
  
  // 严格匹配中国裁判文书网域名
  const isSupported = hostname === 'wenshu.court.gov.cn';
  
  console.log('[Wenshu检测] 域名检测:', {
    hostname,
    isSupported
  });
  
  return isSupported;
}

/**
 * 检测当前页面是否为案例详情页
 * 通过URL参数判断：包含index.html?docId字段
 * @returns 是否为案例详情页
 */
export function isWenshuCaseDetailPage(): boolean {
  const url = window.location.href;
  
  // 检测URL中是否包含完整的index.html?docId字段
  const hasDocId = url.includes('index.html?docId');
  
  console.log('[Wenshu检测] 页面类型检测:', {
    url,
    hasDocId,
    isCaseDetailPage: hasDocId
  });
  
  return hasDocId;
}

/**
 * 综合检测：是否为中国裁判文书网的案例详情页
 * @returns 是否匹配
 */
export function isWenshuPage(): boolean {
  const isSite = isWenshuSite();
  const isDetailPage = isWenshuCaseDetailPage();
  const result = isSite && isDetailPage;
  
  console.log('[Wenshu检测] 综合检测结果:', {
    isSite,
    isDetailPage,
    finalResult: result
  });
  
  return result;
}

/**
 * 获取中国裁判文书网站点信息
 * @returns 站点信息对象
 */
export function getWenshuSiteInfo() {
  return {
    name: "wenshu",
    domain: window.location.hostname,
    type: "legal",
    pageType: "case-detail", 
    url: window.location.href,
    title: document.title,
  };
}
