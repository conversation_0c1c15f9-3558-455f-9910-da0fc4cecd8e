/**
 * 北大法宝站点检测器
 * 用于检测当前页面是否为北大法宝网站及其镜像站点
 */

/**
 * 检测当前页面是否为北大法宝站点
 * 支持主站和镜像站点
 * @returns 是否为北大法宝站点
 */
export function isPkulawSite(): boolean {
  const hostname = window.location.hostname;

  // 支持的域名列表
  const supportedDomains = [
    "www.pkulaw.com", // 主站
    "xn6.guangjie568.com", // 镜像站点
  ];

  const isSupported = supportedDomains.includes(hostname);

  console.log("[PKULaw检测] 域名检测:", {
    hostname,
    isSupported,
  });

  return isSupported;
}

/**
 * 检测当前页面是否为司法案例详情页
 * 通过URL参数判断：包含指定的案例详情页参数
 * @returns 是否为司法案例详情页
 */
export function isPkulawCaseDetailPage(): boolean {
  const url = window.location.href;

  // 定义北大法宝案例详情页的URL参数列表
  const caseDetailParams = [
    "bankruptcy", // 破产案例
    "gac", // 案例相关参数
    "hkjudgment", // 香港判决
    "pfnl", // 相关参数
    "payz", // 相关参数
    "atr", // 相关参数
    "pal", // 相关参数
  ];

  // 检测URL中是否包含任意一个参数
  const isCaseDetailPage = caseDetailParams.some((param) =>
    url.includes(param)
  );

  console.log("[PKULaw检测] 页面类型检测:", {
    url,
    matchedParams: caseDetailParams.filter((param) => url.includes(param)),
    isCaseDetailPage,
  });

  return isCaseDetailPage;
}

/**
 * 综合检测：是否为北大法宝的司法案例详情页
 * @returns 是否匹配
 */
export function isPkulawPage(): boolean {
  const isSite = isPkulawSite();
  const isDetailPage = isPkulawCaseDetailPage();
  const result = isSite && isDetailPage;

  console.log("[PKULaw检测] 综合检测结果:", {
    isSite,
    isDetailPage,
    finalResult: result,
  });

  return result;
}

/**
 * 获取北大法宝站点信息
 * @returns 站点信息对象
 */
export function getPkulawSiteInfo() {
  return {
    name: "pkulaw",
    domain: window.location.hostname,
    type: "legal",
    pageType: "case-detail",
    url: window.location.href,
    title: document.title,
  };
}
