/**
 * 威科先行站点检测器
 * 用于检测当前页面是否为威科先行法律数据库站点
 */

/**
 * 检测当前页面是否为威科先行站点
 * @returns 是否为威科先行站点
 */
export function isWkinfoSite(): boolean {
  const hostname = window.location.hostname.toLowerCase();

  // 支持的域名列表
  const supportedDomains = [
    'law.wkinfo.com.cn',     // 主站
    // 可以添加其他镜像站点
  ];

  // 标准域名检测
  const isStandardDomain = supportedDomains.includes(hostname);

  // 镜像站检测：域名中同时包含 wkinf 和 law 关键词
  const isMirrorDomain = hostname.includes("wkinf") && hostname.includes("law");

  const isSupported = isStandardDomain || isMirrorDomain;

  console.log('[Wkinfo检测] 域名检测:', {
    hostname,
    isStandardDomain,
    isMirrorDomain,
    isSupported
  });

  return isSupported;
}

/**
 * 检测当前页面是否为案例详情页
 * 通过URL路径判断：包含judgment-documents/detail/字段
 * @returns 是否为案例详情页
 */
export function isWkinfoCaseDetailPage(): boolean {
  const pathname = window.location.pathname;
  const url = window.location.href;

  // 检测URL路径中是否包含案例详情页标识
  const hasDetailPath = pathname.includes("/judgment-documents/detail/");

  console.log('[Wkinfo检测] 页面类型检测:', {
    url,
    pathname,
    hasDetailPath,
    isCaseDetailPage: hasDetailPath
  });

  return hasDetailPath;
}

/**
 * 综合检测：是否为威科先行的案例详情页
 * @returns 是否匹配
 */
export function isWkinfoPage(): boolean {
  const isSite = isWkinfoSite();
  const isDetailPage = isWkinfoCaseDetailPage();
  const result = isSite && isDetailPage;

  console.log('[Wkinfo检测] 综合检测结果:', {
    isSite,
    isDetailPage,
    finalResult: result
  });

  return result;
}

/**
 * 获取威科先行站点信息
 * @returns 站点信息对象
 */
export function getWkinfoSiteInfo() {
  return {
    name: "wkinfo",
    domain: window.location.hostname,
    type: "legal",
    pageType: "case-detail",
    url: window.location.href,
    title: document.title,
  };
}
