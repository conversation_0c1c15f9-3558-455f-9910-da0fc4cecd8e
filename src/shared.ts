/**
 * 共享类型定义和工具函数
 * 在 background script 和 content script 之间共享
 */

// ========================
// 通用类型定义
// ========================

/**
 * 消息类型
 */
export type MessageType = 
  | 'PING'
  | 'GET_SITE_INFO'
  | 'SITE_INFO'
  | 'TRIGGER_DOWNLOAD_BUTTON'
  | 'DOWNLOAD_BUTTON_DETECTED'
  | 'EXTENSION_SIMULATED_CLICK'
  | 'PROCESS_INTERCEPTED_FILE'
  | 'CHECK_AUTH_STATUS'
  | 'OPEN_SIDE_PANEL'
  | 'LOGIN_COMPLETE'
  | 'LOGIN_TAB_CLOSED'
  | 'TEST_MESSAGE';

/**
 * 通用消息接口
 */
export interface Message {
  type: MessageType;
  data?: any;
  tabId?: number;
  timestamp?: number;
}

/**
 * 站点信息
 */
export interface SiteInfo {
  name: string;
  domain: string;
  version?: string;
  type?: string;
  pageType?: string;
  url?: string;
  title?: string;
  caseInfo?: CaseInfo;
}

/**
 * 案件信息
 */
export interface CaseInfo {
  id: string;
  title: string;
  court?: string;
  date?: string;
  caseNumber?: string;
}



/**
 * 认证状态
 */
export interface AuthStatus {
  isAuthenticated: boolean;
  error?: string;
  user?: User;
  statusCode?: number;
}

/**
 * 用户信息
 */
export interface User {
  id: string;
  email: string;
  name?: string;
  avatar?: string;
  role?: string;
}

/**
 * 威科先行版本枚举
 */
export enum WkinfoVersion {
  NEW = 'new',
  OLD = 'old',
  UNKNOWN = 'unknown'
}

// ========================
// 工具函数
// ========================

/**
 * 统一的日志函数
 * @param module 模块名称
 * @param message 消息
 * @param data 额外数据
 */
export function log(module: string, message: string, data?: any): void {
  const timestamp = new Date().toISOString();
  const prefix = `[${module}]`;
  
  if (data !== undefined) {
    console.log(`${prefix} ${message}`, data);
  } else {
    console.log(`${prefix} ${message}`);
  }
}

/**
 * 错误日志函数
 * @param module 模块名称
 * @param message 消息
 * @param error 错误对象
 */
export function logError(module: string, message: string, error?: any): void {
  const timestamp = new Date().toISOString();
  const prefix = `[${module}]`;
  
  if (error !== undefined) {
    console.error(`${prefix} ${message}`, error);
  } else {
    console.error(`${prefix} ${message}`);
  }
}

/**
 * 安全执行函数，避免异常导致整个脚本崩溃
 * @param fn 要执行的函数
 * @param defaultValue 出错时的默认返回值
 * @param module 模块名称，用于日志
 */
export function safeExecute<T>(
  fn: () => T, 
  defaultValue: T, 
  module: string = 'Unknown'
): T {
  try {
    return fn();
  } catch (error) {
    logError(module, '函数执行失败', error);
    return defaultValue;
  }
}

/**
 * 等待指定时间
 * @param ms 毫秒数
 */
export function sleep(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * 带超时的Promise包装器
 * @param promise 原始Promise
 * @param timeoutMs 超时时间（毫秒）
 * @param timeoutError 超时错误消息
 */
export function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number,
  timeoutError: string = '操作超时'
): Promise<T> {
  return Promise.race([
    promise,
    new Promise<never>((_, reject) => {
      setTimeout(() => reject(new Error(timeoutError)), timeoutMs);
    })
  ]);
}

/**
 * 重试函数
 * @param fn 要重试的函数
 * @param maxRetries 最大重试次数
 * @param delayMs 重试间隔（毫秒）
 */
export async function retry<T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delayMs: number = 1000
): Promise<T> {
  let lastError: Error;
  
  for (let i = 0; i <= maxRetries; i++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      
      if (i < maxRetries) {
        await sleep(delayMs);
      }
    }
  }
  
  throw lastError!;
}

/**
 * 验证URL是否有效
 * @param url URL字符串
 */
export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

/**
 * 从URL中提取文件名
 * @param url URL字符串
 * @param defaultName 默认文件名
 */
export function extractFilenameFromUrl(url: string, defaultName: string = 'document'): string {
  try {
    const urlObj = new URL(url);
    const pathname = urlObj.pathname;
    const filename = pathname.split('/').pop() || defaultName;
    
    // 如果没有扩展名，默认添加.doc
    if (!filename.includes('.')) {
      return filename + '.doc';
    }
    
    return filename;
  } catch {
    return defaultName + '.doc';
  }
}

/**
 * 检查是否为有效的文档文件
 * @param filename 文件名
 */
export function isValidDocumentFile(filename: string): boolean {
  const docExtensions = ['.doc', '.docx', '.pdf', '.txt'];
  const lowerFilename = filename.toLowerCase();
  return docExtensions.some(ext => lowerFilename.endsWith(ext));
}

/**
 * 格式化文件大小
 * @param bytes 字节数
 */
export function formatFileSize(bytes: number): string {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

/**
 * 格式化时间戳
 * @param timestamp 时间戳
 */
export function formatTimestamp(timestamp: number): string {
  return new Date(timestamp).toLocaleString('zh-CN');
}

/**
 * 深拷贝对象
 * @param obj 要拷贝的对象
 */
export function deepClone<T>(obj: T): T {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }
  
  if (obj instanceof Date) {
    return new Date(obj.getTime()) as unknown as T;
  }
  
  if (obj instanceof Array) {
    return obj.map(item => deepClone(item)) as unknown as T;
  }
  
  if (typeof obj === 'object') {
    const cloned = {} as T;
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        cloned[key] = deepClone(obj[key]);
      }
    }
    return cloned;
  }
  
  return obj;
}

// ========================
// 常量定义
// ========================

/**
 * 扩展相关常量
 */
export const EXTENSION_CONSTANTS = {
  // 存储键名
  STORAGE_KEYS: {
    LAST_INTERCEPTED_URL: 'lastInterceptedDownloadUrl',
    HAS_NEW_INTERCEPTED_URL: 'hasNewInterceptedUrl',
    INTERCEPTED_URL_TIMESTAMP: 'interceptedUrlTimestamp',
  },
  
  // 网络拦截规则ID
  NETWORK_RULE_ID: 999999,
  
  // 超时时间（毫秒）
  TIMEOUTS: {
    DOWNLOAD: 30000,
    UPLOAD: 60000,
    AUTH_CHECK: 15000,
    INTERCEPT_WAIT: 15000,
  },
  
  // 重试配置
  RETRY: {
    MAX_ATTEMPTS: 3,
    DELAY_MS: 1000,
  },
  
  // 文件大小限制（字节）
  FILE_LIMITS: {
    MAX_SIZE: 50 * 1024 * 1024, // 50MB
  },
} as const;