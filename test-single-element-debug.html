<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>单元素选择调试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .problem-indicator {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #721c24;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🐛 单元素选择调试</h1>
        
        <div class="problem-indicator">
            <strong>❌ 发现的问题</strong>：非跨元素或跨表格的选中不能实现文本高亮
        </div>

        <div class="test-case">
            <h3>测试1: 单个文本节点内的选择</h3>
            <div class="test-text" id="single-text">
                这是一段连续的文本内容，请选择其中的一部分文字进行测试。
            </div>
            <p><strong>测试方法</strong>：选择上面文本中的部分内容，如"连续的文本"</p>
        </div>

        <div class="test-case">
            <h3>测试2: 跨元素选择（对比）</h3>
            <div class="test-text">
                这段文本包含<strong>粗体部分</strong>和<em>斜体部分</em>，选择跨越元素的文字。
            </div>
            <p><strong>测试方法</strong>：选择从"粗体"到"斜体"的文字</p>
        </div>

        <div class="test-case">
            <h3>测试3: 表格内单元格选择</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        单元格内的连续文本内容，选择其中一部分
                    </td>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        另一个单元格的文本内容
                    </td>
                </tr>
            </table>
            <p><strong>测试方法</strong>：在单个单元格内选择部分文字</p>
        </div>

        <div id="debug-output" class="debug-info">
            等待文本选择...
        </div>
    </div>

    <script>
        // 监听文本选择事件
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            const debugOutput = document.getElementById('debug-output');
            
            if (selection && !selection.isCollapsed) {
                const selectedText = selection.toString();
                const range = selection.getRangeAt(0);
                
                // 详细分析选择范围
                const analysis = analyzeSelection(range);
                
                debugOutput.textContent = `
=== 选择分析 ===
选中文本: "${selectedText}"
文本长度: ${selectedText.length}

=== 范围信息 ===
起始容器: ${analysis.startContainer}
结束容器: ${analysis.endContainer}
起始偏移: ${range.startOffset}
结束偏移: ${range.endOffset}
公共祖先: ${analysis.commonAncestor}

=== 选择类型 ===
是否跨元素: ${analysis.isCrossElement ? '是' : '否'}
是否单文本节点: ${analysis.isSingleTextNode ? '是' : '否'}
是否部分选择: ${analysis.isPartialSelection ? '是' : '否'}

=== TextHighlighter模拟 ===
${simulateTextHighlighter(range)}
                `;
                
                console.log('选择详情:', analysis);
            } else {
                debugOutput.textContent = '等待文本选择...';
            }
        });
        
        function analyzeSelection(range) {
            const startContainer = range.startContainer;
            const endContainer = range.endContainer;
            
            return {
                startContainer: getNodeDescription(startContainer),
                endContainer: getNodeDescription(endContainer),
                commonAncestor: getNodeDescription(range.commonAncestorContainer),
                isCrossElement: startContainer !== endContainer,
                isSingleTextNode: startContainer === endContainer && startContainer.nodeType === Node.TEXT_NODE,
                isPartialSelection: isPartialSelection(range)
            };
        }
        
        function getNodeDescription(node) {
            if (node.nodeType === Node.TEXT_NODE) {
                return `TEXT_NODE: "${node.textContent.substring(0, 20)}..."`;
            } else if (node.nodeType === Node.ELEMENT_NODE) {
                return `ELEMENT_NODE: <${node.tagName.toLowerCase()}>`;
            } else {
                return `${node.nodeType}: ${node.nodeName}`;
            }
        }
        
        function isPartialSelection(range) {
            if (range.startContainer === range.endContainer && 
                range.startContainer.nodeType === Node.TEXT_NODE) {
                const textNode = range.startContainer;
                const fullLength = textNode.textContent.length;
                return range.startOffset > 0 || range.endOffset < fullLength;
            }
            return true;
        }
        
        function simulateTextHighlighter(range) {
            let result = '';
            
            try {
                // 模拟getTextFragments
                result += '1. getTextFragments():\n';
                
                const walker = document.createTreeWalker(
                    range.commonAncestorContainer,
                    NodeFilter.SHOW_TEXT,
                    {
                        acceptNode: (node) => {
                            const textNode = node;
                            
                            // 排除空白字符节点
                            if (!textNode.textContent?.trim()) {
                                return NodeFilter.FILTER_REJECT;
                            }
                            
                            // 检查是否与选择范围相交
                            return range.intersectsNode(textNode) 
                                ? NodeFilter.FILTER_ACCEPT 
                                : NodeFilter.FILTER_REJECT;
                        }
                    }
                );
                
                const textNodes = [];
                let node;
                while (node = walker.nextNode()) {
                    textNodes.push(node);
                }
                
                result += `   找到 ${textNodes.length} 个文本节点\n`;
                
                // 模拟calculateTextFragment
                result += '2. calculateTextFragment():\n';
                const fragments = [];
                
                textNodes.forEach((textNode, index) => {
                    const fragment = calculateTextFragment(range, textNode);
                    if (fragment) {
                        fragments.push(fragment);
                        result += `   片段${index}: "${fragment.text}" (${fragment.startOffset}-${fragment.endOffset})\n`;
                    } else {
                        result += `   片段${index}: null (被过滤)\n`;
                    }
                });
                
                result += `3. 最终结果: ${fragments.length} 个有效片段\n`;
                
                if (fragments.length === 0) {
                    result += '❌ 问题：没有有效片段，高亮将失败！';
                } else {
                    result += '✅ 正常：有有效片段，高亮应该成功';
                }
                
            } catch (error) {
                result += `❌ 错误: ${error.message}`;
            }
            
            return result;
        }
        
        function calculateTextFragment(range, textNode) {
            try {
                let startOffset = 0;
                let endOffset = textNode.textContent?.length || 0;
                
                // 如果选择范围的起点在这个文本节点内
                if (range.startContainer === textNode) {
                    startOffset = Math.max(startOffset, range.startOffset);
                } else if (range.comparePoint(textNode, 0) > 0) {
                    return null;
                }
                
                // 如果选择范围的终点在这个文本节点内
                if (range.endContainer === textNode) {
                    endOffset = Math.min(endOffset, range.endOffset);
                } else if (range.comparePoint(textNode, textNode.textContent?.length || 0) < 0) {
                    return null;
                }
                
                // 验证范围有效性
                if (startOffset >= endOffset) {
                    return null;
                }
                
                return {
                    textNode,
                    startOffset,
                    endOffset,
                    text: textNode.textContent.substring(startOffset, endOffset)
                };
            } catch (error) {
                return null;
            }
        }
    </script>
</body>
</html>
