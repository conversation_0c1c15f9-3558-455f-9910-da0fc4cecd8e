const path = require("path");
const CopyPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");
const MiniCssExtractPlugin = require("mini-css-extract-plugin");
const webpack = require("webpack");
const { setupEnvironment } = require("./webpack.env");

// 设置环境变量
const { env, defineConfig } = setupEnvironment(
  process.env.NODE_ENV || "development"
);

module.exports = {
  entry: {
    background: path.resolve("src/background.ts"),
    contentScript: path.resolve("src/content.ts"),
    sidepanel: path.resolve("src/sidepanel.tsx"),
    "popup/popup": path.resolve("src/popup/index.tsx"),
  },
  module: {
    rules: [
      {
        test: /\.(js|ts|tsx)$/,
        use: "ts-loader",
        exclude: /node_modules/,
      },
      {
        test: /\.css$/i,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: "css-loader",
            options: {
              importLoaders: 1,
            },
          },
          "postcss-loader",
        ],
      },
      {
        test: /\.(png|jpg|jpeg|gif|svg)$/i,
        type: "asset/resource",
        generator: {
          filename: "assets/[name][ext]",
        },
      },
    ],
  },
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
    modules: [path.resolve(__dirname, "src"), "node_modules"],
    alias: {
      // 强制所有模块使用同一个 React 实例，解决 React Hook 冲突问题
      react: path.resolve(__dirname, "node_modules/react"),
      "react-dom": path.resolve(__dirname, "node_modules/react-dom"),
    },
  },
  plugins: [
    // 注入环境变量到客户端代码
    new webpack.DefinePlugin(defineConfig),
    new CopyPlugin({
      patterns: [
        { from: "src/manifest.json", to: "manifest.json" },
        { from: "src/assets", to: "assets", noErrorOnMissing: true },
        {
          from: "src/contentScript/highlight/styles",
          to: "contentScript/highlight/styles",
          noErrorOnMissing: true,
        },
      ],
    }),
    new HtmlWebpackPlugin({
      template: path.resolve("src/sidepanel.html"),
      filename: "sidepanel.html",
      chunks: ["sidepanel"],
      inject: true,
    }),
    new HtmlWebpackPlugin({
      template: path.resolve("src/popup/popup.html"),
      filename: "popup/popup.html",
      chunks: ["popup/popup"],
      inject: true,
    }),
    new MiniCssExtractPlugin({
      filename: "[name].css",
    }),
  ],
  output: {
    filename: "[name].js",
    path: path.resolve(__dirname, "dist"),
    clean: true,
  },
  optimization: {
    splitChunks: {
      chunks: "all",
    },
  },
};
