<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部分文本选择修复验证</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .remio-comparison {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .highlight-demo {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 部分文本选择修复验证</h1>
        
        <div class="success-indicator">
            <strong>✅ 修复完成！</strong> 现在应该可以选择任意部分文本并成功创建高亮。
        </div>

        <div class="remio-comparison">
            <h3>📋 修复对比</h3>
            <p><strong>修复前</strong>：选择部分文本时，surroundContents()方法失败，无法创建高亮</p>
            <p><strong>修复后</strong>：使用安全的文本分割策略，与Remio相同的实现方式</p>
            
            <div class="code-example">
                <strong>Remio的成功示例：</strong><br>
                原始：crime of illegal mining; cross-administrative<br>
                结果：crime of illegal m&lt;span&gt;ining; cross-adm&lt;/span&gt;inistrative
            </div>
        </div>

        <h2>🧪 测试案例</h2>
        
        <div class="test-case">
            <h3>案例1: 单个文本节点内的部分选择</h3>
            <div class="test-text">
                criminal; civil public interest litigation case incidental to criminal proceedings; crime of illegal mining; cross-administrative divisions; designated jurisdiction; restoration of the affected area
            </div>
            <p><strong>测试方法</strong>：选择上面文本中的任意部分，比如"mining; cross-adm"</p>
            <p><strong>预期结果</strong>：部分文本被成功高亮，不影响其他文本</p>
        </div>

        <div class="test-case">
            <h3>案例2: 跨HTML元素的部分选择</h3>
            <div class="test-text">
                这段文本包含<strong>粗体部分内容</strong>和<em>斜体部分内容</em>，请尝试选择跨越这些元素的部分文字进行高亮测试。
            </div>
            <p><strong>测试方法</strong>：选择从"粗体部分"到"斜体部分"的文字</p>
            <p><strong>预期结果</strong>：每个文本片段被独立高亮，保持HTML结构</p>
        </div>

        <div class="test-case">
            <h3>案例3: 列表项中的部分选择</h3>
            <ul>
                <li class="test-text">First item: criminal; civil public interest litigation case incidental to criminal proceedings</li>
                <li class="test-text">Second item: crime of illegal mining; cross-administrative divisions; designated jurisdiction</li>
                <li class="test-text">Third item: restoration of the affected area and environmental protection measures</li>
            </ul>
            <p><strong>测试方法</strong>：在任意列表项中选择部分文本</p>
            <p><strong>预期结果</strong>：部分文本高亮成功，列表结构不受影响</p>
        </div>

        <div class="test-case">
            <h3>案例4: 表格中的部分选择</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        criminal proceedings and civil litigation cases
                    </td>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        illegal mining activities and environmental restoration
                    </td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        cross-administrative divisions and jurisdiction issues
                    </td>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        designated jurisdiction and affected area restoration
                    </td>
                </tr>
            </table>
            <p><strong>测试方法</strong>：在表格单元格中选择部分文本</p>
            <p><strong>预期结果</strong>：单元格内的部分文本被高亮，表格结构完整</p>
        </div>

        <h2>🔍 技术改进详情</h2>
        
        <div class="remio-comparison">
            <h3>核心修复点</h3>
            <ol>
                <li><strong>移除surroundContents限制</strong>：不再使用有限制的surroundContents()方法</li>
                <li><strong>实现安全文本分割</strong>：将文本节点分割为前缀+高亮+后缀三部分</li>
                <li><strong>使用DocumentFragment</strong>：一次性替换，避免DOM操作中断</li>
                <li><strong>增强错误处理</strong>：更好的边界情况处理</li>
            </ol>
            
            <div class="code-example">
                <strong>新的安全算法：</strong><br>
                1. 分割文本：beforeText + highlightText + afterText<br>
                2. 创建DocumentFragment<br>
                3. 添加三个部分到fragment<br>
                4. 一次性替换原始文本节点
            </div>
        </div>
    </div>

    <script>
        console.log('部分文本选择修复验证页面加载完成');
        
        // 监听文本选择事件
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            if (selection && !selection.isCollapsed) {
                const selectedText = selection.toString();
                console.log('文本选择测试:', {
                    text: selectedText.substring(0, 50) + (selectedText.length > 50 ? '...' : ''),
                    length: selectedText.length,
                    isPartialSelection: isPartialSelection(selection.getRangeAt(0))
                });
            }
        });
        
        function isPartialSelection(range) {
            if (range.startContainer === range.endContainer && 
                range.startContainer.nodeType === Node.TEXT_NODE) {
                const textNode = range.startContainer;
                const fullLength = textNode.textContent.length;
                return range.startOffset > 0 || range.endOffset < fullLength;
            }
            return true;
        }
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，TextHighlighter修复已生效');
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);
    </script>
</body>
</html>
