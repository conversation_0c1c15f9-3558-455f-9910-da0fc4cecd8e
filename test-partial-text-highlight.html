<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部分文本高亮修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .fix-info {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        .highlight-example {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 部分文本高亮修复测试</h1>
        
        <div class="fix-info">
            <h3>✅ 修复内容</h3>
            <ul>
                <li><strong>问题</strong>：选择部分文本时无法创建高亮（文本片段数量为0）</li>
                <li><strong>原因</strong>：TextHighlighter中的边界检测逻辑错误</li>
                <li><strong>修复</strong>：修正comparePoint逻辑，增加详细调试日志</li>
            </ul>
        </div>

        <h2>🧪 测试用例</h2>
        
        <div class="test-section">
            <h3>测试1: 部分文本选择（原问题场景）</h3>
            <div class="test-text">
                （1993年9月2日第八届全国人民代表大会常务委员会第三次会议通过　2017年11月4日第十二届全国人民代表大会常务委员会第三十次会议第一次修订　根据2019年4月23日第十三届全国人民代表大会常务委员会第十次会议《关于修改〈中华人民共和国建筑法〉等八部法律的决定》第二次修订）
            </div>
            <p><strong>测试方法</strong>：选择"根据2019年4月23日"这样的部分文本</p>
            <p><strong>预期结果</strong>：能够成功创建高亮，控制台显示"找到文本片段数量: 1"</p>
        </div>

        <div class="test-section">
            <h3>测试2: 跨元素部分选择</h3>
            <div class="test-text">
                这是一段包含<strong>粗体文字</strong>和<em>斜体文字</em>的复杂文本，用于测试跨越多个HTML元素的部分文本选择功能。
            </div>
            <p><strong>测试方法</strong>：选择"包含粗体文字和斜体"这样跨越多个元素的文本</p>
            <p><strong>预期结果</strong>：每个文本片段被正确识别和高亮</p>
        </div>

        <div class="test-section">
            <h3>测试3: 单词级别选择</h3>
            <div class="test-text">
                The quick brown fox jumps over the lazy dog. This is a test sentence for partial text selection.
            </div>
            <p><strong>测试方法</strong>：选择单个单词如"quick"或短语如"brown fox"</p>
            <p><strong>预期结果</strong>：精确高亮选中的单词或短语</p>
        </div>

        <div class="test-section">
            <h3>测试4: 中文标点符号</h3>
            <div class="test-text">
                中华人民共和国建筑法（修订版）：第一条、第二条、第三条。本法适用于在中华人民共和国境内从事建筑活动，实施对建筑活动的监督管理。
            </div>
            <p><strong>测试方法</strong>：选择包含标点符号的文本，如"第一条、第二条"</p>
            <p><strong>预期结果</strong>：标点符号也被正确包含在高亮中</p>
        </div>

        <h2>🔍 调试信息</h2>
        <div class="debug-info" id="debugInfo">
            打开浏览器控制台查看详细的调试日志...
        </div>

        <h2>📊 修复对比</h2>
        <div class="test-section">
            <h3>修复前 vs 修复后</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <thead>
                    <tr style="background-color: #f2f2f2;">
                        <th style="border: 1px solid #ddd; padding: 8px;">场景</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">修复前</th>
                        <th style="border: 1px solid #ddd; padding: 8px;">修复后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">部分文本选择</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">❌ 文本片段数量: 0</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">✅ 文本片段数量: 1+</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">边界检测</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">❌ comparePoint逻辑错误</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">✅ 逻辑修正，增加容错</td>
                    </tr>
                    <tr>
                        <td style="border: 1px solid #ddd; padding: 8px;">调试能力</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">❌ 缺少详细日志</td>
                        <td style="border: 1px solid #ddd; padding: 8px;">✅ 完整的调试日志</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        console.log('部分文本高亮修复测试页面加载完成');
        
        // 监听文本选择事件
        document.addEventListener('mouseup', (e) => {
            const selection = window.getSelection();
            if (selection && !selection.isCollapsed) {
                const selectedText = selection.toString();
                console.log('=== 文本选择测试 ===');
                console.log('选中文本:', selectedText);
                console.log('选择范围:', {
                    startContainer: selection.getRangeAt(0).startContainer,
                    startOffset: selection.getRangeAt(0).startOffset,
                    endContainer: selection.getRangeAt(0).endContainer,
                    endOffset: selection.getRangeAt(0).endOffset
                });
                
                // 更新调试信息显示
                const debugInfo = document.getElementById('debugInfo');
                debugInfo.innerHTML = `
                    <strong>最近选择:</strong> "${selectedText.substring(0, 50)}${selectedText.length > 50 ? '...' : ''}"<br>
                    <strong>选择长度:</strong> ${selectedText.length} 字符<br>
                    <strong>时间:</strong> ${new Date().toLocaleTimeString()}<br>
                    <em>详细日志请查看控制台</em>
                `;
            }
        });

        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载');
                console.log('✅ TextHighlighter组件可用');
            } else {
                console.warn('❌ HighlightSystem未找到，请确保扩展已加载');
            }
        }, 2000);
    </script>
</body>
</html>
