/**
 * 环境变量配置模块
 * 专门用于 Webpack 构建时的环境变量处理
 */

const dotenv = require("dotenv");
const fs = require("fs");
const path = require("path");

/**
 * 必需的环境变量列表
 */
const REQUIRED_ENV_VARS = [
  "NEXT_PUBLIC_AUTH_SERVICE_API_URL",
  "API_BASE_URL",
  "NEXT_PUBLIC_DEFAULT_PROJECT_ID",
];

/**
 * 可选的环境变量列表（带默认值）
 */
const OPTIONAL_ENV_VARS = {
  NEXT_PUBLIC_REQUEST_TIMEOUT: "15000",
  NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL: "",
  NEXT_PUBLIC_AUTH_DOMAIN: "",
};

/**
 * 加载环境变量
 * @param {string} mode - 构建模式 (development/production)
 * @returns {object} 处理后的环境变量对象
 */
function loadEnvironmentVariables(mode = "development") {
  console.log(`🔧 加载环境变量 (模式: ${mode})`);

  // 1. 加载默认 .env 文件
  const defaultEnvPath = path.resolve(__dirname, ".env");
  const defaultEnv = fs.existsSync(defaultEnvPath)
    ? dotenv.parse(fs.readFileSync(defaultEnvPath))
    : {};

  // 2. 加载特定环境的 .env 文件
  const envPath = path.resolve(__dirname, `.env.${mode}`);
  const envConfig = fs.existsSync(envPath)
    ? dotenv.parse(fs.readFileSync(envPath))
    : {};

  // 3. 合并环境变量（优先级：系统环境变量 > 特定环境文件 > 默认文件）
  const mergedEnv = { ...defaultEnv, ...envConfig, ...process.env };

  // 4. 只选择需要注入到客户端的环境变量
  const clientEnv = {};

  // 添加所有 NEXT_PUBLIC_ 前缀的变量
  Object.keys(mergedEnv).forEach((key) => {
    if (key.startsWith("NEXT_PUBLIC_")) {
      clientEnv[key] = mergedEnv[key];
    }
  });

  // 添加特定的非 NEXT_PUBLIC_ 变量（用于服务端）
  const serverOnlyVars = ["API_BASE_URL"];
  serverOnlyVars.forEach((key) => {
    if (mergedEnv[key]) {
      clientEnv[key] = mergedEnv[key];
    }
  });

  // 添加 NODE_ENV
  clientEnv.NODE_ENV = mode;

  // 5. 验证必需的环境变量
  const missingVars = REQUIRED_ENV_VARS.filter(
    (varName) => !clientEnv[varName]
  );
  if (missingVars.length > 0) {
    console.error("❌ 缺少必需的环境变量:");
    missingVars.forEach((varName) => {
      console.error(`   - ${varName}`);
    });
    console.error("\n💡 解决方案:");
    console.error("   1. 检查 .env 文件是否存在");
    console.error("   2. 确保所有必需变量都已设置");
    console.error("   3. 参考 .env.example 文件");
    throw new Error("环境变量验证失败");
  }

  // 6. 设置可选环境变量的默认值
  Object.keys(OPTIONAL_ENV_VARS).forEach((varName) => {
    if (!clientEnv[varName]) {
      clientEnv[varName] = OPTIONAL_ENV_VARS[varName];
      console.log(`📝 使用默认值: ${varName} = ${OPTIONAL_ENV_VARS[varName]}`);
    }
  });

  // 7. 打印加载的环境变量（生产环境下隐藏敏感信息）
  console.log("✅ 环境变量加载完成:");
  Object.keys(clientEnv).forEach((key) => {
    const value = clientEnv[key];
    // 在生产环境下隐藏敏感信息
    const displayValue =
      mode === "production" && key.includes("URL")
        ? value.replace(/\/\/[^\/]+/, "//***")
        : value;
    console.log(`   ${key}: ${displayValue}`);
  });

  return clientEnv;
}

/**
 * 创建 Webpack DefinePlugin 配置
 * @param {object} env - 环境变量对象
 * @returns {object} DefinePlugin 配置对象
 */
function createDefinePluginConfig(env) {
  const config = {};

  // 将每个环境变量单独注入，避免整个 process.env 对象被注入
  Object.keys(env).forEach((key) => {
    // 跳过 NODE_ENV，因为 Webpack 会自动处理
    if (key !== "NODE_ENV") {
      config[`process.env.${key}`] = JSON.stringify(env[key]);
    }
  });

  // 单独处理 NODE_ENV，确保没有冲突
  config["process.env.NODE_ENV"] = JSON.stringify(env.NODE_ENV);

  return config;
}

/**
 * 验证环境变量的有效性
 * @param {object} env - 环境变量对象
 */
function validateEnvironmentVariables(env) {
  const errors = [];

  // 验证 URL 格式
  const urlVars = Object.keys(env).filter((key) => key.includes("URL"));
  urlVars.forEach((key) => {
    const value = env[key];
    if (value && !value.match(/^https?:\/\/.+/)) {
      errors.push(`${key} 必须是有效的 HTTP/HTTPS URL`);
    }
  });

  // 验证项目 ID 格式（UUID）
  const projectId = env.NEXT_PUBLIC_DEFAULT_PROJECT_ID;
  if (
    projectId &&
    !projectId.match(
      /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i
    )
  ) {
    errors.push("NEXT_PUBLIC_DEFAULT_PROJECT_ID 必须是有效的 UUID 格式");
  }

  if (errors.length > 0) {
    console.error("❌ 环境变量验证失败:");
    errors.forEach((error) => console.error(`   - ${error}`));
    throw new Error("环境变量格式验证失败");
  }
}

/**
 * 主要导出函数
 * @param {string} mode - 构建模式
 * @returns {object} 包含环境变量和 DefinePlugin 配置的对象
 */
function setupEnvironment(mode) {
  try {
    const env = loadEnvironmentVariables(mode);
    validateEnvironmentVariables(env);
    const defineConfig = createDefinePluginConfig(env);

    return {
      env,
      defineConfig,
      requiredVars: REQUIRED_ENV_VARS,
      optionalVars: OPTIONAL_ENV_VARS,
    };
  } catch (error) {
    console.error("🚨 环境变量设置失败:", error.message);
    process.exit(1);
  }
}

module.exports = {
  setupEnvironment,
  loadEnvironmentVariables,
  createDefinePluginConfig,
  validateEnvironmentVariables,
  REQUIRED_ENV_VARS,
  OPTIONAL_ENV_VARS,
};
