<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格内外混合高亮测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #555;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 15px 0;
        }
        td, th {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .expected {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="content">
        <h1>表格内外混合高亮测试</h1>
        
        <div class="instructions">
            <h3>🎯 测试目标</h3>
            <p>验证新的分区域处理算法能够正确处理表格内外混合选择，确保表格布局不被破坏。</p>
            <p><strong>重点</strong>：选择跨越表格外文本和表格内文本时，表格布局应保持不变。</p>
        </div>

        <div class="test-case">
            <h3>测试案例1: 段落到表格的混合选择</h3>
            <p>这是表格前的段落文本，包含重要信息需要高亮。</p>
            <table>
                <tr>
                    <th>列标题1</th>
                    <th>列标题2</th>
                </tr>
                <tr>
                    <td>单元格1内容</td>
                    <td>单元格2内容</td>
                </tr>
                <tr>
                    <td>单元格3内容</td>
                    <td>单元格4内容</td>
                </tr>
            </table>
            <p>这是表格后的段落文本，也包含重要信息。</p>
            
            <div class="expected">
                <strong>测试方法</strong>：选择从"包含重要信息"到"单元格2内容"的跨区域文本。<br>
                <strong>预期结果</strong>：
                <ul>
                    <li>段落文本正常高亮（普通样式）</li>
                    <li>表格内文本高亮但不影响表格布局</li>
                    <li>控制台显示检测到2个区域：normal + table</li>
                </ul>
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例2: 表格到段落的混合选择</h3>
            <table>
                <tr>
                    <td>起始单元格内容</td>
                    <td>中间单元格内容</td>
                </tr>
                <tr>
                    <td>结束单元格内容</td>
                    <td>最后单元格内容</td>
                </tr>
            </table>
            <p>表格后的文本内容，需要一起被选择和高亮。</p>
            
            <div class="expected">
                <strong>测试方法</strong>：选择从"起始单元格内容"到"一起被选择"的跨区域文本。<br>
                <strong>预期结果</strong>：表格布局保持不变，段落文本正常高亮。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例3: 段落-表格-段落的复杂混合</h3>
            <p>第一段文本内容。</p>
            <table>
                <tr>
                    <td>表格内容A</td>
                    <td>表格内容B</td>
                </tr>
            </table>
            <p>第二段文本内容。</p>
            
            <div class="expected">
                <strong>测试方法</strong>：选择从"第一段文本"到"第二段文本"的完整跨区域文本。<br>
                <strong>预期结果</strong>：控制台显示检测到3个区域：normal + table + normal。
            </div>
        </div>

        <div class="test-case">
            <h3>测试案例4: 嵌套表格测试</h3>
            <p>外层文本开始。</p>
            <div style="border: 1px solid #ccc; padding: 10px; margin: 10px 0;">
                <p>容器内的文本。</p>
                <table>
                    <tr>
                        <td>嵌套表格内容1</td>
                        <td>嵌套表格内容2</td>
                    </tr>
                </table>
                <p>容器内的结束文本。</p>
            </div>
            <p>外层文本结束。</p>
            
            <div class="expected">
                <strong>测试方法</strong>：选择从"外层文本开始"到"外层文本结束"的全部内容。<br>
                <strong>预期结果</strong>：正确识别不同区域，表格布局不受影响。
            </div>
        </div>

        <h2>🔍 调试信息</h2>
        <p>打开浏览器控制台查看详细的分区域处理日志：</p>
        <ul>
            <li><code>[HighlightSystem] 分析到 X 个区域: [normal, table, ...]</code></li>
            <li>每个区域的处理结果</li>
            <li>元素创建和样式应用过程</li>
        </ul>

        <h2>✅ 验证清单</h2>
        <ul>
            <li>[ ] 表格布局在高亮后保持不变</li>
            <li>[ ] 表格内文本正确高亮（使用table专用样式）</li>
            <li>[ ] 普通文本正确高亮（使用标准样式）</li>
            <li>[ ] 控制台显示正确的区域分析结果</li>
            <li>[ ] hover任何高亮部分都能显示工具栏</li>
            <li>[ ] 删除高亮时所有相关部分都被移除</li>
        </ul>
    </div>

    <script>
        console.log('表格内外混合高亮测试页面加载完成');
        
        // 监听选择事件，显示选择信息
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            if (selection && !selection.isCollapsed) {
                console.log('选择文本:', {
                    text: selection.toString(),
                    rangeCount: selection.rangeCount,
                    startContainer: selection.getRangeAt(0).startContainer,
                    endContainer: selection.getRangeAt(0).endContainer
                });
            }
        });
        
        // 添加测试按钮
        setTimeout(() => {
            const button = document.createElement('button');
            button.textContent = '手动测试工具栏';
            button.style.position = 'fixed';
            button.style.top = '10px';
            button.style.right = '10px';
            button.style.zIndex = '999999';
            button.style.padding = '10px';
            button.style.backgroundColor = '#007bff';
            button.style.color = 'white';
            button.style.border = 'none';
            button.style.borderRadius = '4px';
            button.onclick = () => {
                if (window.testHighlightSystem) {
                    window.testHighlightSystem();
                } else {
                    console.log('测试方法不存在');
                }
            };
            document.body.appendChild(button);
        }, 1000);
    </script>
</body>
</html>
