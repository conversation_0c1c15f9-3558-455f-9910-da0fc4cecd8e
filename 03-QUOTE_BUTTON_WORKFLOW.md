# "采集到Quote"按钮工作逻辑详解

## 🎯 概述

本文档详细说明注入在页面中的"采集到Quote"按钮的完整工作逻辑，包括按钮注入、状态管理、采集流程等。

## 🏗️ 1. 按钮组件架构

### 组件结构

**文件位置**: `src/contentScript/sites/wkinfo/components/QuoteButton/`

```
QuoteButton/
├── types.ts              # 类型定义和接口
├── styles.ts             # 样式管理器
├── StateManager.ts       # 状态管理器
├── DropdownMenu.ts       # 下拉菜单组件
├── QuoteButton.ts        # 主按钮组件
└── index.ts              # 入口文件和工厂函数
```

### 按钮布局设计

```
┌─────────────────────┬───┐
│  📄 采集到Quote     │ ▼ │
└─────────────────────┴───┘
    主按钮区域        下拉
```

**功能分区**:
- **左侧主按钮**: 执行采集功能，显示状态
- **右侧下拉**: 选择采集项目（默认"all"）

## 🎨 2. 状态管理系统

### 按钮状态定义

```typescript
enum ButtonState {
  IDLE = 'idle',           // 空闲状态
  PROCESSING = 'processing', // 处理中
  SUCCESS = 'success',     // 成功
  ERROR = 'error'          // 失败
}
```

### 状态视觉表现

| 状态 | 图标 | 文本 | 颜色 | 动画 |
|------|------|------|------|------|
| 空闲 | 📄 | 采集到Quote | 蓝色 | 无 |
| 处理中 | ⏳ | 正在采集... | 紫色 | 旋转 |
| 成功 | ✅ | 采集成功 | 绿色 | 无 |
| 失败 | ❌ | 采集失败 | 红色 | 无 |

### 状态管理器实现

```typescript
class StateManager {
  private currentState: ButtonState = ButtonState.IDLE;
  private autoResetTimer: number | null = null;
  
  setState(newState: ButtonState, autoReset: boolean = true): void {
    this.currentState = newState;
    this.updateButtonAppearance();
    
    // 自动重置机制
    if (autoReset && (newState === ButtonState.SUCCESS || newState === ButtonState.ERROR)) {
      this.scheduleAutoReset();
    }
  }
  
  private scheduleAutoReset(): void {
    if (this.autoResetTimer) {
      clearTimeout(this.autoResetTimer);
    }
    
    this.autoResetTimer = window.setTimeout(() => {
      this.setState(ButtonState.IDLE, false);
    }, 3000); // 3秒后自动重置
  }
}
```

## 🔄 3. 按钮注入逻辑

### 注入时机检测

**文件位置**: `src/contentScript/sites/wkinfo/dom/ButtonManager.ts`

```typescript
export function createQuoteButtonManager(): QuoteButtonManager {
  return {
    // 检查是否应该显示按钮
    shouldShowButton(): boolean {
      // 1. 检查域名和路径
      const isWkinfoSite = detectWkinfoSite();
      
      // 2. 检查页面是否有下载按钮
      const hasDownloadButton = findDownloadButton() !== null;
      
      // 3. 检查是否已经注入过按钮
      const alreadyInjected = document.querySelector('.quote-collect-button') !== null;
      
      return isWkinfoSite && hasDownloadButton && !alreadyInjected;
    },
    
    // 注入按钮到页面
    injectButton(): void {
      if (!this.shouldShowButton()) return;
      
      const targetContainer = this.findInjectionTarget();
      if (targetContainer) {
        const quoteButton = createQuoteButton(this.getButtonConfig());
        targetContainer.appendChild(quoteButton.getElement());
      }
    }
  };
}
```

### 注入位置策略

```typescript
private findInjectionTarget(): HTMLElement | null {
  // 优先级1: 查找特定的工具栏容器
  let target = document.querySelector('.toolbar-container');
  
  // 优先级2: 查找下载按钮的父容器
  if (!target) {
    const downloadButton = findDownloadButton();
    target = downloadButton?.closest('.button-group');
  }
  
  // 优先级3: 查找页面头部区域
  if (!target) {
    target = document.querySelector('.page-header .actions');
  }
  
  // 备选方案: 创建独立容器
  if (!target) {
    target = this.createFloatingContainer();
  }
  
  return target;
}
```

## ⚡ 4. 采集工作流程

### 完整执行流程

```typescript
// 主要回调函数
onCollect: async (project: string) => {
  console.log(`开始采集到项目: ${project}`);
  
  try {
    // 1. 检查认证状态
    const authStatus = await checkAuthenticationStatus();
    if (!authStatus.isAuthenticated) {
      await showLoginPrompt();
      await openSidePanel();
      throw new Error("请先登录后再进行采集");
    }
    
    // 2. 清理旧的拦截URL
    await chrome.storage.local.remove([
      'lastInterceptedDownloadUrl', 
      'hasNewInterceptedUrl',
      'interceptedUrlTimestamp'
    ]);
    
    // 3. 执行模拟下载逻辑
    await executeSimulateDownloadLogic(project);
    
    // 4. 等待拦截URL
    const interceptedUrl = await waitForInterceptedUrl(15000);
    
    // 5. 执行文件处理逻辑
    const result = await executeFileProcessingLogic(
      interceptedUrl,
      (progress) => console.log(`文件处理进度: ${progress}%`)
    );
    
    // 6. 清理已使用的拦截URL
    await chrome.storage.local.remove([
      'lastInterceptedDownloadUrl', 
      'hasNewInterceptedUrl',
      'interceptedUrlTimestamp'
    ]);
    
    console.log(`采集完成: ${project}`);
    
  } catch (error) {
    console.error("采集过程中出错:", error);
    throw error; // 让状态管理器处理错误显示
  }
}
```

### 4.1 模拟下载逻辑

```typescript
async function executeSimulateDownloadLogic(project: string): Promise<void> {
  console.log(`开始执行模拟下载逻辑，项目: ${project}`);
  
  // 1. 设置模拟点击标记
  (window as any).__EXTENSION_SIMULATED_CLICK = true;
  
  // 2. 模拟点击下载按钮
  const success = simulateDownloadButtonClick();
  if (!success) {
    throw new Error("未找到下载按钮或模拟点击失败");
  }
  
  // 3. 发送消息给后台脚本
  await chrome.runtime.sendMessage({
    type: "EXTENSION_SIMULATED_CLICK",
    data: {
      timestamp: Date.now(),
      source: "quote-button",
      project: project,
    },
  });
  
  console.log("模拟下载逻辑执行完成");
}
```

### 4.2 简化的消息传递

```typescript
// 简化的消息处理（在content.ts中）
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  console.log('[Content Script] 收到消息:', message);

  switch (message.type) {
    case 'PING':
      sendResponse({ success: true, message: 'Content script 正常运行' });
      return false;

    case 'TRIGGER_DOWNLOAD_BUTTON':
      // 触发下载按钮点击
      try {
        const success = simulateDownloadButtonClick();
        if (success) {
          // 发送模拟点击事件给后台脚本
          safeSendMessage({
            type: 'EXTENSION_SIMULATED_CLICK',
            data: {
              timestamp: Date.now(),
              source: 'sidepanel-trigger'
            }
          });
        }
        sendResponse({ success: success, message: success ? '成功模拟点击' : '未找到下载按钮' });
      } catch (error) {
        console.error('[Content Script] 模拟点击失败:', error);
        sendResponse({ success: false, message: '模拟点击失败', error: String(error) });
      }
      return false;

    default:
      console.log('[Content Script] 未知消息类型:', message.type);
      sendResponse({ success: false, message: '未知消息类型' });
      return false;
  }
});
```

## 🎭 5. 用户交互体验

### 点击反馈

```typescript
// 主按钮点击处理
mainButton.addEventListener('click', async (event) => {
  event.preventDefault();
  event.stopPropagation();
  
  // 防止重复点击
  if (stateManager.getCurrentState() === ButtonState.PROCESSING) {
    return;
  }
  
  // 设置处理中状态
  stateManager.setState(ButtonState.PROCESSING);
  
  try {
    // 执行采集逻辑
    await config.onCollect(dropdownMenu.getSelectedProject());
    
    // 设置成功状态
    stateManager.setState(ButtonState.SUCCESS);
    
  } catch (error) {
    // 设置失败状态
    stateManager.setState(ButtonState.ERROR);
    console.error("采集失败:", error);
  }
});
```

### 下拉菜单交互

```typescript
class DropdownMenu {
  private isOpen: boolean = false;
  
  toggle(): void {
    this.isOpen = !this.isOpen;
    this.updateVisibility();
  }
  
  selectProject(projectValue: string): void {
    this.selectedProject = projectValue;
    this.updateButtonText();
    this.close();
    
    // 触发项目变更回调
    if (this.config.onProjectChange) {
      this.config.onProjectChange(projectValue);
    }
  }
  
  private updateVisibility(): void {
    const menu = this.menuElement;
    if (this.isOpen) {
      menu.style.display = 'block';
      menu.style.opacity = '0';
      menu.style.transform = 'translateY(-10px)';
      
      // 动画显示
      requestAnimationFrame(() => {
        menu.style.opacity = '1';
        menu.style.transform = 'translateY(0)';
      });
    } else {
      menu.style.opacity = '0';
      menu.style.transform = 'translateY(-10px)';
      
      setTimeout(() => {
        menu.style.display = 'none';
      }, 200);
    }
  }
}
```

## 🔧 6. 错误处理与恢复

### 错误分类处理

```typescript
// 认证错误
if (error.message.includes("请先登录")) {
  stateManager.setState(ButtonState.ERROR);
  showAuthenticationPrompt();
}

// 网络错误
else if (error.message.includes("网络") || error.message.includes("超时")) {
  stateManager.setState(ButtonState.ERROR);
  showRetryPrompt("网络连接异常，请检查网络后重试");
}

// 文件处理错误
else if (error.message.includes("下载") || error.message.includes("解压")) {
  stateManager.setState(ButtonState.ERROR);
  showRetryPrompt("文件处理失败，请重试");
}

// 未知错误
else {
  stateManager.setState(ButtonState.ERROR);
  console.error("未知错误:", error);
}
```

### 自动恢复机制

```typescript
// 状态自动重置
private scheduleAutoReset(): void {
  this.autoResetTimer = window.setTimeout(() => {
    this.setState(ButtonState.IDLE, false);
  }, 3000);
}

// 网络重连检测
private setupNetworkRecovery(): void {
  window.addEventListener('online', () => {
    if (this.currentState === ButtonState.ERROR) {
      console.log("网络已恢复，重置按钮状态");
      this.setState(ButtonState.IDLE, false);
    }
  });
}
```

## 📊 7. 性能优化

### 懒加载机制

```typescript
// 动态导入避免循环依赖
let DownloadManager: any = null;
async function getDownloadManager() {
  if (!DownloadManager) {
    const module = await import("../../../../services/DownloadManager");
    DownloadManager = module.DownloadManager;
  }
  return DownloadManager;
}
```

### 内存管理

```typescript
// 组件销毁时清理资源
destroy(): void {
  // 清理定时器
  if (this.autoResetTimer) {
    clearTimeout(this.autoResetTimer);
  }
  
  // 移除事件监听器
  this.element.removeEventListener('click', this.clickHandler);
  
  // 清理DOM引用
  this.element.remove();
  this.element = null;
}
```

## 📋 总结

"采集到Quote"按钮的工作逻辑包括：

1. **智能注入**: 检测页面环境，在合适位置注入按钮
2. **状态管理**: 完整的状态系统，提供视觉反馈
3. **采集流程**: 自动化的完整采集和处理流程
4. **用户体验**: 流畅的交互和错误处理
5. **性能优化**: 懒加载和内存管理

整个系统确保了用户只需一键点击即可完成从文件采集到上传的全流程操作。
