{"name": "quote-browser-extension", "version": "0.1.0", "description": "一个浏览器引用扩展的 MVP 版本", "private": true, "scripts": {"dev": "cross-env NODE_ENV=development webpack --watch --config webpack.dev.js", "build": "cross-env NODE_ENV=production webpack --config webpack.prod.js", "build:dev": "cross-env NODE_ENV=development webpack --config webpack.prod.js", "build:staging": "cross-env NODE_ENV=staging webpack --config webpack.prod.js", "build:chrome": "cross-env NODE_ENV=production BROWSER=chrome webpack --config webpack.prod.js", "build:firefox": "cross-env NODE_ENV=production BROWSER=firefox webpack --config webpack.prod.js", "build:edge": "cross-env NODE_ENV=production BROWSER=edge webpack --config webpack.prod.js", "build:safari": "cross-env NODE_ENV=production BROWSER=safari webpack --config webpack.prod.js", "env:check": "node -e \"const { setupEnvironment } = require('./webpack.env'); setupEnvironment('development');\"", "env:validate": "node -e \"const { setupEnvironment } = require('./webpack.env'); setupEnvironment(process.env.NODE_ENV || 'development');\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:api": "jest tests/api", "test:components": "jest tests/components", "test:integration": "jest tests/integration", "lint": "eslint --ext .js,.ts,.tsx src/", "format": "prettier --write \"src/**/*.{js,ts,tsx,json,css,scss}\""}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@types/chrome": "^0.0.246", "@types/jest": "^30.0.0", "@types/react": "^18.3.23", "@types/react-dom": "^18.3.7", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "autoprefixer": "^10.4.16", "copy-webpack-plugin": "^11.0.0", "cross-env": "^7.0.3", "css-loader": "^6.8.1", "dotenv": "^17.0.0", "eslint": "^8.45.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-react": "^7.33.0", "html-webpack-plugin": "^5.5.3", "identity-obj-proxy": "^3.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "mini-css-extract-plugin": "^2.7.6", "postcss": "^8.4.31", "postcss-loader": "^7.3.3", "prettier": "^3.0.0", "style-loader": "^3.3.3", "tailwindcss": "^3.3.3", "terser-webpack-plugin": "^5.3.9", "ts-jest": "^29.4.0", "ts-loader": "^9.4.4", "typescript": "^5.1.6", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-merge": "^5.9.0"}}