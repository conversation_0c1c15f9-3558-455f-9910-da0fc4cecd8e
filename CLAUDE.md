# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Chrome browser extension for legal document collection from Chinese legal databases (primarily WKInfo/威科先行). The extension injects "采集到Quote" buttons into legal database pages, extracts content directly from pages, and uploads them to a unified Quote system with SSO authentication.

## Development Commands

```bash
# Development mode (watch mode)
npm run dev

# Build for production
npm run build

# Build for specific browsers
npm run build:chrome
npm run build:firefox
npm run build:edge
npm run build:safari

# Environment validation
npm run env:check
npm run env:validate

# Testing
npm run test
npm run test:watch
npm run test:coverage
npm run test:api
npm run test:components
npm run test:integration

# Code quality
npm run lint
npm run format
```

## Environment Configuration

**Critical**: All API URLs and configuration MUST be provided via environment variables with `NEXT_PUBLIC_` prefix. Never use hardcoded values.

Required environment variables:

- `NEXT_PUBLIC_AUTH_SERVICE_API_URL` - Authentication service URL
- `API_BASE_URL` - File upload service URL (server-side only, more secure)
- `NEXT_PUBLIC_DEFAULT_PROJECT_ID` - Project UUID (must be provided, no default value)

Optional environment variables:

- `NEXT_PUBLIC_REQUEST_TIMEOUT` - Request timeout (default: "15000")
- `NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL` - Auth redirect URL (default: "")
- `NEXT_PUBLIC_AUTH_DOMAIN` - Auth domain (default: "")

Copy `.env.example` to `.env` and configure before development. Environment variables are injected at build time via Webpack DefinePlugin, not runtime.

## Core Architecture

### Extension Structure

- **Background Script** (`src/background.ts`): Service worker handling authentication monitoring, project management, and content upload API calls
- **Content Scripts** (`src/contentScript/`): Site-specific adapters that inject UI components into legal database pages
- **Components** (`src/components/`): React components for sidepanel, authentication, and UI elements
- **Services** (`src/services/`): Core business logic for authentication and content extraction

### Site Adaptation System

Located in `src/contentScript/sites/`, currently supports:

- **WKInfo** (`wkinfo/`): Comprehensive adapter for 威科先行 with version detection (new/old versions)
- **PKULaw** (`pkulaw/`): Adapter for 北大法宝
- **Wenshu** (`wenshu/`): Adapter for 中国裁判文书网
- Extensible pattern for adding new legal databases

### Content Extraction System

- **ContentExtractor** (`src/contentScript/extractors/contentExtractor.ts`): Main extraction logic
- **MarkdownConverter** (`src/utils/MarkdownConverter.ts`): HTML to Markdown conversion
- **TextUtils** (`src/utils/TextUtils.ts`): Text processing utilities
- **UrlUtils** (`src/utils/UrlUtils.ts`): URL processing and truncation

## Key Workflows

1. **Page Detection**: Content script detects compatible legal database sites
2. **Button Injection**: Injects "采集到Quote" button with dropdown menu and project selector
3. **Content Extraction**: Extracts and formats content from legal document pages
4. **Authentication**: Integrates with Quote unified auth system
5. **Upload**: Sends extracted content to Quote API with metadata

## Authentication Integration

- Uses Quote unified authentication system
- Auth status monitoring across tabs via background script
- Login state synchronization between extension components
- Background script handles login tab creation and monitoring

## Technology Stack

- **Framework**: React 18 + TypeScript
- **Build**: Webpack 5 with custom environment variable injection
- **Styling**: Tailwind CSS (prefer utility classes)
- **Testing**: Jest with React Testing Library and jsdom environment
- **APIs**: Chrome Extension APIs (storage, tabs, sidePanel)

## Code Conventions

- Use TypeScript strict mode
- Follow React 18 patterns with function components and hooks
- Environment variables must use `NEXT_PUBLIC_` prefix for client-side access
- Organize components by feature in `src/components/[feature-name]/`
- Site adapters follow abstract base class pattern in `src/contentScript/sites/`
- Use Webpack path aliases for React modules to prevent hook conflicts

## Extension Manifest

Manifest v3 with key permissions:

- `storage` for configuration and state
- `tabs` for tab management and communication
- `sidePanel` for file processing UI
- `downloads` for file handling
- `<all_urls>` host permissions for legal database access

## Testing

- Jest configuration with jsdom environment
- Test coverage thresholds for core utilities
- Global test environment variables configured
- Module mocking for CSS and file assets
- Separate test suites for API, components, and integration tests

## Important Notes

- All API endpoints configured via environment variables in `src/config/api.ts`
- React components use strict aliasing to prevent hook conflicts
- Webpack DefinePlugin replaces environment variables at build time for security
- Background script handles cross-tab communication and authentication state
- Content extraction supports HTML to Markdown conversion with formatting preservation

# important-instruction-reminders
Do what has been asked; nothing more, nothing less.
NEVER create files unless they're absolutely necessary for achieving your goal.
ALWAYS prefer editing an existing file to creating a new one.
NEVER proactively create documentation files (*.md) or README files. Only create documentation files if explicitly requested by the User.

      
      IMPORTANT: this context may or may not be relevant to your tasks. You may tend to ignore it unless your task is to update the CLAUDE.md file.