<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>多层独立高亮测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
            font-size: 16px;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .step-list {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .expected-result {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-weight: bold;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 多层独立高亮测试</h1>
        
        <div class="success-indicator">
            <strong>🎉 新功能上线！</strong> 现在支持独立多层高亮，重叠时不影响现有高亮，删除时也保持其他高亮完整。
        </div>

        <div class="feature-highlight">
            <h3>🔧 核心特性</h3>
            <ol>
                <li><strong>独立采集</strong>：每次选择都是独立的高亮，不删除或替换现有高亮</li>
                <li><strong>多层共存</strong>：重叠区域可以有多个独立高亮层</li>
                <li><strong>独立删除</strong>：删除任一高亮不影响其他高亮的完整性</li>
                <li><strong>视觉区分</strong>：重叠区域使用不同颜色和边框标识</li>
                <li><strong>层级管理</strong>：通过透明度和z-index管理视觉层级</li>
            </ol>
            
            <div class="code-example">
                <strong>技术实现：</strong><br>
                • 重叠检测：分析新选择与现有高亮的重叠情况<br>
                • 分段创建：将重叠区域分为独立片段<br>
                • 层级样式：重叠区域橙色系，独立区域黄色系<br>
                • 安全删除：只移除指定层，保留其他层完整
            </div>
        </div>

        <h2>🧪 测试场景</h2>
        
        <div class="test-case">
            <h3>场景1: 完全重合测试</h3>
            <div class="test-text">
                这是一段用于测试完全重合高亮的文本内容，请选择相同的文字进行多次高亮。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>选择"测试完全重合高亮"并创建第一个高亮</li>
                    <li>再次选择相同的"测试完全重合高亮"文字</li>
                    <li>创建第二个高亮</li>
                    <li>观察重叠区域的视觉效果</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：重叠区域显示橙色背景和虚线边框，表示多层高亮
            </div>
        </div>

        <div class="test-case">
            <h3>场景2: 部分重叠测试</h3>
            <div class="test-text">
                这段文本包含多个词语，用于测试部分重叠的高亮效果，观察重叠区域和独立区域的不同表现。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>选择"多个词语"并创建高亮</li>
                    <li>选择"词语，用于测试"并创建第二个高亮</li>
                    <li>观察"词语"重叠部分和其他独立部分</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：重叠部分橙色，独立部分黄色，边界清晰
            </div>
        </div>

        <div class="test-case">
            <h3>场景3: 包含关系测试</h3>
            <div class="test-text">
                这是一个包含关系测试的示例文本，外层高亮包含内层高亮的情况。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>选择"包含关系测试的示例"创建外层高亮</li>
                    <li>选择"关系测试"创建内层高亮</li>
                    <li>观察包含关系的视觉效果</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：内层区域显示为重叠样式，外层区域保持独立样式
            </div>
        </div>

        <div class="test-case">
            <h3>场景4: 独立删除测试</h3>
            <div class="test-text">
                这段文本用于测试独立删除功能，删除其中一个高亮不应该影响其他高亮的完整性。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>选择"独立删除功能"创建第一个高亮</li>
                    <li>选择"删除功能，删除"创建第二个高亮（有重叠）</li>
                    <li>hover到重叠区域，删除其中一个高亮</li>
                    <li>观察剩余高亮是否完整</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：删除一个高亮后，另一个高亮保持完整，重叠区域恢复为单层样式
            </div>
        </div>

        <div class="test-case">
            <h3>场景5: 跨元素重叠测试</h3>
            <div class="test-text">
                这段文本包含<strong>粗体文字</strong>和<em>斜体文字</em>，测试跨HTML元素的重叠高亮效果。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>选择"粗体文字"创建高亮</li>
                    <li>选择"文字和斜体"创建跨元素高亮</li>
                    <li>观察跨元素重叠的处理效果</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：跨元素的重叠高亮正常工作，保持HTML结构完整
            </div>
        </div>

        <div class="test-case">
            <h3>场景6: 多层hover测试</h3>
            <div class="test-text">
                这段文本用于测试多层高亮的hover交互，观察工具栏的显示和层级指示。
            </div>
            <div class="step-list">
                <strong>测试步骤：</strong>
                <ol>
                    <li>创建多个重叠的高亮</li>
                    <li>hover到重叠区域</li>
                    <li>观察工具栏是否显示层级信息</li>
                    <li>测试删除功能是否正常</li>
                </ol>
            </div>
            <div class="expected-result">
                ✅ 预期结果：hover时显示最顶层高亮的工具栏，可能显示层级指示器
            </div>
        </div>

        <h2>🔍 调试信息</h2>
        <div class="feature-highlight">
            <p>打开浏览器控制台可以看到详细的多层高亮日志：</p>
            <ul>
                <li><code>[LayeredHighlight] 创建独立高亮，不影响现有高亮</code></li>
                <li><code>[LayeredHighlight] 重叠分析完成</code></li>
                <li><code>[LayeredHighlight] 创建分段元素</code></li>
                <li><code>[IndependentRemover] 删除独立高亮，不影响其他高亮</code></li>
                <li><code>[IndependentRemover] 移除层级</code></li>
            </ul>
        </div>
    </div>

    <script>
        console.log('多层独立高亮测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，多层独立高亮功能已生效');
                
                // 检查新组件
                const textHighlighter = highlightSystem.textHighlighter;
                if (textHighlighter && textHighlighter.layeredManager) {
                    console.log('✅ LayeredHighlightManager已初始化');
                }
                
                const independentRemover = highlightSystem.independentRemover;
                if (independentRemover) {
                    console.log('✅ IndependentRemover已初始化');
                }
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);

        // 监听高亮创建事件
        document.addEventListener('mouseup', () => {
            setTimeout(() => {
                const highlights = document.querySelectorAll('.quote-highlight');
                const layeredHighlights = document.querySelectorAll('.quote-highlight-layered');
                
                if (highlights.length > 0) {
                    console.log('📊 当前高亮统计:', {
                        总高亮数: highlights.length,
                        重叠高亮数: layeredHighlights.length,
                        独立高亮数: highlights.length - layeredHighlights.length
                    });
                }
            }, 100);
        });

        // 监听hover事件
        document.addEventListener('mouseover', (e) => {
            const target = e.target;
            if (target.classList && target.classList.contains('quote-highlight')) {
                const highlightId = target.getAttribute('data-highlight-id');
                const layer = target.getAttribute('data-layer');
                const existingLayers = target.getAttribute('data-existing-layers');
                
                console.log('🎯 Hover高亮信息:', {
                    highlightId,
                    layer: parseInt(layer) || 1,
                    isLayered: target.classList.contains('quote-highlight-layered'),
                    existingLayers: existingLayers ? existingLayers.split(',') : [],
                    text: target.textContent.substring(0, 20) + '...'
                });
            }
        });
    </script>
</body>
</html>
