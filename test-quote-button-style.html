<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quote按钮样式测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .button-demo {
            display: flex;
            gap: 8px;
            align-items: center;
            margin: 15px 0;
            padding: 15px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        .demo-button {
            background: none;
            border: none;
            width: 32px;
            height: 32px;
            border-radius: 4px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.2s ease;
            color: inherit;
            padding: 0;
        }
        .demo-button:hover {
            background-color: #f5f5f5;
        }
        .demo-divider {
            width: 1px;
            height: 20px;
            background-color: #e0e0e0;
            margin: 0 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔘 Quote按钮样式测试</h1>
        
        <div class="success-indicator">
            <strong>✅ Quote图标已改为按钮形式！</strong> 现在Quote图标是一个与左侧高亮按钮相同样式的按钮。
        </div>

        <div class="feature-highlight">
            <h3>🔧 更新内容</h3>
            <ol>
                <li><strong>按钮形式</strong>：Quote图标现在是一个真正的button元素</li>
                <li><strong>统一样式</strong>：与左侧高亮按钮使用相同的CSS样式</li>
                <li><strong>相同大小</strong>：两个按钮都是32px × 32px</li>
                <li><strong>一致交互</strong>：相同的hover效果和过渡动画</li>
                <li><strong>保留分割线</strong>：按钮之间仍有灰色分割线</li>
            </ol>
        </div>

        <h2>🎯 按钮样式预览</h2>
        
        <div class="test-case">
            <h3>工具栏按钮对比</h3>
            
            <div class="button-demo">
                <button class="demo-button" title="高亮采集">
                    <svg id="_图层_1" data-name="图层 1" xmlns="http://www.w3.org/2000/svg" version="1.1" viewBox="0 0 24 24" width="16" height="16">
                        <defs>
                            <style>
                                .cls-1{stroke:#000;}.cls-1,.cls-2{fill:none;stroke-linecap:round;stroke-linejoin:round;stroke-width:2px;}.cls-2{stroke:#ffe000;}
                            </style>
                        </defs>
                        <path class="cls-2" d="M13,21h8"/>
                        <path class="cls-1" d="M15,5l4,4"/>
                        <path class="cls-1" d="M21.2,6.8c1.1-1.1,1.1-2.9,0-4-1.1-1.1-2.9-1.1-4,0L3.8,16.2c-.2.2-.4.5-.5.8l-1.3,4.4c0,.3,0,.5.3.6,0,0,.2,0,.3,0l4.4-1.3c.3,0,.6-.3.8-.5l13.3-13.3Z"/>
                    </svg>
                </button>
                
                <div class="demo-divider"></div>
                
                <button class="demo-button" title="Quote">
                    <svg width="14" height="17" viewBox="0 0 14 17">
                        <g>
                            <path d="M11.5035,13.6008C9.06035,13.6008,7.07856,11.6981,7.07856,9.35029L14,9.35009L14,0L0,0L0,10.3226C0,14.0108,3.11192,17,6.95152,17L11.9467,17L11.9467,13.5998L11.5046,13.5998L11.5035,13.6008Z" fill="#4034BA" fill-opacity="1" style="mix-blend-mode:passthrough"/>
                        </g>
                    </svg>
                </button>
            </div>
            
            <p><strong>样式特点</strong>：</p>
            <ul>
                <li>两个按钮大小完全一致（32px × 32px）</li>
                <li>相同的圆角边框（4px border-radius）</li>
                <li>统一的hover背景色（#f5f5f5）</li>
                <li>相同的过渡动画效果</li>
                <li>灰色分割线保持视觉分离</li>
            </ul>
        </div>

        <h2>🧪 功能测试</h2>
        
        <div class="test-case">
            <h3>测试1: 按钮大小一致性</h3>
            <div class="test-text">
                请选择这段文字创建高亮，观察工具栏中两个按钮的大小是否完全一致。
            </div>
            <p><strong>预期结果</strong>：✅ 左侧高亮按钮和右侧Quote按钮大小完全相同</p>
        </div>

        <div class="test-case">
            <h3>测试2: hover效果一致性</h3>
            <div class="test-text">
                hover到工具栏的两个按钮上，观察hover效果是否一致。
            </div>
            <p><strong>预期结果</strong>：✅ 两个按钮都有相同的背景色变化效果</p>
        </div>

        <div class="test-case">
            <h3>测试3: 视觉对齐</h3>
            <div class="test-text">
                观察工具栏中两个按钮的对齐情况，以及分割线的位置。
            </div>
            <p><strong>预期结果</strong>：</p>
            <ul>
                <li>✅ 两个按钮垂直居中对齐</li>
                <li>✅ 分割线位于两个按钮中间</li>
                <li>✅ 整体布局平衡美观</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>测试4: 交互体验</h3>
            <div class="test-text">
                测试两个按钮的点击和hover交互体验。
            </div>
            <p><strong>预期结果</strong>：</p>
            <ul>
                <li>✅ 左侧按钮可以正常执行高亮/取消高亮功能</li>
                <li>✅ 右侧Quote按钮有hover效果但暂无点击功能</li>
                <li>✅ 两个按钮的交互反馈一致</li>
            </ul>
        </div>

        <h2>🎨 技术实现</h2>
        
        <div class="feature-highlight">
            <h3>CSS样式统一</h3>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                .quote-selection-toolbar button,<br>
                .quote-selection-toolbar .quote-button {<br>
                &nbsp;&nbsp;width: 32px !important;<br>
                &nbsp;&nbsp;height: 32px !important;<br>
                &nbsp;&nbsp;border-radius: 4px !important;<br>
                &nbsp;&nbsp;background: none !important;<br>
                &nbsp;&nbsp;border: none !important;<br>
                &nbsp;&nbsp;/* 其他样式... */<br>
                }
            </div>
            
            <h3>DOM结构</h3>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-family: monospace; font-size: 12px;">
                &lt;div class="quote-selection-toolbar"&gt;<br>
                &nbsp;&nbsp;&lt;button&gt;/* 高亮按钮 */&lt;/button&gt;<br>
                &nbsp;&nbsp;&lt;div class="divider"&gt;&lt;/div&gt;<br>
                &nbsp;&nbsp;&lt;button class="quote-button"&gt;/* Quote按钮 */&lt;/button&gt;<br>
                &lt;/div&gt;
            </div>
        </div>
    </div>

    <script>
        console.log('Quote按钮样式测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，Quote按钮样式已更新');
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);

        // 模拟按钮hover效果
        document.addEventListener('DOMContentLoaded', () => {
            const demoButtons = document.querySelectorAll('.demo-button');
            demoButtons.forEach(button => {
                button.addEventListener('click', () => {
                    console.log('演示按钮被点击:', button.title);
                });
            });
        });
    </script>
</body>
</html>
