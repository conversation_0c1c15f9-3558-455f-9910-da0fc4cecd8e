/**
 * AuthService API 测试
 */

import { AuthService, AuthStatus } from '../../src/contentScript/services/AuthService';

// 模拟 LoginPrompt 组件
jest.mock('../../src/contentScript/components/LoginPrompt', () => ({
  createLoginPrompt: jest.fn(() => ({
    show: jest.fn(),
  })),
}));

describe('AuthService API Tests', () => {
  beforeEach(() => {
    // 重置所有模拟
    jest.clearAllMocks();
    AuthService.reset();
  });

  describe('checkAuthenticationStatus', () => {
    it('应该返回认证成功状态', async () => {
      // 模拟成功的认证响应
      const mockResponse = {
        success: true,
        isAuthenticated: true,
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.checkAuthenticationStatus();

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'CHECK_AUTH_STATUS',
      });
      expect(result).toEqual({
        isAuthenticated: true,
        error: undefined,
      });
    });

    it('应该返回认证失败状态', async () => {
      // 模拟失败的认证响应
      const mockResponse = {
        success: true,
        isAuthenticated: false,
        error: '用户未登录',
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.checkAuthenticationStatus();

      expect(result).toEqual({
        isAuthenticated: false,
        error: '用户未登录',
      });
    });

    it('应该处理API调用失败的情况', async () => {
      // 模拟API调用失败
      const mockResponse = {
        success: false,
        error: 'API调用失败',
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

      const result = await AuthService.checkAuthenticationStatus();

      expect(result).toEqual({
        isAuthenticated: false,
        error: 'API调用失败',
      });
    });

    it('应该处理网络错误', async () => {
      // 模拟网络错误
      const networkError = new Error('网络连接失败');
      (chrome.runtime.sendMessage as jest.Mock).mockRejectedValue(networkError);

      const result = await AuthService.checkAuthenticationStatus();

      expect(result).toEqual({
        isAuthenticated: false,
        error: '网络连接失败',
      });
    });

    it('应该处理空响应', async () => {
      // 模拟空响应
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(null);

      const result = await AuthService.checkAuthenticationStatus();

      expect(result).toEqual({
        isAuthenticated: false,
        error: '认证检查失败',
      });
    });
  });

  describe('handleLogin', () => {
    it('应该成功打开登录标签页', async () => {
      // 模拟成功的登录响应
      const mockResponse = {
        success: true,
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

      await AuthService.handleLogin();

      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'OPEN_LOGIN_TAB',
        data: {
          loginUrl: 'https://test-redirect.example.com/login',
        },
      });
    });

    it('应该处理打开登录标签页失败的情况', async () => {
      // 模拟失败的响应
      const mockResponse = {
        success: false,
        error: '无法打开标签页',
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockResponse);

      // 应该不抛出错误，只记录日志
      await expect(AuthService.handleLogin()).resolves.toBeUndefined();
    });

    it('应该处理网络错误', async () => {
      // 模拟网络错误
      const networkError = new Error('网络连接失败');
      (chrome.runtime.sendMessage as jest.Mock).mockRejectedValue(networkError);

      // 应该不抛出错误，只记录日志
      await expect(AuthService.handleLogin()).resolves.toBeUndefined();
    });
  });

  describe('showLoginPrompt', () => {
    it('应该防止重复显示登录提示', async () => {
      const { createLoginPrompt } = require('../../src/contentScript/components/LoginPrompt');
      const mockLoginPrompt = {
        show: jest.fn().mockResolvedValue('login'),
      };
      createLoginPrompt.mockReturnValue(mockLoginPrompt);

      // 第一次调用
      const promise1 = AuthService.showLoginPrompt();
      
      // 第二次调用（应该被阻止）
      const promise2 = AuthService.showLoginPrompt();

      const [result1, result2] = await Promise.all([promise1, promise2]);

      expect(result1).toBe('login');
      expect(result2).toBe('cancel'); // 第二次调用应该返回 cancel
      expect(createLoginPrompt).toHaveBeenCalledTimes(1); // 只创建一次
    });
  });

  describe('reset', () => {
    it('应该重置内部状态', () => {
      // 这个测试主要验证 reset 方法不会抛出错误
      expect(() => AuthService.reset()).not.toThrow();
    });
  });
});
