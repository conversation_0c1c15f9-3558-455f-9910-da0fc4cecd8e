/**
 * Jest 测试环境设置
 */

// 扩展 Jest 匹配器
import '@testing-library/jest-dom';

// 模拟 Chrome Extension APIs
const mockChrome = {
  runtime: {
    sendMessage: jest.fn(),
    onMessage: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
    getURL: jest.fn((path: string) => `chrome-extension://test-id/${path}`),
    id: 'test-extension-id',
  },
  tabs: {
    onUpdated: {
      addListener: jest.fn(),
      removeListener: jest.fn(),
    },
    create: jest.fn(),
    query: jest.fn(),
    update: jest.fn(),
  },
  storage: {
    local: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
    },
    sync: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn(),
      clear: jest.fn(),
    },
  },
  action: {
    setBadgeText: jest.fn(),
    setBadgeBackgroundColor: jest.fn(),
  },
};

// 全局设置 chrome 对象
(global as any).chrome = mockChrome;

// 模拟 DOM APIs
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// 模拟 ResizeObserver
global.ResizeObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 模拟 IntersectionObserver
global.IntersectionObserver = jest.fn().mockImplementation(() => ({
  observe: jest.fn(),
  unobserve: jest.fn(),
  disconnect: jest.fn(),
}));

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL = 'https://test-auth.example.com';
process.env.NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL = 'https://test-redirect.example.com';
process.env.API_BASE_URL = 'https://test-api.example.com';
process.env.NEXT_PUBLIC_DEFAULT_PROJECT_ID = 'test-project-id';

// 全局测试工具函数
(global as any).createMockResponse = (data: any, success: boolean = true) => ({
  success,
  ...data,
});

// 清理函数
afterEach(() => {
  jest.clearAllMocks();
});

// 控制台警告过滤
const originalWarn = console.warn;
console.warn = (...args: any[]) => {
  // 过滤掉一些已知的测试警告
  const message = args[0];
  if (typeof message === 'string') {
    if (message.includes('Warning: ReactDOM.render is deprecated')) return;
    if (message.includes('Warning: componentWillReceiveProps has been renamed')) return;
  }
  originalWarn.apply(console, args);
};
