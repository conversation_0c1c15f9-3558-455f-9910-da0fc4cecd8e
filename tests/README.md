# Jest 测试文档

## 概述

本项目使用 Jest 进行 API 服务测试，专注于核心功能的质量保证。测试遵循"不为了通过测试而强行修改测试规则"的原则，确保测试的真实性和有效性。

## 测试结构

```
tests/
├── __mocks__/           # 模拟文件
│   └── fileMock.js      # 静态资源模拟
├── api/                 # API 服务测试
│   └── auth.test.ts     # 认证服务测试
├── components/          # 组件测试
│   └── TextUtils.test.ts # 文本工具类测试
├── integration/         # 集成测试
│   └── quote-collection.test.ts # Quote采集集成测试
├── setup.ts            # 测试环境设置
└── README.md           # 本文档
```

## 测试命令

```bash
# 运行所有测试
npm test

# 运行特定类型的测试
npm run test:api          # API 服务测试
npm run test:components   # 组件测试
npm run test:integration  # 集成测试

# 监听模式
npm run test:watch

# 覆盖率测试
npm run test:coverage
```

## 测试覆盖的功能

### 1. API 服务测试 (`tests/api/auth.test.ts`)

**测试范围**:
- `AuthService.checkAuthenticationStatus()` - 认证状态检查
- `AuthService.handleLogin()` - 登录处理
- `AuthService.showLoginPrompt()` - 登录提示显示
- `AuthService.reset()` - 状态重置

**测试场景**:
- ✅ 认证成功/失败状态
- ✅ API调用失败处理
- ✅ 网络错误处理
- ✅ 空响应处理
- ✅ 重复登录提示防护

### 2. 工具类测试 (`tests/components/TextUtils.test.ts`)

**测试范围**:
- `TextUtils.isChineseChar()` - 中文字符识别
- `TextUtils.getVisualWidth()` - 视觉宽度计算
- `TextUtils.truncateToVisualWidth()` - 视觉宽度截取
- `TextUtils.applyMiddleEllipsis()` - 中间省略逻辑

**测试场景**:
- ✅ 中文字符识别（汉字、标点、全角字符）
- ✅ 英文字符识别
- ✅ 中英文混合文本处理
- ✅ 视觉宽度精确计算
- ✅ 智能截取算法
- ✅ 边界情况处理

### 3. 集成测试 (`tests/integration/quote-collection.test.ts`)

**测试范围**:
- 完整的认证流程
- 项目数据获取
- 内容采集功能
- 错误处理机制

**测试场景**:
- ✅ 认证检查 → 登录 → 采集的完整流程
- ✅ 项目列表获取成功/失败
- ✅ 采集到指定项目
- ✅ 采集到收件箱
- ✅ 网络错误和服务不可用处理

## 测试环境配置

### Chrome Extension APIs 模拟

```typescript
// 模拟 chrome.runtime
chrome.runtime.sendMessage = jest.fn()
chrome.runtime.onMessage = { addListener: jest.fn(), removeListener: jest.fn() }

// 模拟 chrome.storage
chrome.storage.local = { get: jest.fn(), set: jest.fn(), remove: jest.fn() }

// 模拟 chrome.tabs
chrome.tabs.create = jest.fn()
chrome.tabs.query = jest.fn()
```

### 环境变量

```typescript
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_AUTH_SERVICE_API_URL = 'https://test-auth.example.com'
process.env.API_BASE_URL = 'https://test-api.example.com'
```

## 覆盖率标准

### 核心工具类 (`TextUtils.ts`)
- **分支覆盖率**: ≥90%
- **函数覆盖率**: 100%
- **行覆盖率**: ≥95%
- **语句覆盖率**: ≥95%

### API 服务 (`AuthService.ts`)
- **分支覆盖率**: ≥50%
- **函数覆盖率**: ≥55%
- **行覆盖率**: ≥60%
- **语句覆盖率**: ≥60%

## 测试原则

### 1. 真实性优先
- 测试基于实际的API响应格式
- 不为了通过测试而修改业务逻辑
- 测试用例反映真实的使用场景

### 2. 边界情况覆盖
- 网络错误处理
- 空数据处理
- 异常输入处理
- 并发操作处理

### 3. 模拟的合理性
- 只模拟外部依赖（Chrome APIs、网络请求）
- 不模拟被测试的核心逻辑
- 模拟数据符合实际API规范

## 运行结果示例

```
Test Suites: 3 passed, 3 total
Tests:       41 passed, 41 total
Snapshots:   0 total
Time:        0.495 s

Coverage Summary:
- TextUtils.ts: 100% functions, 90.32% branches, 100% lines
- AuthService.ts: 57.14% functions, 覆盖核心认证逻辑
```

## 持续改进

### 待扩展的测试
1. **UI组件测试**: QuoteButton、ProjectSelector 等
2. **Background脚本测试**: 消息处理、存储管理
3. **端到端测试**: 完整的用户操作流程

### 测试质量提升
1. **增加边界测试**: 更多异常情况覆盖
2. **性能测试**: 大量数据处理的性能验证
3. **兼容性测试**: 不同浏览器环境的兼容性

## 注意事项

1. **不修改测试规则**: 当测试失败时，优先检查代码逻辑而不是放宽测试标准
2. **保持测试独立**: 每个测试用例都应该能够独立运行
3. **及时更新**: 当API接口变更时，同步更新测试用例
4. **文档同步**: 测试用例的变更应该同步更新本文档
