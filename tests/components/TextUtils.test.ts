/**
 * TextUtils 工具类测试
 */

import { TextUtils } from "../../src/utils/TextUtils";

describe("TextUtils", () => {
  describe("isChineseChar", () => {
    it("应该正确识别中文字符", () => {
      expect(TextUtils.isChineseChar("中")).toBe(true);
      expect(TextUtils.isChineseChar("文")).toBe(true);
      expect(TextUtils.isChineseChar("测")).toBe(true);
      expect(TextUtils.isChineseChar("试")).toBe(true);
    });

    it("应该正确识别中文标点符号", () => {
      expect(TextUtils.isChineseChar("，")).toBe(true);
      expect(TextUtils.isChineseChar("。")).toBe(true);
      expect(TextUtils.isChineseChar("？")).toBe(true);
      expect(TextUtils.isChineseChar("！")).toBe(true);
    });

    it("应该正确识别全角字符", () => {
      expect(TextUtils.isChineseChar("１")).toBe(true);
      expect(TextUtils.isChineseChar("Ａ")).toBe(true);
      expect(TextUtils.isChineseChar("（")).toBe(true);
      expect(TextUtils.isChineseChar("）")).toBe(true);
    });

    it("应该正确识别英文字符", () => {
      expect(TextUtils.isChineseChar("a")).toBe(false);
      expect(TextUtils.isChineseChar("Z")).toBe(false);
      expect(TextUtils.isChineseChar("1")).toBe(false);
      expect(TextUtils.isChineseChar("9")).toBe(false);
    });

    it("应该正确识别英文标点符号", () => {
      expect(TextUtils.isChineseChar(",")).toBe(false);
      expect(TextUtils.isChineseChar(".")).toBe(false);
      expect(TextUtils.isChineseChar("?")).toBe(false);
      expect(TextUtils.isChineseChar("!")).toBe(false);
    });
  });

  describe("getVisualWidth", () => {
    it("应该正确计算纯英文字符串的视觉宽度", () => {
      expect(TextUtils.getVisualWidth("hello", 1.8)).toBe(5);
      expect(TextUtils.getVisualWidth("test123", 1.8)).toBe(7);
    });

    it("应该正确计算纯中文字符串的视觉宽度", () => {
      expect(TextUtils.getVisualWidth("测试", 1.8)).toBe(3.6);
      expect(TextUtils.getVisualWidth("中文测试", 1.8)).toBe(7.2);
    });

    it("应该正确计算中英文混合字符串的视觉宽度", () => {
      expect(TextUtils.getVisualWidth("test测试", 1.8)).toBe(7.6); // 4 + 3.6
      expect(TextUtils.getVisualWidth("项目Project", 1.8)).toBe(10.6); // 3.6 + 7
    });

    it("应该支持自定义中文字符宽度比例", () => {
      expect(TextUtils.getVisualWidth("测试", 2.0)).toBe(4.0);
      expect(TextUtils.getVisualWidth("测试", 1.5)).toBe(3.0);
    });

    it("应该处理空字符串", () => {
      expect(TextUtils.getVisualWidth("", 1.8)).toBe(0);
    });
  });

  describe("truncateToVisualWidth", () => {
    it("应该从开头截取指定视觉宽度的字符串", () => {
      expect(TextUtils.truncateToVisualWidth("hello world", 5, true, 1.8)).toBe(
        "hello"
      );
      expect(TextUtils.truncateToVisualWidth("测试文本", 3.6, true, 1.8)).toBe(
        "测试"
      );
    });

    it("应该从结尾截取指定视觉宽度的字符串", () => {
      expect(
        TextUtils.truncateToVisualWidth("hello world", 5, false, 1.8)
      ).toBe("world");
      expect(TextUtils.truncateToVisualWidth("测试文本", 3.6, false, 1.8)).toBe(
        "文本"
      );
    });

    it("应该处理中英文混合字符串", () => {
      // test(4) + 测(1.8) = 5.8 > 5，所以只能取到test
      expect(TextUtils.truncateToVisualWidth("test测试", 5, true, 1.8)).toBe(
        "test"
      );
      // 从结尾截取5个单位：试(1.8) + 测(1.8) = 3.6 < 5，可以继续取t(1) = 4.6 < 5
      expect(TextUtils.truncateToVisualWidth("test测试", 5, false, 1.8)).toBe(
        "t测试"
      );
    });

    it("应该处理超出长度的情况", () => {
      expect(TextUtils.truncateToVisualWidth("test", 10, true, 1.8)).toBe(
        "test"
      );
      expect(TextUtils.truncateToVisualWidth("测试", 10, true, 1.8)).toBe(
        "测试"
      );
    });

    it("应该处理空字符串", () => {
      expect(TextUtils.truncateToVisualWidth("", 5, true, 1.8)).toBe("");
    });
  });

  describe("applyMiddleEllipsis", () => {
    it("应该对短文本不进行省略", () => {
      expect(TextUtils.applyMiddleEllipsis("短文本", 20, 12, 5, 1.8)).toBe(
        "短文本"
      );
      expect(TextUtils.applyMiddleEllipsis("short", 20, 12, 5, 1.8)).toBe(
        "short"
      );
    });

    it("应该对长文本进行中间省略", () => {
      const longText = "这是一个非常长的项目名称用于测试中间省略功能";
      const result = TextUtils.applyMiddleEllipsis(longText, 20, 12, 5, 1.8);

      expect(result).toContain("...");
      expect(result.length).toBeLessThan(longText.length);
      // 验证结果包含开头和结尾部分
      expect(result.indexOf("这是一个")).toBe(0); // 开头部分
      expect(result.includes("功能")).toBe(true); // 结尾部分
    });

    it("应该处理英文长文本", () => {
      const longText =
        "This is a very long project name for testing middle ellipsis functionality";
      const result = TextUtils.applyMiddleEllipsis(longText, 20, 12, 5, 1.8);

      expect(result).toContain("...");
      expect(result.length).toBeLessThan(longText.length);
    });

    it("应该处理中英文混合长文本", () => {
      const longText = "Project项目名称测试中间省略功能Test";
      const result = TextUtils.applyMiddleEllipsis(longText, 20, 12, 5, 1.8);

      expect(result).toContain("...");
      expect(result.length).toBeLessThan(longText.length);
    });

    it("应该处理空字符串", () => {
      expect(TextUtils.applyMiddleEllipsis("", 20, 12, 5, 1.8)).toBe("");
    });

    it("应该在可用宽度不足时按比例分配", () => {
      const longText = "测试文本省略功能";
      const result = TextUtils.applyMiddleEllipsis(longText, 8, 6, 4, 1.8);

      expect(result).toContain("...");
      // 当 startChars + endChars > availableWidth 时，应该按比例分配
    });

    it("应该使用默认参数", () => {
      const longText = "这是一个用于测试默认参数的非常长的项目名称";
      const result = TextUtils.applyMiddleEllipsis(longText);

      expect(result).toContain("...");
      expect(result.length).toBeLessThan(longText.length);
    });
  });
});
