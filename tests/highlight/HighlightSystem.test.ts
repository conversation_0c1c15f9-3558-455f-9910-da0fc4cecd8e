/**
 * HighlightSystem 核心管理器测试
 */

import { HighlightSystem } from "../../src/contentScript/highlight/HighlightSystem";

// Mock Chrome API
const mockChrome = {
  runtime: {
    getURL: jest.fn((path: string) => `chrome-extension://test-id/${path}`),
  },
};

// 设置全局 chrome 对象
(global as any).chrome = mockChrome;

// Mock DOM APIs
Object.defineProperty(window, "getSelection", {
  writable: true,
  value: jest.fn(),
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
Object.defineProperty(window, "localStorage", {
  value: localStorageMock,
});

// Mock React DOM
jest.mock("react-dom/client", () => ({
  createRoot: jest.fn(() => ({
    render: jest.fn(),
    unmount: jest.fn(),
  })),
}));

describe("HighlightSystem", () => {
  let highlightSystem: HighlightSystem;
  let mockSelection: any;
  let mockRange: any;

  beforeEach(() => {
    // 清理 DOM
    document.body.innerHTML = "";

    // 重置所有 mock
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);

    // 创建 mock selection 和 range
    mockRange = {
      getBoundingClientRect: jest.fn(() => ({
        left: 100,
        top: 100,
        right: 200,
        bottom: 120,
        width: 100,
        height: 20,
      })),
      surroundContents: jest.fn(),
    };

    mockSelection = {
      isCollapsed: false,
      toString: jest.fn(() => "selected text"),
      getRangeAt: jest.fn(() => mockRange),
      removeAllRanges: jest.fn(),
    };

    (window.getSelection as jest.Mock).mockReturnValue(mockSelection);

    // 创建系统实例
    highlightSystem = new HighlightSystem();
  });

  afterEach(() => {
    if (highlightSystem) {
      highlightSystem.destroy();
    }
  });

  describe("初始化", () => {
    test("应该创建必要的DOM容器", () => {
      const toolbarContainer = document.getElementById(
        "quote-selection-toolbar-container"
      );
      const notificationContainer = document.getElementById(
        "quote-notification-container"
      );

      expect(toolbarContainer).toBeTruthy();
      expect(notificationContainer).toBeTruthy();
    });

    test("应该注入样式链接", () => {
      const styleLink = document.getElementById("quote-highlight-styles");
      expect(styleLink).toBeTruthy();
      expect(styleLink?.getAttribute("href")).toBe(
        "chrome-extension://test-id/contentScript/highlight/styles/highlight.css"
      );
    });

    test("应该绑定事件监听器", () => {
      const addEventListenerSpy = jest.spyOn(document, "addEventListener");

      // 创建新实例来测试事件绑定
      const newSystem = new HighlightSystem();

      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "mouseup",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "keyup",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "scroll",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "click",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "mouseover",
        expect.any(Function)
      );
      expect(addEventListenerSpy).toHaveBeenCalledWith(
        "mouseout",
        expect.any(Function)
      );

      newSystem.destroy();
      addEventListenerSpy.mockRestore();
    });
  });

  describe("文本选择处理", () => {
    test("应该在文本选择后显示工具栏", (done) => {
      const mouseUpEvent = new MouseEvent("mouseup", {
        clientX: 150,
        clientY: 110,
      });

      document.dispatchEvent(mouseUpEvent);

      // 使用 setTimeout 等待异步处理
      setTimeout(() => {
        expect(window.getSelection).toHaveBeenCalled();
        expect(mockSelection.toString).toHaveBeenCalled();
        expect(mockRange.getBoundingClientRect).toHaveBeenCalled();
        done();
      }, 20);
    });

    test("应该在空选择时隐藏工具栏", (done) => {
      mockSelection.isCollapsed = true;
      mockSelection.toString.mockReturnValue("");

      const mouseUpEvent = new MouseEvent("mouseup");
      document.dispatchEvent(mouseUpEvent);

      setTimeout(() => {
        expect(window.getSelection).toHaveBeenCalled();
        done();
      }, 20);
    });

    test("应该在按ESC键时隐藏工具栏", () => {
      const escapeEvent = new KeyboardEvent("keyup", { key: "Escape" });
      document.dispatchEvent(escapeEvent);

      // 工具栏应该被隐藏（通过检查状态或DOM变化）
      expect(true).toBe(true); // 基础测试，实际应检查工具栏状态
    });
  });

  describe("高亮功能", () => {
    test("应该能够创建高亮文本", () => {
      // 模拟高亮操作
      const span = document.createElement("span");
      span.className = "quote-highlight";
      span.setAttribute("data-highlight-id", "test-id");
      span.textContent = "highlighted text";

      document.body.appendChild(span);

      expect(span.classList.contains("quote-highlight")).toBe(true);
      expect(span.getAttribute("data-highlight-id")).toBe("test-id");
    });

    test("应该保存高亮数据到localStorage", () => {
      // 通过调用私有方法来测试存储功能
      (highlightSystem as any).saveToStorage();

      expect(localStorageMock.setItem).toHaveBeenCalledWith(
        "quote_highlights",
        expect.any(String)
      );
    });
  });

  describe("存储管理", () => {
    test("应该能够加载存储的高亮数据", () => {
      const mockData = JSON.stringify([
        {
          id: "test-1",
          text: "test text",
          timestamp: Date.now(),
          pageUrl: "http://test.com",
        },
      ]);

      localStorageMock.getItem.mockReturnValue(mockData);

      // 创建新实例来测试加载
      const newSystem = new HighlightSystem();

      expect(localStorageMock.getItem).toHaveBeenCalledWith("quote_highlights");

      newSystem.destroy();
    });

    test("应该处理无效的存储数据", () => {
      localStorageMock.getItem.mockReturnValue("invalid json");

      // 应该不抛出错误
      expect(() => {
        const newSystem = new HighlightSystem();
        newSystem.destroy();
      }).not.toThrow();
    });
  });

  describe("清理功能", () => {
    test("destroy方法应该清理所有资源", () => {
      const removeEventListenerSpy = jest.spyOn(
        document,
        "removeEventListener"
      );

      highlightSystem.destroy();

      // 检查容器是否被移除
      expect(
        document.getElementById("quote-selection-toolbar-container")
      ).toBeNull();
      expect(
        document.getElementById("quote-notification-container")
      ).toBeNull();

      removeEventListenerSpy.mockRestore();
    });
  });

  describe("工具栏位置计算", () => {
    test("应该正确计算工具栏位置", () => {
      const rect = {
        left: 100,
        top: 100,
        right: 200,
        bottom: 120,
        width: 100,
        height: 20,
      } as DOMRect;

      // 通过反射访问私有方法进行测试
      const position = (highlightSystem as any).calculateToolbarPosition(rect);

      expect(position.x).toBeGreaterThan(rect.right);
      expect(position.y).toBeGreaterThan(rect.bottom);
    });
  });

  describe("ID生成", () => {
    test("应该生成唯一的ID", () => {
      const id1 = (highlightSystem as any).generateId();
      const id2 = (highlightSystem as any).generateId();

      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^highlight_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^highlight_\d+_[a-z0-9]+$/);
    });
  });
});
