/**
 * SelectionToolbar 组件测试
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { SelectionToolbar } from '../../../src/contentScript/highlight/components/SelectionToolbar';

describe('SelectionToolbar', () => {
  const defaultProps = {
    visible: true,
    position: { x: 100, y: 100 },
    mode: 'add' as const,
    onHighlight: jest.fn(),
    onRemove: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('渲染行为', () => {
    test('当visible为true时应该渲染工具栏', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar');
      expect(toolbar).toBeInTheDocument();
    });

    test('当visible为false时不应该渲染工具栏', () => {
      render(<SelectionToolbar {...defaultProps} visible={false} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar');
      expect(toolbar).not.toBeInTheDocument();
    });

    test('应该根据position属性设置正确的位置', () => {
      const position = { x: 150, y: 200 };
      render(<SelectionToolbar {...defaultProps} position={position} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar') as HTMLElement;
      expect(toolbar).toHaveStyle({
        left: '150px',
        top: '200px',
      });
    });
  });

  describe('模式切换', () => {
    test('在add模式下应该显示高亮采集图标', () => {
      render(<SelectionToolbar {...defaultProps} mode="add" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('title', '高亮采集');
      
      // 检查是否包含高亮采集图标的SVG路径
      const svg = button.querySelector('svg');
      expect(svg).toBeInTheDocument();
      
      const paths = svg?.querySelectorAll('path');
      expect(paths).toHaveLength(2); // 高亮采集图标有2个path
    });

    test('在remove模式下应该显示取消高亮图标', () => {
      render(<SelectionToolbar {...defaultProps} mode="remove" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('title', '取消高亮');
      
      // 检查是否包含取消高亮图标的SVG路径
      const svg = button.querySelector('svg');
      expect(svg).toBeInTheDocument();
      
      const paths = svg?.querySelectorAll('path');
      expect(paths).toHaveLength(2); // 取消高亮图标有2个path
    });
  });

  describe('事件处理', () => {
    test('在add模式下点击按钮应该调用onHighlight', () => {
      const onHighlight = jest.fn();
      render(
        <SelectionToolbar 
          {...defaultProps} 
          mode="add" 
          onHighlight={onHighlight}
        />
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(onHighlight).toHaveBeenCalledTimes(1);
      expect(defaultProps.onRemove).not.toHaveBeenCalled();
    });

    test('在remove模式下点击按钮应该调用onRemove', () => {
      const onRemove = jest.fn();
      render(
        <SelectionToolbar 
          {...defaultProps} 
          mode="remove" 
          onRemove={onRemove}
        />
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(onRemove).toHaveBeenCalledTimes(1);
      expect(defaultProps.onHighlight).not.toHaveBeenCalled();
    });

    test('应该阻止事件冒泡', () => {
      const onHighlight = jest.fn();
      const parentClickHandler = jest.fn();
      
      const { container } = render(
        <div onClick={parentClickHandler}>
          <SelectionToolbar 
            {...defaultProps} 
            mode="add" 
            onHighlight={onHighlight}
          />
        </div>
      );
      
      const button = screen.getByRole('button');
      fireEvent.click(button);
      
      expect(onHighlight).toHaveBeenCalledTimes(1);
      // 父元素的点击事件不应该被触发（如果正确阻止了冒泡）
      // 注意：这个测试可能需要根据实际实现调整
    });
  });

  describe('Quote图标', () => {
    test('应该显示装饰性的Quote图标', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const quoteIcon = document.querySelector('.quote-icon');
      expect(quoteIcon).toBeInTheDocument();
      
      const svg = quoteIcon?.querySelector('svg');
      expect(svg).toBeInTheDocument();
      
      // Quote图标应该有特定的路径
      const paths = svg?.querySelectorAll('path');
      expect(paths).toHaveLength(2); // Quote图标有2个path
    });

    test('Quote图标不应该是可点击的', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const quoteIcon = document.querySelector('.quote-icon');
      expect(quoteIcon).not.toHaveAttribute('onClick');
      expect(quoteIcon?.tagName.toLowerCase()).toBe('div');
    });
  });

  describe('样式类名', () => {
    test('工具栏应该有正确的CSS类名', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar');
      expect(toolbar).toHaveClass('quote-selection-toolbar');
    });

    test('按钮应该在工具栏内部', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar');
      const button = screen.getByRole('button');
      
      expect(toolbar).toContainElement(button);
    });
  });

  describe('可访问性', () => {
    test('按钮应该有适当的title属性', () => {
      render(<SelectionToolbar {...defaultProps} mode="add" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('title', '高亮采集');
    });

    test('在remove模式下按钮应该有正确的title', () => {
      render(<SelectionToolbar {...defaultProps} mode="remove" />);
      
      const button = screen.getByRole('button');
      expect(button).toHaveAttribute('title', '取消高亮');
    });

    test('SVG图标应该有适当的属性', () => {
      render(<SelectionToolbar {...defaultProps} />);
      
      const svgs = document.querySelectorAll('svg');
      svgs.forEach(svg => {
        expect(svg).toHaveAttribute('width', '16');
        expect(svg).toHaveAttribute('height', '16');
        expect(svg).toHaveAttribute('viewBox');
      });
    });
  });

  describe('边界情况', () => {
    test('应该处理极端位置值', () => {
      const extremePosition = { x: -100, y: -50 };
      render(<SelectionToolbar {...defaultProps} position={extremePosition} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar') as HTMLElement;
      expect(toolbar).toHaveStyle({
        left: '-100px',
        top: '-50px',
      });
    });

    test('应该处理非常大的位置值', () => {
      const largePosition = { x: 9999, y: 9999 };
      render(<SelectionToolbar {...defaultProps} position={largePosition} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar') as HTMLElement;
      expect(toolbar).toHaveStyle({
        left: '9999px',
        top: '9999px',
      });
    });

    test('应该处理零位置值', () => {
      const zeroPosition = { x: 0, y: 0 };
      render(<SelectionToolbar {...defaultProps} position={zeroPosition} />);
      
      const toolbar = document.querySelector('.quote-selection-toolbar') as HTMLElement;
      expect(toolbar).toHaveStyle({
        left: '0px',
        top: '0px',
      });
    });
  });
});
