/**
 * NotificationBar 组件测试
 */

import React from "react";
import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import "@testing-library/jest-dom";
import { NotificationBar } from "../../../src/contentScript/highlight/components/NotificationBar";

describe("NotificationBar", () => {
  const defaultProps = {
    visible: true,
    count: 5,
    onClear: jest.fn(),
    onSelectProject: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("渲染行为", () => {
    test("当visible为true时应该渲染通知栏", () => {
      render(<NotificationBar {...defaultProps} />);

      const notificationBar = document.querySelector(".quote-notification-bar");
      expect(notificationBar).toBeInTheDocument();
    });

    test("当visible为false时不应该渲染通知栏", () => {
      render(<NotificationBar {...defaultProps} visible={false} />);

      const notificationBar = document.querySelector(".quote-notification-bar");
      expect(notificationBar).not.toBeInTheDocument();
    });
  });

  describe("数量显示", () => {
    test("应该显示正确的采集数量文本", () => {
      render(<NotificationBar {...defaultProps} count={3} />);

      expect(screen.getByText("已采集到知识库")).toBeInTheDocument();
    });

    test("个位数应该显示圆形徽章", () => {
      render(<NotificationBar {...defaultProps} count={5} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toBeInTheDocument();
      expect(badge).not.toHaveClass("two-digit");
      expect(badge).toHaveTextContent("5");
    });

    test("两位数应该显示圆角矩形徽章", () => {
      render(<NotificationBar {...defaultProps} count={25} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass("two-digit");
      expect(badge).toHaveTextContent("25");
    });

    test("超过99应该显示99+", () => {
      render(<NotificationBar {...defaultProps} count={150} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toBeInTheDocument();
      expect(badge).toHaveClass("two-digit");
      expect(badge).toHaveTextContent("99+");
    });

    test("应该正确处理边界值99", () => {
      render(<NotificationBar {...defaultProps} count={99} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toHaveTextContent("99");
      expect(badge).toHaveClass("two-digit");
    });

    test("应该正确处理边界值100", () => {
      render(<NotificationBar {...defaultProps} count={100} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toHaveTextContent("99+");
      expect(badge).toHaveClass("two-digit");
    });
  });

  describe("按钮功能", () => {
    test("应该渲染清除按钮", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      expect(clearButton).toBeInTheDocument();
      expect(clearButton).toHaveClass("quote-notification-btn", "danger");
    });

    test("应该渲染选择project按钮", () => {
      render(<NotificationBar {...defaultProps} />);

      const projectButton = screen.getByText("选择project");
      expect(projectButton).toBeInTheDocument();
      expect(projectButton).toHaveClass("quote-notification-btn");
      expect(projectButton).not.toHaveClass("danger");
    });

    test("点击选择project按钮应该调用onSelectProject", () => {
      const onSelectProject = jest.fn();
      render(
        <NotificationBar {...defaultProps} onSelectProject={onSelectProject} />
      );

      const projectButton = screen.getByText("选择project");
      fireEvent.click(projectButton);

      expect(onSelectProject).toHaveBeenCalledTimes(1);
    });
  });

  describe("确认对话框", () => {
    test("点击清除按钮应该显示确认对话框", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      expect(
        screen.getByRole("heading", { name: "确认清除" })
      ).toBeInTheDocument();
      expect(
        screen.getByText("确定要清除页面中所有高亮文本吗？此操作不可撤销。")
      ).toBeInTheDocument();
    });

    test("确认对话框应该有取消和确认按钮", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      expect(screen.getByText("取消")).toBeInTheDocument();
      expect(
        screen.getByRole("button", { name: "确认清除" })
      ).toBeInTheDocument();
    });

    test("点击取消应该关闭确认对话框", async () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      const cancelButton = screen.getByText("取消");
      fireEvent.click(cancelButton);

      await waitFor(() => {
        expect(screen.queryByText("确认清除")).not.toBeInTheDocument();
      });
    });

    test("点击遮罩层应该关闭确认对话框", async () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      const overlay = document.querySelector(".quote-confirm-dialog-overlay");
      expect(overlay).toBeInTheDocument();

      fireEvent.click(overlay!);

      await waitFor(() => {
        expect(
          screen.queryByRole("heading", { name: "确认清除" })
        ).not.toBeInTheDocument();
      });
    });

    test("点击确认清除应该调用onClear并关闭对话框", async () => {
      const onClear = jest.fn();
      render(<NotificationBar {...defaultProps} onClear={onClear} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      const confirmButton = screen.getByRole("button", { name: "确认清除" });
      fireEvent.click(confirmButton);

      expect(onClear).toHaveBeenCalledTimes(1);

      await waitFor(() => {
        expect(
          screen.queryByRole("button", { name: "确认清除" })
        ).not.toBeInTheDocument();
      });
    });
  });

  describe("样式类名", () => {
    test("通知栏应该有正确的CSS类名", () => {
      render(<NotificationBar {...defaultProps} />);

      const notificationBar = document.querySelector(".quote-notification-bar");
      expect(notificationBar).toHaveClass("quote-notification-bar");
    });

    test("数量区域应该有正确的CSS类名", () => {
      render(<NotificationBar {...defaultProps} />);

      const countSection = document.querySelector(".quote-notification-count");
      expect(countSection).toHaveClass("quote-notification-count");
    });

    test("按钮区域应该有正确的CSS类名", () => {
      render(<NotificationBar {...defaultProps} />);

      const actionsSection = document.querySelector(
        ".quote-notification-actions"
      );
      expect(actionsSection).toHaveClass("quote-notification-actions");
    });
  });

  describe("边界情况", () => {
    test("应该处理count为0的情况", () => {
      render(<NotificationBar {...defaultProps} count={0} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toHaveTextContent("0");
      expect(badge).not.toHaveClass("two-digit");
    });

    test("应该处理负数count", () => {
      render(<NotificationBar {...defaultProps} count={-5} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toHaveTextContent("-5");
    });

    test("应该处理非常大的count值", () => {
      render(<NotificationBar {...defaultProps} count={999999} />);

      const badge = document.querySelector(".quote-count-badge");
      expect(badge).toHaveTextContent("99+");
      expect(badge).toHaveClass("two-digit");
    });
  });

  describe("可访问性", () => {
    test("按钮应该是可访问的", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByRole("button", { name: "清除" });
      const projectButton = screen.getByRole("button", { name: "选择project" });

      expect(clearButton).toBeInTheDocument();
      expect(projectButton).toBeInTheDocument();
    });

    test("确认对话框应该有适当的标题", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      const dialogTitle = screen.getByRole("heading", { name: "确认清除" });
      expect(dialogTitle.tagName.toLowerCase()).toBe("h3");
    });

    test("确认对话框的按钮应该是可访问的", () => {
      render(<NotificationBar {...defaultProps} />);

      const clearButton = screen.getByText("清除");
      fireEvent.click(clearButton);

      const cancelButton = screen.getByRole("button", { name: "取消" });
      const confirmButton = screen.getByRole("button", { name: "确认清除" });

      expect(cancelButton).toBeInTheDocument();
      expect(confirmButton).toBeInTheDocument();
    });
  });
});
