/**
 * 高亮系统入口文件测试
 */

import {
  initHighlightSystem,
  destroyHighlightSystem,
  getHighlightSystemInstance,
} from "../../src/contentScript/highlight/index";

// Mock HighlightSystem
const mockDestroy = jest.fn();
const mockHighlightSystem = {
  destroy: mockDestroy,
};

jest.mock("../../src/contentScript/highlight/HighlightSystem", () => ({
  HighlightSystem: jest.fn(() => mockHighlightSystem),
}));

// Mock window.location
const mockLocation = {
  hostname: "example.com",
  pathname: "/test",
  href: "https://example.com/test",
};

// 删除现有的location属性并重新定义
delete (window as any).location;
(window as any).location = mockLocation;

// Mock window.location.hostname getter
Object.defineProperty(window, "location", {
  value: {
    ...mockLocation,
    get hostname() {
      return mockLocation.hostname;
    },
    set hostname(value) {
      mockLocation.hostname = value;
    },
  },
  writable: true,
  configurable: true,
});

describe("高亮系统入口", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // 重置location mock
    mockLocation.hostname = "example.com";
    mockLocation.pathname = "/test";
    mockLocation.href = "https://example.com/test";

    // 确保location对象被正确设置
    (window as any).location = mockLocation;
  });

  afterEach(() => {
    // 清理系统实例
    destroyHighlightSystem();
  });

  describe("shouldEnableHighlightSystem", () => {
    test("应该在普通网站上启用高亮系统", () => {
      mockLocation.hostname = "example.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeTruthy();
    });

    test("应该在威科先行站点上禁用高亮系统", () => {
      mockLocation.hostname = "law.wkinfo.com.cn";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该在北大法宝站点上禁用高亮系统", () => {
      mockLocation.hostname = "www.pkulaw.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该在中国裁判文书网站点上禁用高亮系统", () => {
      mockLocation.hostname = "wenshu.court.gov.cn";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该在pkulaw.com站点上禁用高亮系统", () => {
      mockLocation.hostname = "pkulaw.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该检测威科先行镜像站点", () => {
      mockLocation.hostname = "law.wkinf123.ffdtwsa.top";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该检测北大法宝镜像站点", () => {
      mockLocation.hostname = "mirror.pkulaw.example.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该检测中国裁判文书网镜像站点", () => {
      mockLocation.hostname = "wenshu.mirror.example.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该在子域名上正确检测排除站点", () => {
      mockLocation.hostname = "subdomain.law.wkinfo.com.cn";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });
  });

  describe("initHighlightSystem", () => {
    test("应该在支持的站点上创建高亮系统实例", () => {
      mockLocation.hostname = "example.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeTruthy();
    });

    test("应该避免重复初始化", () => {
      mockLocation.hostname = "example.com";

      initHighlightSystem();
      const firstInstance = getHighlightSystemInstance();

      initHighlightSystem();
      const secondInstance = getHighlightSystemInstance();

      expect(firstInstance).toBe(secondInstance);
    });

    test("应该处理初始化错误", () => {
      const {
        HighlightSystem,
      } = require("../../src/contentScript/highlight/HighlightSystem");
      HighlightSystem.mockImplementationOnce(() => {
        throw new Error("初始化失败");
      });

      mockLocation.hostname = "example.com";

      // 应该不抛出错误
      expect(() => {
        initHighlightSystem();
      }).not.toThrow();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });
  });

  describe("destroyHighlightSystem", () => {
    test("应该销毁现有的高亮系统实例", () => {
      mockLocation.hostname = "example.com";

      initHighlightSystem();
      expect(getHighlightSystemInstance()).toBeTruthy();

      destroyHighlightSystem();
      expect(getHighlightSystemInstance()).toBeNull();
      expect(mockDestroy).toHaveBeenCalledTimes(1);
    });

    test("应该处理没有实例时的销毁调用", () => {
      // 确保没有实例
      destroyHighlightSystem();

      // 再次调用销毁应该不抛出错误
      expect(() => {
        destroyHighlightSystem();
      }).not.toThrow();
    });
  });

  describe("getHighlightSystemInstance", () => {
    test("应该返回当前的高亮系统实例", () => {
      mockLocation.hostname = "example.com";

      expect(getHighlightSystemInstance()).toBeNull();

      initHighlightSystem();
      expect(getHighlightSystemInstance()).toBeTruthy();

      destroyHighlightSystem();
      expect(getHighlightSystemInstance()).toBeNull();
    });
  });

  describe("域名检测边界情况", () => {
    test("应该处理空域名", () => {
      mockLocation.hostname = "";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeTruthy();
    });

    test("应该处理大小写混合的域名", () => {
      mockLocation.hostname = "LAW.WKINFO.COM.CN";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该处理包含端口号的域名", () => {
      mockLocation.hostname = "law.wkinfo.com.cn:8080";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      // 注意：hostname通常不包含端口号，但这里测试边界情况
      expect(instance).toBeNull();
    });

    test("应该正确处理相似但不同的域名", () => {
      mockLocation.hostname = "notlaw.wkinfo.com.cn";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeNull();
    });

    test("应该正确处理包含关键词但不是目标站点的域名", () => {
      mockLocation.hostname = "lawfirm.example.com";

      initHighlightSystem();

      const instance = getHighlightSystemInstance();
      expect(instance).toBeTruthy(); // 这不是法律数据库站点
    });
  });

  describe("控制台日志", () => {
    let consoleSpy: jest.SpyInstance;

    beforeEach(() => {
      consoleSpy = jest.spyOn(console, "log").mockImplementation();
    });

    afterEach(() => {
      consoleSpy.mockRestore();
    });

    test("应该记录支持的站点", () => {
      mockLocation.hostname = "example.com";

      initHighlightSystem();

      expect(consoleSpy).toHaveBeenCalledWith(
        "[HighlightSystem] 当前站点支持高亮系统:",
        "example.com"
      );
    });

    test("应该记录排除的站点", () => {
      mockLocation.hostname = "law.wkinfo.com.cn";

      initHighlightSystem();

      expect(consoleSpy).toHaveBeenCalledWith(
        "[HighlightSystem] 当前站点已被排除:",
        "law.wkinfo.com.cn"
      );
    });

    test("应该记录镜像站点检测", () => {
      mockLocation.hostname = "law.wkinf123.example.com";

      initHighlightSystem();

      expect(consoleSpy).toHaveBeenCalledWith(
        "[HighlightSystem] 检测到法律数据库镜像站点，已排除:",
        "law.wkinf123.example.com"
      );
    });
  });
});
