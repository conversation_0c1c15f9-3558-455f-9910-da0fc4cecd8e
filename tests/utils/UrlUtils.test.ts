/**
 * UrlUtils 测试
 * 验证URL处理功能的正确性
 */

import { UrlUtils } from "../../src/utils/UrlUtils";

describe("UrlUtils", () => {
  describe("cleanAndTruncateUrl", () => {
    it("应该保持短URL不变", () => {
      const shortUrl = "https://www.pkulaw.com/case/123456";
      const result = UrlUtils.cleanAndTruncateUrl(shortUrl);
      
      expect(result).toBe(shortUrl);
    });

    it("应该保持中等长度URL不变", () => {
      const mediumUrl = "https://www.pkulaw.com/case/123456?searchId=abc&index=1";
      const result = UrlUtils.cleanAndTruncateUrl(mediumUrl);
      
      // 只有超长URL才处理，中等长度URL保持不变
      expect(result).toBe(mediumUrl);
    });

    it("应该正确处理威科先行超长URL", () => {
      const longWkinfoUrl = "http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/MjA0MDMyMzc5ODI%3D?searchId=f74224483b28478396a327bdbf775a64&index=1&q=77&module=&childModule=all&summary=%E9%BB%91%E9%BE%99%E6%B1%9F%E7%9C%81%E5%93%88%E5%B0%94%E6%BB%A8%E5%B8%82%E9%81%93%E5%A4%96%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%0D%E5%86%B3%E5%AE%9A%E4%B9%A6%0D%0D%EF%BC%882025%EF%BC%89%E9%BB%9101";
      const result = UrlUtils.cleanAndTruncateUrl(longWkinfoUrl);
      
      expect(result).toBe("http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/MjA0MDMyMzc5ODI%3D");
      expect(result.length).toBeLessThanOrEqual(255);
    });

    it("应该正确处理威科先行官网超长URL", () => {
      const longWkinfoUrl = "https://www.wkinfo.com.cn/judgment-documents/detail/ABC123DEF456?searchId=test&summary=很长的中文摘要内容".repeat(10);
      const result = UrlUtils.cleanAndTruncateUrl(longWkinfoUrl);
      
      expect(result).toContain("https://www.wkinfo.com.cn/judgment-documents/detail/");
      expect(result.length).toBeLessThanOrEqual(255);
    });

    it("应该正确处理北大法宝超长URL", () => {
      const longPkulawUrl = "https://www.pkulaw.com/case/very-long-case-id-that-exceeds-normal-length?searchId=test&summary=很长的查询参数".repeat(5);
      const result = UrlUtils.cleanAndTruncateUrl(longPkulawUrl);
      
      expect(result).toContain("https://www.pkulaw.com");
      expect(result.length).toBeLessThanOrEqual(255);
    });

    it("应该正确处理中国裁判文书网超长URL", () => {
      const longWenshuUrl = "https://wenshu.court.gov.cn/website/wenshu/very-long-document-path?docId=test&summary=很长的文档摘要".repeat(5);
      const result = UrlUtils.cleanAndTruncateUrl(longWenshuUrl);
      
      expect(result).toContain("https://wenshu.court.gov.cn");
      expect(result.length).toBeLessThanOrEqual(255);
    });

    it("应该正确处理其他网站超长URL", () => {
      const longGenericUrl = "https://example.com/very/long/path/that/exceeds/normal/length?param1=value1&param2=value2".repeat(3);
      const result = UrlUtils.cleanAndTruncateUrl(longGenericUrl);
      
      expect(result).toContain("https://example.com");
      expect(result.length).toBeLessThanOrEqual(255);
    });

    it("应该处理无效URL", () => {
      const invalidUrl = "not-a-valid-url-but-very-long".repeat(20);
      const result = UrlUtils.cleanAndTruncateUrl(invalidUrl);
      
      expect(result.length).toBeLessThanOrEqual(255);
      expect(result).toContain("...");
    });

    it("应该支持自定义最大长度", () => {
      const longUrl = "https://example.com/path".repeat(10);
      const result = UrlUtils.cleanAndTruncateUrl(longUrl, 50);
      
      expect(result.length).toBeLessThanOrEqual(50);
    });
  });

  describe("getUrlProcessingInfo", () => {
    it("应该正确识别未处理的URL", () => {
      const url = "https://www.pkulaw.com/case/123";
      const info = UrlUtils.getUrlProcessingInfo(url, url);
      
      expect(info.wasProcessed).toBe(false);
      expect(info.processingType).toBe("none");
      expect(info.lengthReduction).toBe(0);
    });

    it("应该正确识别被截断的超长URL", () => {
      const originalUrl = "https://example.com/very-long-path".repeat(20);
      const processedUrl = "https://example.com/very-long-path...";
      const info = UrlUtils.getUrlProcessingInfo(originalUrl, processedUrl);
      
      expect(info.wasProcessed).toBe(true);
      expect(info.processingType).toBe("truncated_oversize");
      expect(info.lengthReduction).toBeGreaterThan(0);
      expect(info.originalLength).toBe(originalUrl.length);
      expect(info.processedLength).toBe(processedUrl.length);
    });
  });

  describe("威科先行特殊测试", () => {
    it("应该保留威科先行的核心URL结构", () => {
      const wkinfoUrl = "http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/MjA0MDMyMzc5ODI%3D?searchId=f74224483b28478396a327bdbf775a64&index=1&q=77&module=&childModule=all&summary=%E9%BB%91%E9%BE%99%E6%B1%9F%E7%9C%81%E5%93%88%E5%B0%94%E6%BB%A8%E5%B8%82%E9%81%93%E5%A4%96%E5%8C%BA%E4%BA%BA%E6%B0%91%E6%B3%95%E9%99%A2%0D%E5%86%B3%E5%AE%9A%E4%B9%A6%0D%0D%EF%BC%882025%EF%BC%89%E9%BB%9101";
      const result = UrlUtils.cleanAndTruncateUrl(wkinfoUrl);
      
      // 应该保留到文档ID，不进一步简化
      expect(result).toBe("http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/MjA0MDMyMzc5ODI%3D");
      
      // 应该移除所有查询参数
      expect(result).not.toContain("searchId");
      expect(result).not.toContain("summary");
      expect(result).not.toContain("?");
      
      // 应该保留文档ID
      expect(result).toContain("MjA0MDMyMzc5ODI%3D");
    });

    it("应该处理威科先行极长文档ID的情况", () => {
      const veryLongId = "A".repeat(200);
      const wkinfoUrl = `http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/${veryLongId}?searchId=test`;
      const result = UrlUtils.cleanAndTruncateUrl(wkinfoUrl);
      
      expect(result).toContain("http://law.wkinf123.ffdtwsa.top/judgment-documents/detail/");
      expect(result.length).toBeLessThanOrEqual(255);
      expect(result).toContain("...");
    });
  });
});
