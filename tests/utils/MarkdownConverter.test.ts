/**
 * MarkdownConverter 测试
 * 验证HTML到Markdown转换功能的正确性
 */

import { MarkdownConverter } from "../../src/utils/MarkdownConverter";

// 模拟DOM环境
const createMockElement = (html: string): Element => {
  const div = document.createElement("div");
  div.innerHTML = html;
  return div;
};

describe("MarkdownConverter", () => {
  describe("htmlToMarkdown", () => {
    it("应该正确转换标题", () => {
      const html = "<h1>一级标题</h1><h2>二级标题</h2><h3>三级标题</h3>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("# 一级标题");
      expect(result).toContain("## 二级标题");
      expect(result).toContain("### 三级标题");
    });

    it("应该正确转换段落", () => {
      const html = "<p>第一段内容</p><p>第二段内容</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toBe("第一段内容\n\n第二段内容");
    });

    it("应该正确转换无序列表", () => {
      const html = "<ul><li>项目1</li><li>项目2</li><li>项目3</li></ul>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("- 项目1");
      expect(result).toContain("- 项目2");
      expect(result).toContain("- 项目3");
    });

    it("应该正确转换有序列表", () => {
      const html = "<ol><li>第一项</li><li>第二项</li><li>第三项</li></ol>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("1. 第一项");
      expect(result).toContain("2. 第二项");
      expect(result).toContain("3. 第三项");
    });

    it("应该正确转换粗体文本", () => {
      const html = "<p>这是<strong>粗体文本</strong>的示例</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("**粗体文本**");
    });

    it("应该正确转换斜体文本", () => {
      const html = "<p>这是<em>斜体文本</em>的示例</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        preserveItalic: true,
      });

      expect(result).toContain("*斜体文本*");
    });

    it("应该正确转换链接", () => {
      const html = '<p>访问<a href="https://example.com">示例网站</a></p>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        preserveLinks: true,
      });

      expect(result).toContain("[示例网站](https://example.com)");
    });

    it("应该在禁用链接时只保留文本", () => {
      const html =
        '<p>根据《<a href="/law.html">中华人民共和国招标投标法</a>》第<a href="/article.html">四十五条</a></p>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        preserveLinks: false,
      });

      expect(result).toBe("根据《中华人民共和国招标投标法》第四十五条");
      expect(result).not.toContain("/law.html");
      expect(result).not.toContain("/article.html");
    });

    it("应该处理换行符", () => {
      const html = "<p>第一行<br>第二行</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("第一行\n第二行");
    });

    it("应该忽略脚本和样式标签", () => {
      const html =
        '<p>正常内容</p><script>alert("test")</script><style>.test{}</style>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toBe("正常内容");
      expect(result).not.toContain("alert");
      expect(result).not.toContain(".test");
    });

    it("应该处理复杂的嵌套结构", () => {
      const html = `
        <div>
          <h2>案件信息</h2>
          <p>案件编号：<strong>(2023)京01民终1234号</strong></p>
          <p>审理法院：北京市第一中级人民法院</p>
          <h3>争议焦点</h3>
          <ul>
            <li>合同效力问题</li>
            <li>违约责任认定</li>
          </ul>
        </div>
      `;
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("## 案件信息");
      expect(result).toContain("**(2023)京01民终1234号**");
      expect(result).toContain("### 争议焦点");
      expect(result).toContain("- 合同效力问题");
      expect(result).toContain("- 违约责任认定");
    });
  });

  describe("detectParagraphs", () => {
    it("应该检测明确的段落元素", () => {
      const html = "<p>第一段</p><p>第二段</p><p>第三段</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.detectParagraphs(element);

      expect(result).toBe("第一段\n\n第二段\n\n第三段");
    });

    it("应该通过双换行分割段落", () => {
      const html = "<div>第一段内容\n\n第二段内容\n\n第三段内容</div>";
      const element = createMockElement(html);
      const result = MarkdownConverter.detectParagraphs(element);

      expect(result).toContain("第一段内容");
      expect(result).toContain("第二段内容");
      expect(result).toContain("第三段内容");
    });

    it("应该过滤太短的段落", () => {
      const html = "<p>正常段落内容</p><p>短</p><p>另一个正常段落</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.detectParagraphs(element);

      expect(result).toContain("正常段落内容");
      expect(result).toContain("另一个正常段落");
      expect(result).not.toContain("短\n\n");
    });
  });

  describe("extractHeadings", () => {
    it("应该提取所有级别的标题", () => {
      const html = `
        <h1>主标题</h1>
        <h2>二级标题</h2>
        <h3>三级标题</h3>
        <h4>四级标题</h4>
      `;
      const element = createMockElement(html);
      const result = MarkdownConverter.extractHeadings(element);

      expect(result).toContain("# 主标题");
      expect(result).toContain("## 二级标题");
      expect(result).toContain("### 三级标题");
      expect(result).toContain("#### 四级标题");
    });

    it("应该忽略空标题", () => {
      const html = "<h1>有内容的标题</h1><h2></h2><h3>   </h3>";
      const element = createMockElement(html);
      const result = MarkdownConverter.extractHeadings(element);

      expect(result).toContain("# 有内容的标题");
      expect(result).not.toContain("##\n");
      expect(result).not.toContain("###\n");
    });
  });

  describe("配置选项测试", () => {
    it("应该根据配置禁用标题格式", () => {
      const html = "<h1>标题</h1><p>内容</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        preserveHeadings: false,
      });

      expect(result).not.toContain("# 标题");
      expect(result).toContain("标题");
    });

    it("应该根据配置禁用列表格式", () => {
      const html = "<ul><li>项目1</li><li>项目2</li></ul>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        preserveLists: false,
      });

      expect(result).not.toContain("- 项目1");
      expect(result).toContain("项目1");
      expect(result).toContain("项目2");
    });

    it("应该使用自定义段落分隔符", () => {
      const html = "<p>段落1</p><p>段落2</p>";
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element, {
        paragraphSeparator: "\n---\n",
      });

      expect(result).toContain("段落1\n---\n段落2");
    });
  });

  describe("中国裁判文书网特殊格式测试", () => {
    it("应该正确处理PDF_title主标题", () => {
      const html =
        '<div class="PDF_title">辛某春贪污罪刑事申诉再审审查刑事通知书</div>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("# 辛某春贪污罪刑事申诉再审审查刑事通知书");
    });

    it("应该正确处理机构名称标题", () => {
      const html =
        '<div style="TEXT-ALIGN: center; FONT-FAMILY: 黑体; FONT-SIZE: 18pt;">中华人民共和国最高人民法院</div>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("## 中华人民共和国最高人民法院");
    });

    it("应该正确处理文书类型标题", () => {
      const html =
        '<div style="TEXT-ALIGN: center; FONT-FAMILY: 黑体; FONT-SIZE: 18pt;">通知书</div>';
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("## 通知书");
    });

    it("应该正确处理PDF_pox内的段落div", () => {
      const html = `
        <div class="PDF_pox">
          <div id="1">（2025）最高法刑申37号</div>
          <div id="2">辛某春：</div>
          <div id="3">你因贪污一案，不服相关判决...</div>
        </div>
      `;
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("（2025）最高法刑申37号");
      expect(result).toContain("辛某春：");
      expect(result).toContain("你因贪污一案，不服相关判决...");
      // 检查段落分隔 - 现在每个div都会分段
      expect(result.split("\n\n").length).toBeGreaterThanOrEqual(3);
    });

    it("应该对所有div进行分段处理", () => {
      const html = `
        <div>
          <div>第一段内容</div>
          <div>第二段内容</div>
          <div>第三段内容</div>
        </div>
      `;
      const element = createMockElement(html);
      const result = MarkdownConverter.htmlToMarkdown(element);

      expect(result).toContain("第一段内容");
      expect(result).toContain("第二段内容");
      expect(result).toContain("第三段内容");
      // 每个div都应该分段
      const paragraphs = result.split("\n\n").filter((p) => p.trim());
      expect(paragraphs.length).toBe(3);
    });
  });

  describe("postProcessMarkdown", () => {
    it("应该清理多余空行", () => {
      const content = "标题1\n\n\n\n\n内容1\n\n\n\n标题2\n\n\n\n\n内容2";
      const result = MarkdownConverter.postProcessMarkdown(content);

      expect(result).toBe("标题1\n\n内容1\n\n标题2\n\n内容2");
    });

    it("应该移除完全重复的主标题", () => {
      const content = `## （2025）黑0104清申96号受理债权人的强制清算申请-民事****

## （2025）黑0104清申96号受理债权人的强制清算申请-民事****

案由

民事>与公司、证券、保险、票据等有关的民事纠纷`;

      const result = MarkdownConverter.postProcessMarkdown(content);

      expect(result).toContain(
        "## （2025）黑0104清申96号受理债权人的强制清算申请-民事****"
      );
      // 应该只出现一次
      const titleCount = (
        result.match(
          /## （2025）黑0104清申96号受理债权人的强制清算申请-民事\*\*\*\*/g
        ) || []
      ).length;
      expect(titleCount).toBe(1);
    });

    it("应该保留近似但不完全相同的标题", () => {
      const content = `## 标题A

## 标题B

## 标题A（不同）`;

      const result = MarkdownConverter.postProcessMarkdown(content);

      // 应该保留所有标题，因为它们不完全相同
      expect(result).toContain("## 标题A");
      expect(result).toContain("## 标题B");
      expect(result).toContain("## 标题A（不同）");
    });

    it("应该移除重复的法院信息", () => {
      const content = `## 主标题

#### 黑龙江省****(2025)黑****清申96号2025.07**** 裁判

审理法院：
黑龙江省****

案号：
(2025)黑****清申96号`;

      const result = MarkdownConverter.postProcessMarkdown(content);

      expect(result).toContain("## 主标题");
      expect(result).toContain("审理法院：");
      expect(result).toContain("案号：");
      // 应该移除重复的法院信息行
      expect(result).not.toContain(
        "#### 黑龙江省****(2025)黑****清申96号2025.07**** 裁判"
      );
    });

    it("应该综合处理所有问题", () => {
      const content = `## （2025）黑0104清申96号受理债权人的强制清算申请-民事****



## （2025）黑0104清申96号受理债权人的强制清算申请-民事****



#### 黑龙江省****(2025)黑****清申96号2025.07**** 裁判



案由



审理法院：
黑龙江省****


案号：
(2025)黑****清申96号`;

      const result = MarkdownConverter.postProcessMarkdown(content);

      // 检查重复标题被移除
      const titleCount = (
        result.match(
          /## （2025）黑0104清申96号受理债权人的强制清算申请-民事\*\*\*\*/g
        ) || []
      ).length;
      expect(titleCount).toBe(1);

      // 检查法院信息被移除
      expect(result).not.toContain(
        "#### 黑龙江省****(2025)黑****清申96号2025.07**** 裁判"
      );

      // 检查多余空行被清理 - 修改检查方式
      const hasTripleNewlines = result.includes("\n\n\n");
      expect(hasTripleNewlines).toBe(false);

      // 检查保留的内容
      expect(result).toContain("案由");
      expect(result).toContain("审理法院：");
      expect(result).toContain("案号：");
    });
  });
});
