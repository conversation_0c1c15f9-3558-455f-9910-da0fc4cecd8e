/**
 * Quote采集功能集成测试
 */

import { AuthService } from '../../src/contentScript/services/AuthService';

// 模拟 LoginPrompt 组件
jest.mock('../../src/contentScript/components/LoginPrompt', () => ({
  createLoginPrompt: jest.fn(() => ({
    show: jest.fn().mockResolvedValue('login'),
  })),
}));

describe('Quote Collection Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    AuthService.reset();
  });

  describe('认证流程集成测试', () => {
    it('应该完成完整的认证检查流程', async () => {
      // 模拟认证成功的响应
      const mockAuthResponse = {
        success: true,
        isAuthenticated: true,
      };
      
      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockAuthResponse);

      // 执行认证检查
      const authStatus = await AuthService.checkAuthenticationStatus();

      // 验证结果
      expect(authStatus.isAuthenticated).toBe(true);
      expect(authStatus.error).toBeUndefined();
      
      // 验证API调用
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'CHECK_AUTH_STATUS',
      });
    });

    it('应该处理未认证用户的登录流程', async () => {
      // 模拟未认证的响应
      const mockAuthResponse = {
        success: true,
        isAuthenticated: false,
        error: '用户未登录',
      };
      
      const mockLoginResponse = {
        success: true,
      };

      (chrome.runtime.sendMessage as jest.Mock)
        .mockResolvedValueOnce(mockAuthResponse) // 第一次调用：认证检查
        .mockResolvedValueOnce(mockLoginResponse); // 第二次调用：打开登录页

      // 执行认证检查
      const authStatus = await AuthService.checkAuthenticationStatus();
      expect(authStatus.isAuthenticated).toBe(false);

      // 执行登录流程
      await AuthService.handleLogin();

      // 验证登录API调用
      expect(chrome.runtime.sendMessage).toHaveBeenCalledWith({
        type: 'OPEN_LOGIN_TAB',
        data: {
          loginUrl: 'https://test-redirect.example.com/login',
        },
      });
    });
  });

  describe('项目数据获取集成测试', () => {
    it('应该成功获取项目列表', async () => {
      // 模拟项目列表响应
      const mockProjectsResponse = {
        success: true,
        projects: [
          {
            id: 'project-1',
            name: '测试项目1',
            last_edited_at: '2024-01-01T00:00:00Z',
          },
          {
            id: 'project-2',
            name: 'Test Project 2',
            last_edited_at: '2024-01-02T00:00:00Z',
          },
        ],
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockProjectsResponse);

      // 模拟获取项目列表的调用
      const response = await chrome.runtime.sendMessage({
        type: 'GET_PROJECTS',
      });

      expect(response.success).toBe(true);
      expect(response.projects).toHaveLength(2);
      expect(response.projects[0].name).toBe('测试项目1');
      expect(response.projects[1].name).toBe('Test Project 2');
    });

    it('应该处理项目列表获取失败', async () => {
      // 模拟失败响应
      const mockErrorResponse = {
        success: false,
        error: '无法获取项目列表',
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockErrorResponse);

      const response = await chrome.runtime.sendMessage({
        type: 'GET_PROJECTS',
      });

      expect(response.success).toBe(false);
      expect(response.error).toBe('无法获取项目列表');
    });
  });

  describe('内容采集集成测试', () => {
    it('应该成功采集内容到指定项目', async () => {
      // 模拟采集成功响应
      const mockCollectResponse = {
        success: true,
        data: {
          id: 'quote-123',
          content: '测试采集内容',
          projectId: 'project-1',
        },
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockCollectResponse);

      // 模拟采集到项目的调用
      const response = await chrome.runtime.sendMessage({
        type: 'COLLECT_TO_PROJECT',
        data: {
          content: '测试采集内容',
          projectId: 'project-1',
          url: 'https://example.com',
          title: '测试页面',
        },
      });

      expect(response.success).toBe(true);
      expect(response.data.content).toBe('测试采集内容');
      expect(response.data.projectId).toBe('project-1');
    });

    it('应该成功采集内容到收件箱', async () => {
      // 模拟采集到收件箱成功响应
      const mockInboxResponse = {
        success: true,
        data: {
          id: 'quote-456',
          content: '测试收件箱内容',
          projectId: 'inbox',
        },
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockInboxResponse);

      // 模拟采集到收件箱的调用
      const response = await chrome.runtime.sendMessage({
        type: 'COLLECT_TO_INBOX',
        data: {
          content: '测试收件箱内容',
          url: 'https://example.com',
          title: '测试页面',
        },
      });

      expect(response.success).toBe(true);
      expect(response.data.content).toBe('测试收件箱内容');
      expect(response.data.projectId).toBe('inbox');
    });

    it('应该处理采集失败的情况', async () => {
      // 模拟采集失败响应
      const mockErrorResponse = {
        success: false,
        error: '采集失败：网络错误',
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockErrorResponse);

      const response = await chrome.runtime.sendMessage({
        type: 'COLLECT_TO_PROJECT',
        data: {
          content: '测试内容',
          projectId: 'project-1',
          url: 'https://example.com',
          title: '测试页面',
        },
      });

      expect(response.success).toBe(false);
      expect(response.error).toBe('采集失败：网络错误');
    });
  });

  describe('错误处理集成测试', () => {
    it('应该处理网络连接错误', async () => {
      // 模拟网络错误
      const networkError = new Error('网络连接失败');
      (chrome.runtime.sendMessage as jest.Mock).mockRejectedValue(networkError);

      // 测试认证服务的错误处理
      const authStatus = await AuthService.checkAuthenticationStatus();
      
      expect(authStatus.isAuthenticated).toBe(false);
      expect(authStatus.error).toBe('网络连接失败');
    });

    it('应该处理API服务不可用的情况', async () => {
      // 模拟服务不可用
      const mockErrorResponse = {
        success: false,
        error: 'API服务暂时不可用',
      };

      (chrome.runtime.sendMessage as jest.Mock).mockResolvedValue(mockErrorResponse);

      const authStatus = await AuthService.checkAuthenticationStatus();
      
      expect(authStatus.isAuthenticated).toBe(false);
      expect(authStatus.error).toBe('API服务暂时不可用');
    });
  });
});
