<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格高亮布局修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-section h3 {
            margin-top: 0;
            color: #555;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f8f9fa;
            font-weight: 600;
        }
        .instructions {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pass { background-color: #28a745; }
        .status-fail { background-color: #dc3545; }
        .status-pending { background-color: #ffc107; }
    </style>
</head>
<body>
    <div class="content">
        <h1>表格高亮布局修复测试</h1>
        
        <div class="instructions">
            <h3>🎯 测试目标</h3>
            <p>验证跨表格单元格的文本高亮不会破坏表格布局。修复后应该：</p>
            <ul>
                <li>✅ 高亮功能正常工作</li>
                <li>✅ 表格布局保持不变</li>
                <li>✅ 单元格尺寸不发生变化</li>
                <li>✅ 表格边框和对齐保持正常</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>测试案例1: 简单2x2表格</h3>
            <p><strong>测试方法</strong>：选择从"数据A"到"数据D"的跨单元格文本</p>
            <table>
                <tr>
                    <td>数据A</td>
                    <td>数据B</td>
                </tr>
                <tr>
                    <td>数据C</td>
                    <td>数据D</td>
                </tr>
            </table>
            <p><span class="status-indicator status-pending"></span>布局状态：待测试</p>
        </div>

        <div class="test-section">
            <h3>测试案例2: 带表头的数据表格</h3>
            <p><strong>测试方法</strong>：选择从"产品名称"到"库存"的跨列文本</p>
            <table>
                <thead>
                    <tr>
                        <th>产品名称</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>笔记本电脑</td>
                        <td>¥5,999</td>
                        <td>15台</td>
                        <td>有货</td>
                    </tr>
                    <tr>
                        <td>无线鼠标</td>
                        <td>¥199</td>
                        <td>50个</td>
                        <td>有货</td>
                    </tr>
                    <tr>
                        <td>机械键盘</td>
                        <td>¥899</td>
                        <td>0个</td>
                        <td>缺货</td>
                    </tr>
                </tbody>
            </table>
            <p><span class="status-indicator status-pending"></span>布局状态：待测试</p>
        </div>

        <div class="test-section">
            <h3>测试案例3: 复杂表格（合并单元格）</h3>
            <p><strong>测试方法</strong>：选择跨越合并单元格的文本</p>
            <table>
                <tr>
                    <th rowspan="2">类别</th>
                    <th colspan="2">销售数据</th>
                    <th rowspan="2">总计</th>
                </tr>
                <tr>
                    <th>Q1</th>
                    <th>Q2</th>
                </tr>
                <tr>
                    <td>电子产品</td>
                    <td>1,200万</td>
                    <td>1,350万</td>
                    <td>2,550万</td>
                </tr>
                <tr>
                    <td>服装配饰</td>
                    <td>800万</td>
                    <td>920万</td>
                    <td>1,720万</td>
                </tr>
            </table>
            <p><span class="status-indicator status-pending"></span>布局状态：待测试</p>
        </div>

        <div class="test-section">
            <h3>测试案例4: 大型数据表格</h3>
            <p><strong>测试方法</strong>：选择跨多行多列的大范围文本</p>
            <table>
                <tr>
                    <th>ID</th>
                    <th>姓名</th>
                    <th>部门</th>
                    <th>职位</th>
                    <th>入职日期</th>
                    <th>薪资</th>
                </tr>
                <tr>
                    <td>001</td>
                    <td>张三</td>
                    <td>技术部</td>
                    <td>前端工程师</td>
                    <td>2023-01-15</td>
                    <td>¥12,000</td>
                </tr>
                <tr>
                    <td>002</td>
                    <td>李四</td>
                    <td>产品部</td>
                    <td>产品经理</td>
                    <td>2023-02-20</td>
                    <td>¥15,000</td>
                </tr>
                <tr>
                    <td>003</td>
                    <td>王五</td>
                    <td>设计部</td>
                    <td>UI设计师</td>
                    <td>2023-03-10</td>
                    <td>¥10,000</td>
                </tr>
                <tr>
                    <td>004</td>
                    <td>赵六</td>
                    <td>技术部</td>
                    <td>后端工程师</td>
                    <td>2023-04-05</td>
                    <td>¥13,000</td>
                </tr>
            </table>
            <p><span class="status-indicator status-pending"></span>布局状态：待测试</p>
        </div>

        <h2>🔍 检查要点</h2>
        <div class="instructions">
            <h4>高亮前后对比检查：</h4>
            <ol>
                <li><strong>表格宽度</strong>：高亮前后表格总宽度应保持一致</li>
                <li><strong>单元格尺寸</strong>：各单元格的宽度和高度不应发生变化</li>
                <li><strong>文本对齐</strong>：单元格内文本的对齐方式保持不变</li>
                <li><strong>边框样式</strong>：表格边框应保持原有样式</li>
                <li><strong>背景色</strong>：只有高亮文本有背景色，不影响单元格背景</li>
            </ol>
            
            <h4>控制台日志检查：</h4>
            <ul>
                <li>应该看到"检测到表格选择，使用表格专用算法"</li>
                <li>应该显示表格单元格数量统计</li>
                <li>应该显示每个单元格高亮的创建过程</li>
            </ul>
        </div>

        <h2>📊 预期改进效果</h2>
        <table>
            <tr>
                <th>方面</th>
                <th>修复前</th>
                <th>修复后</th>
            </tr>
            <tr>
                <td>表格布局</td>
                <td>❌ 可能变形</td>
                <td>✅ 保持稳定</td>
            </tr>
            <tr>
                <td>单元格尺寸</td>
                <td>❌ 可能变化</td>
                <td>✅ 保持原样</td>
            </tr>
            <tr>
                <td>高亮效果</td>
                <td>✅ 正常</td>
                <td>✅ 正常</td>
            </tr>
            <tr>
                <td>交互功能</td>
                <td>✅ 正常</td>
                <td>✅ 正常</td>
            </tr>
        </table>
    </div>

    <script>
        console.log('表格高亮布局修复测试页面加载完成');
        
        // 监听高亮事件，检查表格布局变化
        let originalTableSizes = new Map();
        
        // 记录原始表格尺寸
        function recordOriginalSizes() {
            document.querySelectorAll('table').forEach((table, index) => {
                const rect = table.getBoundingClientRect();
                originalTableSizes.set(index, {
                    width: rect.width,
                    height: rect.height,
                    cellSizes: Array.from(table.querySelectorAll('td, th')).map(cell => {
                        const cellRect = cell.getBoundingClientRect();
                        return { width: cellRect.width, height: cellRect.height };
                    })
                });
            });
            console.log('记录原始表格尺寸:', originalTableSizes);
        }
        
        // 检查表格尺寸变化
        function checkTableSizes() {
            let hasChanges = false;
            document.querySelectorAll('table').forEach((table, index) => {
                const rect = table.getBoundingClientRect();
                const original = originalTableSizes.get(index);
                
                if (original) {
                    const widthDiff = Math.abs(rect.width - original.width);
                    const heightDiff = Math.abs(rect.height - original.height);
                    
                    if (widthDiff > 1 || heightDiff > 1) { // 允许1px的误差
                        console.warn(`表格${index}尺寸发生变化:`, {
                            原始: { width: original.width, height: original.height },
                            当前: { width: rect.width, height: rect.height },
                            差异: { width: widthDiff, height: heightDiff }
                        });
                        hasChanges = true;
                    }
                }
            });
            
            if (!hasChanges) {
                console.log('✅ 表格布局检查通过，无尺寸变化');
            }
        }
        
        // 页面加载后记录原始尺寸
        setTimeout(recordOriginalSizes, 1000);
        
        // 监听DOM变化（高亮添加/删除）
        const observer = new MutationObserver(() => {
            setTimeout(checkTableSizes, 100);
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true,
            attributes: true,
            attributeFilter: ['class']
        });
    </script>
</body>
</html>
