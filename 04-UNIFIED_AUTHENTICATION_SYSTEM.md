# Quote 统一身份认证系统实现指南（简化版）

## 🎯 概述

本文档说明简化重构后的统一身份认证实现。简化版本保留了对侧边栏组件的认证支持，并在后台脚本中实现了基本的认证状态检查。

## 🏗️ 1. 认证架构概览（简化版）

### 简化技术栈

- **认证SDK**: `@quote/auth-client` v1.0.0（仅在侧边栏中使用）
- **后台认证**: 直接fetch API调用认证服务
- **状态管理**: React Hooks（侧边栏）+ 简单状态检查
- **存储机制**: HTTP-only Cookie + JWT

### 简化架构组成

```
浏览器插件（简化版）
├── src/background.ts           # 后台脚本，直接调用认证API
├── src/components/auth/        # 侧边栏认证组件（保留）
├── src/content.ts              # 内容脚本，简化的认证状态检查
└── 简化的消息传递            # 通过Chrome Runtime API
```

## 🔌 2. SDK 集成方式

### 本地开发链接

```bash
# SDK 通过符号链接指向本地开发目录
@quote/auth-client@1.0.0 extraneous -> ./../quote-unified-auth/sdk-package
```

**优势**:
- 实时同步 SDK 更新
- 便于同时开发插件和 SDK
- 支持本地调试和测试

### SDK 核心功能

```typescript
// SDK 主要接口
interface AuthClient {
  // 认证状态检查
  checkAuth(): Promise<{isAuthenticated: boolean, user?: User, statusCode?: number}>
  
  // 登录重定向
  login(redirectUrl?: string): void
  
  // 登出操作
  logout(): Promise<void>
  
  // 用户信息获取
  getUser(): Promise<User | null>
  
  // JWT 状态验证
  verifyStatus(): Promise<VerifyStatusResponse>
  
  // 浏览器指纹识别
  isCurrentHostnameSensitive(): boolean
}
```

## ⚙️ 3. 认证初始化（简化版）

### 双层认证初始化

#### 侧边栏认证（保留原有SDK）

**文件位置**: `src/components/auth/AuthProvider.tsx`

```typescript
// 侧边栏中保留原有的完整认证功能
import { initAuthClient } from "@quote/auth-client/react";

// 在侧边栏启动时初始化认证客户端
useEffect(() => {
  const initAuth = async () => {
    try {
      if (!validateAuthEnv()) {
        throw new Error("认证环境变量未正确配置");
      }

      initAuthClient(
        getAuthServiceApiUrl(),
        getAuthServiceRedirectUrl(),
        {
          sensitiveHostnames: [],
          timeout: 15000,
        }
      );

      console.log("侧边栏认证客户端初始化成功");
      setIsInitialized(true);
      
    } catch (error) {
      console.error("侧边栏认证初始化失败:", error);
      setInitError(error.message);
    }
  };

  initAuth();
}, []);
```

#### 后台脚本认证（简化版）

**文件位置**: `src/background.ts`

```typescript
// 简化的后台脚本认证检查（直接调用API）
async function handleCheckAuthStatus(): Promise<{
  isAuthenticated: boolean;
  error?: string;
}> {
  try {
    // 直接调用认证 API（因为 Background Script 无法使用认证客户端）
    const response = await fetch(API_ENDPOINTS.AUTH_VERIFY, {
      method: "GET",
      credentials: "include", // 包含 cookies
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (response.ok) {
      const data = await response.json();
      return {
        isAuthenticated: data.success === true,
        error: data.success ? undefined : data.message || "认证失败",
      };
    } else {
      return {
        isAuthenticated: false,
        error: `认证检查失败: ${response.status} ${response.statusText}`,
      };
    }
  } catch (error) {
    console.error("=== 认证检查失败 ===", error);
    return {
      isAuthenticated: false,
      error: error instanceof Error ? error.message : String(error),
    };
  }
}
```

### 环境变量配置

**必需变量**:
```bash
# 认证服务 API URL
NEXT_PUBLIC_AUTH_SERVICE_API_URL=https://k.framesound.tech

# 认证服务重定向 URL  
NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL=https://auth.quote.framesound.tech:3010

# 认证服务域名
NEXT_PUBLIC_AUTH_DOMAIN=quote.framesound.tech
```

**配置验证**:
```typescript
export function validateAuthEnv(): boolean {
  try {
    const authServiceApiUrl = getAuthServiceApiUrl();
    const authServiceRedirectUrl = getAuthServiceRedirectUrl();
    const authDomain = getAuthDomain();

    return Boolean(authServiceApiUrl && authServiceRedirectUrl && authDomain);
  } catch (error) {
    console.error("认证环境变量验证失败:", error);
    return false;
  }
}
```

## 🔄 4. 认证状态管理（简化版）

### 侧边栏 React Hook 集成（保留）

```typescript
// 侧边栏中保留原有的完整认证功能
import { useAuth } from "@quote/auth-client/react";

function SidePanel() {
  const { 
    isAuthenticated,    // 认证状态
    loading,           // 加载状态
    error,             // 错误信息
    user,              // 用户信息
    statusCode,        // HTTP状态码
    login,             // 登录方法
    logout,            // 登出方法
    refreshUser,       // 刷新用户信息
    verifyStatus       // 验证状态
  } = useAuth();

  // 根据认证状态渲染不同界面
  if (loading) {
    return <AuthLoading />;
  }

  if (error && !isAuthenticated) {
    return <AuthError error={error} statusCode={statusCode} />;
  }

  if (!isAuthenticated) {
    return <LoginPrompt onLogin={login} />;
  }

  return <MainInterface user={user} onLogout={logout} />;
}
```

### 简化的后台认证状态检查

```typescript
// 后台脚本中的简化认证检查（在background.ts中）
case "CHECK_AUTH_STATUS":
  console.log("=== 收到认证状态检查请求 ===");
  handleCheckAuthStatus()
    .then((result) => {
      console.log("=== 认证状态检查完成 ===", result);
      sendResponse({ success: true, ...result });
    })
    .catch((error) => {
      console.error("=== 认证状态检查失败 ===", error);
      sendResponse({ success: false, error: String(error) });
    });
  return true;
```
```

### 状态类型定义

```typescript
interface AuthState {
  isAuthenticated: boolean;    // 是否已认证
  user: User | null;          // 用户信息
  loading: boolean;           // 加载状态
  error: string | null;       // 错误信息
  statusCode?: number;        // HTTP状态码
}

interface User {
  id: string;                 // 用户ID
  email: string;              // 邮箱
  name?: string;              // 姓名
  avatar?: string;            // 头像
  role?: string;              // 角色
  createdAt?: string;         // 创建时间
  updatedAt?: string;         // 更新时间
}
```

## 🔐 5. 认证流程实现（简化版）

### 侧边栏登录流程（保留）

```typescript
// 1. 用户点击登录按钮（在侧边栏中）
const handleLogin = () => {
  // 重定向到认证服务
  login(window.location.href);
};

// 2. 认证服务处理登录
// 用户在新标签页完成登录

// 3. 自动检测登录完成
// 通过多种机制检测认证状态变化
```

### 简化的认证状态检查

```typescript
// 后台脚本中的简化认证检查（不需要SDK）
async function checkAuthStatus(): Promise<boolean> {
  try {
    const response = await fetch(API_ENDPOINTS.AUTH_VERIFY, {
      method: "GET",
      credentials: "include",
      headers: { "Content-Type": "application/json" }
    });
    
    if (response.ok) {
      const data = await response.json();
      return data.success === true;
    }
    
    return false;
  } catch (error) {
    console.error('认证检查失败:', error);
    return false;
  }
}

// 在文件处理前检查认证状态
if (!(await checkAuthStatus())) {
  throw new Error('用户未登录，请先登录后再进行操作');
}
```
```

### JWT 验证机制

```typescript
// 定期验证 JWT 有效性
const verifyAuthStatus = async () => {
  try {
    const response = await verifyStatus();
    
    if (response.success) {
      setIsAuthenticated(true);
      setError(null);
    } else {
      setIsAuthenticated(false);
      setError(response.message);
    }
    
    setStatusCode(response.statusCode);
    
  } catch (error) {
    setIsAuthenticated(false);
    setError("认证验证失败");
    console.error("认证验证错误:", error);
  }
};
```

## 🔄 6. 自动状态刷新机制（简化版）

### 简化的检测策略

#### 6.1 标签页监听（保留但简化）
```typescript
// 简化的登录标签页监听（在background.ts中）
chrome.tabs.onUpdated.addListener((tabId, changeInfo) => {
  // 只处理 URL 变化
  if (!changeInfo.url) return;

  console.log("=== 标签页更新 ===", { tabId, url: changeInfo.url });

  // 检查是否是登录标签页
  const loginTabInfo = loginTabs.get(tabId);
  if (!loginTabInfo) return;

  // 检查是否完成登录
  const isLoginPage = changeInfo.url.includes("/login");
  const isAuthDomain = changeInfo.url.includes(loginTabInfo.authDomain);
  const isRootPath = changeInfo.url === loginTabInfo.authDomain || 
                     changeInfo.url === `${loginTabInfo.authDomain}/`;
  const hasSuccessIndicator = /\/(success|dashboard|home|profile|account)/i.test(changeInfo.url);
  const isLoginComplete = isAuthDomain && !isLoginPage && (isRootPath || hasSuccessIndicator);

  if (isLoginComplete) {
    console.log("=== 检测到登录完成，通知 SidePanel ===");
    
    // 发送消息给 SidePanel
    chrome.runtime.sendMessage({
      type: "LOGIN_COMPLETE",
      tabId,
    }).catch((error) => {
      console.log("=== 发送登录完成消息失败 ===", error);
    });
    
    // 清理登录标签页记录
    loginTabs.delete(tabId);
  }
});
```
```

#### 6.2 简化的定期检查（仅在侧边栏中）
```typescript
// 简化的定期检查（仅在侧边栏中保留）
useEffect(() => {
  // 只在侧边栏中定期检查认证状态
  if (!isAuthenticated) {
    const interval = setInterval(() => {
      verifyStatus(); // 使用SDK的方法
    }, 30000); // 30秒

    return () => clearInterval(interval);
  }
}, [isAuthenticated, verifyStatus]);

// 窗口焦点监听（仅在侧边栏中）
useEffect(() => {
  const handleFocus = () => {
    if (!isAuthenticated) {
      verifyStatus();
    }
  };
  
  window.addEventListener('focus', handleFocus);
  return () => window.removeEventListener('focus', handleFocus);
}, [isAuthenticated, verifyStatus]);
```

#### 6.3 后台脚本中的认证检查
```typescript
// 后台脚本中不做定期检查，只在需要时检查
case "PROCESS_INTERCEPTED_FILE":
  // 在处理文件前检查认证状态
  const authResult = await handleCheckAuthStatus();
  if (!authResult.isAuthenticated) {
    sendResponse({ 
      success: false, 
      error: '用户未登录，请先登录后再进行操作' 
    });
    return true;
  }
  
  // 继续文件处理...
  handleProcessInterceptedFile(message.data);
```

## 🛡️ 7. 安全特性（简化版）

### HTTP-only Cookie 安全机制

```typescript
// 简化版使用 HTTP-only Cookie，更安全
// 不需要手动管理 JWT token
const authRequest = async (url: string, options: RequestInit = {}) => {
  return fetch(url, {
    ...options,
    credentials: "include", // 自动包含 HTTP-only cookies
    headers: {
      "Content-Type": "application/json",
      ...options.headers,
    },
  });
};

// 在文件上传时使用
const response = await fetch(API_ENDPOINTS.FILE_UPLOAD, {
  method: "POST",
  body: formData,
  mode: "cors",
  credentials: "include", // 包含 cookies（JWT HTTP-only cookie）
});
```

### 简化的安全检查

```typescript
// 简化的认证状态检查（在background.ts中）
async function isUserAuthenticated(): Promise<boolean> {
  try {
    const response = await fetch(API_ENDPOINTS.AUTH_VERIFY, {
      method: "GET",
      credentials: "include", // 使用 HTTP-only cookie
      headers: { "Content-Type": "application/json" },
    });
    
    return response.ok && (await response.json()).success;
  } catch (error) {
    console.error('认证检查失败:', error);
    return false;
  }
}

// 在关键操作前进行认证检查
if (!(await isUserAuthenticated())) {
  throw new Error('用户未登录，请先登录后再进行操作');
}
```
```

## 🎨 8. 认证UI组件

### 登录提示组件

```typescript
export const LoginPrompt: React.FC<{onLogin: () => void}> = ({ onLogin }) => {
  return (
    <div className="auth-prompt">
      <div className="auth-icon">🔐</div>
      <h3>Authentication Required</h3>
      <p>Please log in to access Quote features</p>
      
      <button 
        onClick={onLogin}
        className="login-button"
      >
        Log In
      </button>
      
      {/* 开发环境显示调试信息 */}
      {process.env.NODE_ENV === 'development' && <AuthDebug />}
    </div>
  );
};
```

### 认证错误组件

```typescript
export const AuthError: React.FC<{error: string, statusCode?: number}> = ({ 
  error, 
  statusCode 
}) => {
  const getErrorMessage = () => {
    switch (statusCode) {
      case 401: return "会话已过期，请重新登录";
      case 408: return "请求超时，请检查网络连接";
      case 500: return "服务器异常，请稍后重试";
      default: return error || "认证失败";
    }
  };

  return (
    <div className="auth-error">
      <div className="error-icon">⚠️</div>
      <h3>Authentication Error</h3>
      <p>{getErrorMessage()}</p>
      
      <button onClick={() => window.location.reload()}>
        Retry
      </button>
    </div>
  );
};
```

## 📊 9. 错误处理与调试

### 错误分类处理

```typescript
// HTTP 状态码处理
const handleAuthError = (error: any, statusCode?: number) => {
  switch (statusCode) {
    case 401:
      // 未授权，清理本地状态
      clearAuthState();
      redirectToLogin();
      break;
      
    case 403:
      // 权限不足
      showPermissionError();
      break;
      
    case 408:
      // 请求超时
      showNetworkError();
      break;
      
    case 500:
      // 服务器错误
      showServerError();
      break;
      
    default:
      // 未知错误
      showGenericError(error);
  }
};
```

### 调试信息组件

```typescript
export const AuthDebug: React.FC = () => {
  const isValidConfig = validateAuthEnv();
  
  return (
    <div className="auth-debug">
      <h4>🔧 Debug Info</h4>
      <div className="debug-item">
        <span>Config Valid:</span>
        <span className={isValidConfig ? 'success' : 'error'}>
          {isValidConfig ? '✅' : '❌'}
        </span>
      </div>
      <div className="debug-item">
        <span>API URL:</span>
        <span>{getAuthServiceApiUrl()}</span>
      </div>
      <div className="debug-item">
        <span>Redirect URL:</span>
        <span>{getAuthServiceRedirectUrl()}</span>
      </div>
    </div>
  );
};
```

## 📋 总结（简化版）

Quote 统一身份认证系统（简化版）实现了：

1. **双层认证架构**: 侧边栏保留完整SDK功能，后台脚本使用简化的直接API调用
2. **简化状态管理**: 去除复杂的全局状态管理，依赖HTTP-only Cookie机制
3. **基本自动刷新**: 保留侧边栏中的自动刷新，后台只在需要时检查
4. **HTTP-only Cookie 安全**: 使用更安全的Cookie机制替代手动JWT管理
5. **简化UI组件**: 保留侧边栏中的完整认证界面
6. **直接API调用**: 后台脚本直接调用认证API，无需SDK依赖

简化版本去除了过度复杂的抽象层，保留核心的认证功能，确保了插件与 Quote 生态系统的稳定认证集成。
