<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 150vh; /* 增加页面高度以测试滚动 */
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .drag-demo {
            display: flex;
            align-items: center;
            gap: 10px;
            margin: 15px 0;
            padding: 10px;
            background: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .cursor-demo {
            width: 24px;
            height: 24px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: move;
            user-select: none;
        }
        .spacer {
            height: 100vh;
            background: linear-gradient(45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(-45deg, #f0f0f0 25%, transparent 25%), 
                        linear-gradient(45deg, transparent 75%, #f0f0f0 75%), 
                        linear-gradient(-45deg, transparent 75%, #f0f0f0 75%);
            background-size: 20px 20px;
            background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 工具栏拖拽功能测试</h1>
        
        <div class="success-indicator">
            <strong>✅ 拖拽功能已实现！</strong> Quote图标现在可以拖拽移动工具栏位置。
        </div>

        <div class="feature-highlight">
            <h3>🔧 拖拽功能特性</h3>
            <ol>
                <li><strong>移动光标</strong>：hover和拖拽时都显示移动箭头光标</li>
                <li><strong>拖拽移动</strong>：按住Quote图标可以拖拽工具栏到任意位置</li>
                <li><strong>边界约束</strong>：工具栏不会被拖出视口范围</li>
                <li><strong>保持显示</strong>：拖拽过程中工具栏不会消失</li>
                <li><strong>样式不变</strong>：拖拽时工具栏保持原有样式，无半透明效果</li>
            </ol>
            
            <div class="drag-demo">
                <span>光标效果演示：</span>
                <div class="cursor-demo">📝</div>
                <span>← hover此区域查看移动光标</span>
            </div>
        </div>

        <h2>🧪 测试场景</h2>
        
        <div class="test-case">
            <h3>场景1: 基础拖拽测试</h3>
            <div class="test-text">
                请选择这段文字创建高亮，然后将鼠标悬停在工具栏的Quote图标上，观察光标是否变为移动箭头。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>选择上面的文字并创建高亮</li>
                <li>hover到高亮文本显示工具栏</li>
                <li>将鼠标移动到工具栏右侧的Quote图标上</li>
                <li>观察光标是否变为移动箭头（↔️）</li>
                <li>按住Quote图标并拖拽</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 光标显示移动箭头，可以拖拽工具栏移动</p>
        </div>

        <div class="test-case">
            <h3>场景2: 边界约束测试</h3>
            <div class="test-text">
                测试工具栏拖拽时的边界约束功能，确保工具栏不会被拖出视口范围。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>创建高亮并显示工具栏</li>
                <li>尝试将工具栏拖拽到页面边缘</li>
                <li>尝试拖拽超出视口范围</li>
                <li>观察工具栏是否被约束在可见区域内</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏被约束在视口内，距离边缘10px</p>
        </div>

        <div class="test-case">
            <h3>场景3: 拖拽时不消失测试</h3>
            <div class="test-text">
                验证拖拽过程中工具栏不会因为鼠标离开高亮文本而消失。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>创建高亮并显示工具栏</li>
                <li>开始拖拽工具栏</li>
                <li>在拖拽过程中移动到页面其他区域</li>
                <li>观察工具栏是否保持显示</li>
                <li>释放鼠标完成拖拽</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 拖拽过程中工具栏始终保持显示</p>
        </div>

        <div class="test-case">
            <h3>场景4: 样式保持测试</h3>
            <div class="test-text">
                验证拖拽时工具栏样式不发生变化，保持原有外观。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>观察工具栏的默认样式</li>
                <li>开始拖拽工具栏</li>
                <li>观察拖拽过程中的样式变化</li>
                <li>完成拖拽后观察最终样式</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 拖拽过程中工具栏样式保持不变</p>
        </div>

        <div class="test-case">
            <h3>场景5: 功能兼容性测试</h3>
            <div class="test-text">
                验证拖拽功能不会影响工具栏的其他功能，如按钮点击、滑动动效等。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>拖拽工具栏到新位置</li>
                <li>点击高亮采集按钮测试功能</li>
                <li>在不同高亮间移动测试滑动动效</li>
                <li>测试hover效果和取消高亮功能</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 所有原有功能正常工作</p>
        </div>

        <h2>🔍 技术实现详情</h2>
        
        <div class="feature-highlight">
            <h3>核心技术点</h3>
            <ul>
                <li><strong>光标样式</strong>：CSS cursor: move 实现移动箭头</li>
                <li><strong>拖拽检测</strong>：mousedown/mousemove/mouseup 事件组合</li>
                <li><strong>位置计算</strong>：基于鼠标移动距离计算新位置</li>
                <li><strong>边界约束</strong>：constrainToolbarPosition 方法限制范围</li>
                <li><strong>状态管理</strong>：dragState 跟踪拖拽状态</li>
                <li><strong>防止消失</strong>：拖拽时阻止hideToolbar执行</li>
            </ul>
            
            <h3>用户体验优化</h3>
            <ul>
                <li>✅ 防止文本选择：user-select: none</li>
                <li>✅ 防止默认拖拽：user-drag: none</li>
                <li>✅ 事件阻止：preventDefault 和 stopPropagation</li>
                <li>✅ 实时位置更新：mousemove 中更新工具栏位置</li>
            </ul>
        </div>
    </div>

    <div class="spacer">
        滚动测试区域 - 在此区域创建高亮测试拖拽功能
    </div>

    <div class="container">
        <h2>📋 测试结果记录</h2>
        
        <div class="test-case">
            <h3>预期测试结果</h3>
            <ul>
                <li>✅ Quote图标hover时显示移动光标</li>
                <li>✅ 可以通过拖拽Quote图标移动工具栏</li>
                <li>✅ 工具栏被约束在视口范围内</li>
                <li>✅ 拖拽过程中工具栏不消失</li>
                <li>✅ 拖拽时工具栏样式保持不变</li>
                <li>✅ 拖拽功能不影响其他功能</li>
            </ul>
        </div>

        <div class="test-case">
            <h3>调试信息</h3>
            <p>打开浏览器控制台可以看到拖拽相关的调试日志：</p>
            <ul>
                <li><code>[HighlightSystem] 开始拖拽工具栏</code> - 拖拽开始</li>
                <li><code>[HighlightSystem] 拖拽结束，工具栏位置: {x, y}</code> - 拖拽结束</li>
                <li>实时的位置更新信息</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('工具栏拖拽功能测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，拖拽功能已生效');
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);

        // 监听拖拽相关事件，提供额外的调试信息
        let isDragging = false;
        
        document.addEventListener('mousedown', (e) => {
            const target = e.target;
            if (target.classList && target.classList.contains('quote-icon')) {
                isDragging = true;
                console.log('🎯 检测到Quote图标拖拽开始');
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                console.log('📍 拖拽中，鼠标位置:', { x: e.clientX, y: e.clientY });
            }
        });

        document.addEventListener('mouseup', (e) => {
            if (isDragging) {
                isDragging = false;
                console.log('🏁 拖拽结束');
            }
        });
    </script>
</body>
</html>
