<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工具栏拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
            min-height: 200vh;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .cursor-demo {
            width: 24px;
            height: 24px;
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: move;
            display: flex;
            align-items: center;
            justify-content: center;
            user-select: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 工具栏拖拽功能测试</h1>
        
        <div class="success-indicator">
            <strong>✅ 拖拽功能已实现！</strong> Quote图标现在支持拖拽移动工具栏位置。
        </div>

        <div class="feature-highlight">
            <h3>🔧 拖拽功能特性</h3>
            <ol>
                <li><strong>移动光标</strong>：hover Quote图标时显示移动箭头（cursor: move）</li>
                <li><strong>实时拖拽</strong>：拖拽时工具栏实时跟随鼠标位置</li>
                <li><strong>边界约束</strong>：工具栏不会被拖出视口边界</li>
                <li><strong>拖拽时不消失</strong>：拖拽过程中工具栏保持显示状态</li>
                <li><strong>样式保持</strong>：拖拽时工具栏样式不变，仅透明度略微降低</li>
            </ol>
        </div>

        <h2>🧪 功能测试</h2>
        
        <div class="test-case">
            <h3>测试1: 基础拖拽功能</h3>
            <div class="test-text">
                请选择这段文字创建高亮，然后将鼠标悬停在工具栏的Quote图标上，观察光标是否变为移动箭头。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>选择上面的文字并创建高亮</li>
                <li>将鼠标悬停在工具栏右侧的Quote图标上</li>
                <li>观察光标是否变为移动箭头（↔）</li>
                <li>按住鼠标左键并拖拽</li>
                <li>观察工具栏是否实时跟随鼠标移动</li>
            </ol>
            <p><strong>预期结果</strong>：✅ Quote图标显示移动光标，拖拽时工具栏实时跟随</p>
        </div>

        <div class="test-case">
            <h3>测试2: 边界约束功能</h3>
            <div class="test-text">
                测试工具栏在拖拽时是否会被约束在视口边界内，不会拖出屏幕外。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>创建高亮并显示工具栏</li>
                <li>尝试将工具栏拖拽到屏幕的各个边缘</li>
                <li>尝试拖拽超出屏幕边界</li>
                <li>观察工具栏是否被正确约束</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏始终保持在视口内，不会超出边界</p>
        </div>

        <div class="test-case">
            <h3>测试3: 拖拽状态管理</h3>
            <div class="test-text">
                测试拖拽过程中的状态管理，包括拖拽开始、进行中和结束的状态变化。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>打开浏览器控制台查看日志</li>
                <li>创建高亮并开始拖拽工具栏</li>
                <li>观察控制台中的拖拽状态日志</li>
                <li>释放鼠标结束拖拽</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 控制台显示"开始拖拽工具栏"和"结束拖拽工具栏"日志</p>
        </div>

        <div class="test-case">
            <h3>测试4: 滚动页面拖拽</h3>
            <div class="test-text">
                测试在页面滚动状态下的拖拽功能，验证坐标计算的准确性。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>滚动页面到不同位置</li>
                <li>创建高亮并显示工具栏</li>
                <li>拖拽工具栏到不同位置</li>
                <li>验证拖拽的准确性</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 无论页面滚动位置如何，拖拽都准确跟随鼠标</p>
        </div>

        <h2>🔍 技术实现</h2>
        
        <div class="feature-highlight">
            <h3>核心技术点</h3>
            <ul>
                <li><strong>事件处理</strong>：mousedown、mousemove、mouseup事件链</li>
                <li><strong>坐标计算</strong>：考虑页面滚动的绝对坐标计算</li>
                <li><strong>边界检测</strong>：视口尺寸和工具栏尺寸的约束计算</li>
                <li><strong>状态管理</strong>：拖拽状态的完整生命周期管理</li>
                <li><strong>样式控制</strong>：拖拽时的视觉反馈和样式保持</li>
            </ul>
        </div>

        <!-- 滚动测试区域 -->
        <div style="height: 500px; background: linear-gradient(to bottom, #f8f9fa, #e9ecef); border-radius: 8px; margin: 40px 0; display: flex; align-items: center; justify-content: center; color: #6c757d;">
            <div style="text-align: center;">
                <h3>滚动测试区域</h3>
                <p>这个区域用于测试页面滚动时的拖拽功能</p>
                <p>请在不同滚动位置测试工具栏拖拽</p>
            </div>
        </div>
    </div>

    <script>
        console.log('工具栏拖拽功能测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，拖拽功能已生效');
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);
    </script>
</body>
</html>
