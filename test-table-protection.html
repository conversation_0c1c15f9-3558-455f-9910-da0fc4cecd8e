<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>表格布局保护测试</title>
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
        line-height: 1.6;
        max-width: 1000px;
        margin: 0 auto;
        padding: 20px;
        background-color: #f5f5f5;
      }
      .content {
        background: white;
        padding: 30px;
        border-radius: 8px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        margin-bottom: 20px;
      }
      h1 {
        color: #333;
        border-bottom: 2px solid #007bff;
        padding-bottom: 10px;
      }
      .test-case {
        margin: 30px 0;
        padding: 20px;
        border: 2px solid #ddd;
        border-radius: 8px;
        background-color: #f9f9f9;
      }
      .test-case h3 {
        margin-top: 0;
        color: #555;
        background-color: #e9ecef;
        padding: 10px;
        border-radius: 4px;
      }
      .instructions {
        background-color: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 15px;
        margin: 20px 0;
      }
      table {
        border-collapse: collapse;
        width: 100%;
        margin: 15px 0;
        border: 3px solid #007bff;
        background-color: #ffffff;
      }
      th,
      td {
        border: 2px solid #dc3545;
        padding: 12px;
        text-align: left;
        background-color: #ffffff;
        vertical-align: top;
      }
      th {
        background-color: #f8f9fa;
        font-weight: bold;
        border: 2px solid #28a745;
      }

      /* 添加更明显的边框样式以便观察变化 */
      table tr:nth-child(even) td {
        background-color: #f9f9f9;
      }

      table tr:hover td {
        background-color: #e3f2fd;
      }
      .before-table,
      .after-table {
        margin: 15px 0;
        padding: 10px;
        background-color: #e3f2fd;
        border-left: 4px solid #2196f3;
      }
    </style>
  </head>
  <body>
    <div class="content">
      <h1>表格布局保护测试</h1>

      <div class="instructions">
        <h3>🎯 测试目标</h3>
        <p>
          验证高亮功能在表格环境中不会破坏原有布局。请按照测试案例进行操作，观察表格布局是否保持稳定。
        </p>
      </div>

      <div class="test-case">
        <h3>测试案例1: 纯表格内选择</h3>
        <div class="before-table">表格前的文本内容，用于对比布局变化。</div>

        <table>
          <thead>
            <tr>
              <th>列标题1</th>
              <th>列标题2</th>
              <th>列标题3</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>单元格A1的内容比较长</td>
              <td>单元格B1</td>
              <td>单元格C1</td>
            </tr>
            <tr>
              <td>单元格A2</td>
              <td>单元格B2的内容也比较长</td>
              <td>单元格C2</td>
            </tr>
          </tbody>
        </table>

        <div class="after-table">表格后的文本内容，用于对比布局变化。</div>

        <p>
          <strong>测试方法</strong
          >：选择表格内的任意文本，如"单元格A1的内容比较长"，观察表格布局是否改变。
        </p>
      </div>

      <div class="test-case">
        <h3>测试案例2: 表格外+表格内混合选择 ⭐</h3>
        <div class="before-table">
          这段文本在表格前面，包含重要信息需要高亮。
        </div>

        <table>
          <tr>
            <td>表格内的重要数据1</td>
            <td>表格内的重要数据2</td>
          </tr>
          <tr>
            <td>表格内的重要数据3</td>
            <td>表格内的重要数据4</td>
          </tr>
        </table>

        <div class="after-table">
          这段文本在表格后面，也包含需要高亮的内容。
        </div>

        <p>
          <strong>测试方法</strong
          >：选择从"包含重要信息"到"重要数据4"，跨越表格边界的文本。
        </p>
        <p><strong>⚠️ 关键测试</strong>：这是最容易破坏布局的场景！</p>
      </div>

      <h2>🔍 验证清单</h2>
      <div style="background-color: #f8f9fa; padding: 15px; border-radius: 4px">
        <h4>高亮前后对比检查：</h4>
        <ul>
          <li>✅ 表格列宽保持不变</li>
          <li>✅ 表格行高保持不变</li>
          <li>✅ 单元格内容对齐方式不变</li>
          <li>✅ 表格边框样式不变</li>
          <li>✅ 表格整体位置不变</li>
          <li>✅ 表格前后文本位置不变</li>
          <li>✅ 高亮效果正常显示</li>
          <li>✅ hover和删除功能正常</li>
        </ul>
      </div>

      <h2>🐛 调试信息</h2>
      <div
        id="debug-info"
        style="
          background-color: #f8f9fa;
          padding: 15px;
          border-radius: 4px;
          margin: 15px 0;
          font-family: monospace;
          font-size: 12px;
        "
      >
        <div><strong>实时监控状态：</strong></div>
        <div id="table-status">等待监控启动...</div>
        <div id="highlight-status">高亮状态：无</div>
      </div>

      <p>打开浏览器控制台查看详细日志：</p>
      <ul>
        <li>
          <code>[HighlightSystem] 检测到表格内高亮，应用安全样式</code> -
          表格检测成功
        </li>
        <li>
          <code>[HighlightSystem] 应用强化表格安全样式</code> - 强化保护应用
        </li>
        <li>
          <code>[HighlightSystem] 添加高亮: {elementsCount: X}</code> -
          高亮元素数量
        </li>
        <li>检查高亮元素的CSS类是否包含 <code>quote-highlight-table</code></li>
      </ul>
    </div>

    <script>
      console.log("表格布局保护测试页面加载完成");

      // 增强的布局监控
      function monitorTableLayout() {
        const tables = document.querySelectorAll("table");
        const initialLayouts = [];
        const tableStatusEl = document.getElementById("table-status");
        const highlightStatusEl = document.getElementById("highlight-status");

        tables.forEach((table, index) => {
          const rect = table.getBoundingClientRect();
          const computedStyle = window.getComputedStyle(table);
          initialLayouts[index] = {
            width: rect.width,
            height: rect.height,
            left: rect.left,
            top: rect.top,
            borderCollapse: computedStyle.borderCollapse,
            borderSpacing: computedStyle.borderSpacing,
          };
          console.log(`表格${index + 1}初始布局:`, initialLayouts[index]);
        });

        // 更新状态显示
        function updateStatus() {
          let statusText = `监控${tables.length}个表格 | `;
          let hasChanges = false;

          tables.forEach((table, index) => {
            const rect = table.getBoundingClientRect();
            const initial = initialLayouts[index];

            if (
              Math.abs(rect.width - initial.width) > 1 ||
              Math.abs(rect.height - initial.height) > 1
            ) {
              hasChanges = true;
              statusText += `表格${index + 1}:变化 `;
              console.warn(`⚠️ 表格${index + 1}布局发生变化:`, {
                before: initial,
                after: { width: rect.width, height: rect.height },
              });
            } else {
              statusText += `表格${index + 1}:正常 `;
            }
          });

          tableStatusEl.textContent = statusText;
          tableStatusEl.style.color = hasChanges ? "#dc3545" : "#28a745";

          // 检查高亮元素
          const highlights = document.querySelectorAll(".quote-highlight");
          const tableHighlights = document.querySelectorAll(
            "table .quote-highlight"
          );
          highlightStatusEl.textContent = `总高亮:${highlights.length} | 表格内:${tableHighlights.length}`;
        }

        // 定期检查布局变化
        setInterval(updateStatus, 500);
        updateStatus(); // 立即执行一次
      }

      setTimeout(monitorTableLayout, 2000);
    </script>
  </body>
</html>
