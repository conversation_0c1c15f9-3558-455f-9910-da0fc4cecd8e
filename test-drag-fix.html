<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .highlight-demo {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .debug-info {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 拖拽修复测试</h1>
        
        <div class="success-indicator">
            <strong>🎯 修复内容</strong><br>
            1. 修复了事件监听器绑定问题<br>
            2. 防止拖拽过程中重新渲染工具栏<br>
            3. 移除了影响文本选中的userSelect设置
        </div>

        <h2>🧪 测试步骤</h2>
        
        <div class="test-case">
            <h3>测试1: 实时拖拽验证</h3>
            <div class="test-text">
                请选择这段文字创建高亮，然后按住Quote图标拖拽工具栏。现在应该能看到工具栏实时跟随鼠标移动，而不是在鼠标抬起时才跳到最终位置。
            </div>
            <p><strong>预期结果</strong>：✅ 工具栏实时跟随鼠标移动</p>
        </div>

        <div class="test-case">
            <h3>测试2: 文本选中态保持</h3>
            <div class="highlight-demo">
                这段文字用于测试拖拽时文本选中态是否保持。请先选择这段文字创建高亮，然后拖拽工具栏，观察高亮文字的选中态是否保持显示。
            </div>
            <p><strong>预期结果</strong>：✅ 拖拽时高亮文字保持选中态显示</p>
        </div>

        <div class="test-case">
            <h3>测试3: 多段高亮拖拽</h3>
            <div class="test-text">
                第一段测试文字：这是用于测试拖拽功能的第一段文字内容。
            </div>
            <div class="test-text">
                第二段测试文字：这是用于测试拖拽功能的第二段文字内容。
            </div>
            <p><strong>测试方法</strong>：</p>
            <ol>
                <li>分别在两段文字中创建高亮</li>
                <li>hover第一段高亮显示工具栏</li>
                <li>拖拽工具栏到新位置</li>
                <li>hover第二段高亮</li>
                <li>观察工具栏行为</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 拖拽后hover切换正常工作</p>
        </div>

        <div class="debug-info" id="debugInfo">
            <strong>调试信息：</strong><br>
            <div id="debugContent">等待扩展加载...</div>
        </div>

        <h2>🔍 修复技术细节</h2>
        
        <div class="test-case">
            <h3>问题1: 事件监听器绑定</h3>
            <p><strong>原因</strong>：每次bind()都创建新函数引用，导致removeEventListener失效</p>
            <p><strong>修复</strong>：预绑定事件处理器到类属性</p>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
// 修复前
icon.addEventListener("mousedown", this.handleDragStart.bind(this));

// 修复后  
private boundHandleDragStart = this.handleDragStart.bind(this);
icon.addEventListener("mousedown", this.boundHandleDragStart);
            </pre>
        </div>

        <div class="test-case">
            <h3>问题2: 拖拽过程中重新渲染</h3>
            <p><strong>原因</strong>：renderToolbar()清空容器并重新创建DOM，中断拖拽</p>
            <p><strong>修复</strong>：拖拽时跳过重新渲染</p>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
// 拖拽过程中不重新渲染，避免中断拖拽
if (this.dragState.isDragging) {
  console.log("[HighlightSystem] 拖拽中，跳过重新渲染");
  return;
}
            </pre>
        </div>

        <div class="test-case">
            <h3>问题3: 文本选中态消失</h3>
            <p><strong>原因</strong>：设置document.body.style.userSelect = "none"影响全局</p>
            <p><strong>修复</strong>：移除全局userSelect设置</p>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px;">
// 修复前
document.body.style.userSelect = "none";

// 修复后
// 移除全局userSelect设置，避免影响文本选中态
            </pre>
        </div>
    </div>

    <script>
        console.log('拖拽修复测试页面加载完成');
        
        const debugContent = document.getElementById('debugContent');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                debugContent.innerHTML = `
                    ✅ HighlightSystem已加载<br>
                    ✅ 拖拽功能已修复<br>
                    ✅ 事件监听器已正确绑定<br>
                    ✅ 渲染逻辑已优化
                `;
            } else {
                debugContent.innerHTML = `
                    ❌ HighlightSystem未找到<br>
                    请检查扩展是否正确加载
                `;
            }
        }, 2000);

        // 监听拖拽事件
        let dragStartTime = 0;
        let dragMoveCount = 0;
        
        document.addEventListener('mousedown', (e) => {
            if (e.target.closest('.quote-icon')) {
                dragStartTime = Date.now();
                dragMoveCount = 0;
                console.log('🎯 开始拖拽Quote图标');
                
                debugContent.innerHTML += '<br>🎯 拖拽开始...';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (dragStartTime > 0) {
                dragMoveCount++;
                if (dragMoveCount % 10 === 0) { // 每10次更新一次，避免过于频繁
                    console.log(`🔄 拖拽移动中... (${dragMoveCount}次)`);
                }
            }
        });

        document.addEventListener('mouseup', (e) => {
            if (dragStartTime > 0) {
                const dragDuration = Date.now() - dragStartTime;
                console.log(`✅ 拖拽结束，持续时间: ${dragDuration}ms，移动次数: ${dragMoveCount}`);
                
                debugContent.innerHTML += `<br>✅ 拖拽结束: ${dragDuration}ms, ${dragMoveCount}次移动`;
                
                dragStartTime = 0;
                dragMoveCount = 0;
            }
        });

        // 监听工具栏状态变化
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'childList') {
                    const toolbar = document.querySelector('.quote-selection-toolbar');
                    if (toolbar) {
                        console.log('📍 工具栏位置:', {
                            left: toolbar.style.left,
                            top: toolbar.style.top,
                            isDragging: toolbar.classList.contains('dragging')
                        });
                    }
                }
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    </script>
</body>
</html>
