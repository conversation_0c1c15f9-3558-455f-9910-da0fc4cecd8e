<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表格布局保护测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .content {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .test-case h3 {
            margin-top: 0;
            color: #555;
            background-color: #e9ecef;
            padding: 10px;
            border-radius: 4px;
        }
        .instructions {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        
        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background-color: white;
        }
        
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
            vertical-align: top;
        }
        
        th {
            background-color: #f8f9fa;
            font-weight: 600;
            color: #495057;
        }
        
        tr:nth-child(even) {
            background-color: #f8f9fa;
        }
        
        tr:hover {
            background-color: #e9ecef;
        }
        
        .complex-table {
            border: 2px solid #007bff;
        }
        
        .complex-table th {
            background-color: #007bff;
            color: white;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-active { background-color: #28a745; }
        .status-pending { background-color: #ffc107; }
        .status-inactive { background-color: #dc3545; }
    </style>
</head>
<body>
    <div class="content">
        <h1>表格布局保护测试</h1>
        
        <div class="instructions">
            <h3>📋 测试说明</h3>
            <p><strong>目标</strong>：验证高亮功能不会破坏表格布局</p>
            <p><strong>测试方法</strong>：</p>
            <ol>
                <li>选择表格内的文本进行高亮</li>
                <li>选择跨表格内外的文本进行高亮</li>
                <li>观察表格布局是否保持不变</li>
                <li>检查控制台日志，确认使用了非侵入式高亮</li>
            </ol>
        </div>

        <div class="test-case">
            <h3>测试案例1: 简单表格</h3>
            <p>这是表格外的文本，可以正常高亮。</p>
            
            <table>
                <thead>
                    <tr>
                        <th>姓名</th>
                        <th>职位</th>
                        <th>部门</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>张三</td>
                        <td>前端开发工程师</td>
                        <td>技术部</td>
                        <td><span class="status-indicator status-active"></span>在职</td>
                    </tr>
                    <tr>
                        <td>李四</td>
                        <td>产品经理</td>
                        <td>产品部</td>
                        <td><span class="status-indicator status-pending"></span>试用期</td>
                    </tr>
                    <tr>
                        <td>王五</td>
                        <td>UI设计师</td>
                        <td>设计部</td>
                        <td><span class="status-indicator status-inactive"></span>离职</td>
                    </tr>
                </tbody>
            </table>
            
            <p>这是表格后的文本，也可以正常高亮。</p>
            
            <p><strong>测试重点</strong>：选择从"这是表格外的文本"到"离职"的跨表格文本，观察表格布局是否改变。</p>
        </div>

        <div class="test-case">
            <h3>测试案例2: 复杂表格</h3>
            <p>包含复杂内容的表格测试。</p>
            
            <table class="complex-table">
                <thead>
                    <tr>
                        <th>项目名称</th>
                        <th>负责人</th>
                        <th>进度</th>
                        <th>预计完成时间</th>
                        <th>备注</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>
                            <strong>Quote浏览器扩展</strong><br>
                            <small>法律文档采集工具</small>
                        </td>
                        <td>
                            开发团队<br>
                            <em>5人小组</em>
                        </td>
                        <td>85%</td>
                        <td>2024年12月31日</td>
                        <td>
                            <ul style="margin: 0; padding-left: 20px;">
                                <li>高亮功能已完成</li>
                                <li>表格布局保护测试中</li>
                                <li>待优化性能</li>
                            </ul>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <strong>统一认证系统</strong><br>
                            <small>用户身份验证</small>
                        </td>
                        <td>
                            后端团队<br>
                            <em>3人小组</em>
                        </td>
                        <td>60%</td>
                        <td>2024年11月15日</td>
                        <td>
                            API接口设计完成，<br>
                            前端集成进行中
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <p><strong>测试重点</strong>：选择表格内的复杂内容，包括嵌套的HTML元素，确保布局稳定。</p>
        </div>

        <h2>🔍 预期行为</h2>
        <div style="background-color: #d4edda; border: 1px solid #c3e6cb; border-radius: 4px; padding: 15px;">
            <h4>✅ 正确行为：</h4>
            <ul>
                <li><strong>表格布局不变</strong>：高亮后表格的行高、列宽、对齐方式保持不变</li>
                <li><strong>非侵入式高亮</strong>：控制台显示"使用非侵入式高亮"</li>
                <li><strong>视觉效果正常</strong>：高亮背景色正确显示</li>
                <li><strong>交互功能完整</strong>：hover显示工具栏，可以取消高亮</li>
                <li><strong>混合选择支持</strong>：表格内外混合选择正常工作</li>
            </ul>
        </div>

        <h2>🐛 调试信息</h2>
        <p>打开浏览器开发者工具，在Console中查看：</p>
        <ul>
            <li><code>[HighlightSystem] 检测到混合表格选择，使用非侵入式高亮</code></li>
            <li><code>[HighlightSystem] 检测到表格选择，使用非侵入式高亮</code></li>
            <li><code>[HighlightSystem] 非侵入式高亮创建完成</code></li>
        </ul>
    </div>

    <script>
        console.log('表格布局保护测试页面加载完成');
    </script>
</body>
</html>
