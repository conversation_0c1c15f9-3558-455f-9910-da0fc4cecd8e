<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Hover工具栏动效测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        h1, h2 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-indicator {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
            color: #155724;
        }
        .test-text {
            background-color: #fff;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            line-height: 1.8;
        }
        .feature-highlight {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .code-example {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .highlight-demo {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
            margin: 0 2px;
        }
        .animation-demo {
            display: flex;
            gap: 20px;
            margin: 20px 0;
        }
        .demo-column {
            flex: 1;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #fff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✨ Hover工具栏动效测试</h1>
        
        <div class="success-indicator">
            <strong>🎉 新功能上线！</strong> Hover工具栏现在支持右上角对齐和滑动动效。
        </div>

        <div class="feature-highlight">
            <h3>🔧 新功能特性</h3>
            <ol>
                <li><strong>右上角对齐</strong>：工具栏右边缘与高亮文本右边缘对齐，显示在上方</li>
                <li><strong>滑动动效</strong>：鼠标在高亮文本间移动时，工具栏平滑滑动而不是消失重现</li>
                <li><strong>视觉增强</strong>：高亮文本hover时有轻微缩放效果</li>
                <li><strong>智能状态管理</strong>：避免不必要的动画触发</li>
            </ol>
            
            <div class="code-example">
                <strong>技术实现：</strong><br>
                • 定位方式：右上角对齐 (right - toolbarWidth, top - toolbarHeight - 5px)<br>
                • 动画效果：CSS transform + cubic-bezier缓动<br>
                • 动画时长：300ms<br>
                • 状态管理：防止重复触发和智能延迟隐藏
            </div>
        </div>

        <h2>🧪 测试场景</h2>
        
        <div class="test-case">
            <h3>场景1: 基础右上角对齐测试</h3>
            <div class="test-text">
                请先选择这段文字创建高亮，然后将鼠标悬停在高亮文本上，观察工具栏是否出现在右上角位置。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>选择上面的文字并创建高亮</li>
                <li>将鼠标悬停在高亮文本上</li>
                <li>观察工具栏位置是否在高亮文本的右上角</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏右边缘与高亮文本右边缘对齐，显示在上方</p>
        </div>

        <div class="test-case">
            <h3>场景2: 滑动动效测试</h3>
            <div class="animation-demo">
                <div class="demo-column">
                    <h4>第一段高亮文本</h4>
                    <div class="test-text">
                        这是第一段需要高亮的文本内容，请选择这段文字并创建高亮。
                    </div>
                </div>
                <div class="demo-column">
                    <h4>第二段高亮文本</h4>
                    <div class="test-text">
                        这是第二段需要高亮的文本内容，也请选择这段文字并创建高亮。
                    </div>
                </div>
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>分别选择两段文字并创建高亮</li>
                <li>将鼠标悬停在第一段高亮文本上</li>
                <li>快速移动鼠标到第二段高亮文本上</li>
                <li>观察工具栏是否平滑滑动而不是消失重现</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏平滑滑动到新位置，动画时长300ms</p>
        </div>

        <div class="test-case">
            <h3>场景3: 复杂布局测试</h3>
            <div class="test-text">
                这段文本包含<strong>粗体高亮文本</strong>和<em>斜体高亮文本</em>，还有<span style="color: blue;">彩色高亮文本</span>，测试在复杂布局中的工具栏定位和动效。
            </div>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>分别选择粗体、斜体、彩色文本并创建高亮</li>
                <li>在这些高亮文本之间快速移动鼠标</li>
                <li>观察工具栏的定位准确性和动效流畅性</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 工具栏准确定位到每个高亮文本的右上角，动效流畅</p>
        </div>

        <div class="test-case">
            <h3>场景4: 表格内高亮测试</h3>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        表格第一个单元格的高亮文本内容
                    </td>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        表格第二个单元格的高亮文本内容
                    </td>
                </tr>
                <tr>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        表格第三个单元格的高亮文本内容
                    </td>
                    <td style="border: 1px solid #ddd; padding: 10px;" class="test-text">
                        表格第四个单元格的高亮文本内容
                    </td>
                </tr>
            </table>
            <p><strong>测试步骤</strong>：</p>
            <ol>
                <li>在不同单元格中创建高亮文本</li>
                <li>在表格单元格的高亮文本之间移动鼠标</li>
                <li>观察工具栏在表格环境中的表现</li>
            </ol>
            <p><strong>预期结果</strong>：✅ 表格内的高亮文本也能正确显示工具栏和动效</p>
        </div>

        <h2>🔍 调试信息</h2>
        <div class="feature-highlight">
            <p>打开浏览器控制台可以看到详细的调试日志：</p>
            <ul>
                <li><code>[HighlightSystem] 首次显示hover工具栏</code> - 首次hover显示</li>
                <li><code>[HighlightSystem] 滑动工具栏到新位置</code> - 滑动动效触发</li>
                <li><code>[HighlightSystem] 工具栏滑动完成</code> - 动画完成</li>
                <li><code>[HighlightSystem] 隐藏hover工具栏</code> - 工具栏隐藏</li>
            </ul>
        </div>
    </div>

    <script>
        console.log('Hover工具栏动效测试页面加载完成');
        
        // 检查扩展状态
        setTimeout(() => {
            const highlightSystem = window.highlightSystemInstance;
            if (highlightSystem) {
                console.log('✅ HighlightSystem已加载，hover动效功能已生效');
                
                // 检查hover状态管理
                if (highlightSystem.hoverState) {
                    console.log('✅ Hover状态管理已初始化');
                } else {
                    console.log('ℹ️ Hover状态管理可能在私有属性中');
                }
            } else {
                console.warn('❌ HighlightSystem未找到，请检查扩展是否正确加载');
            }
        }, 2000);

        // 监听鼠标事件，提供额外的调试信息
        document.addEventListener('mouseover', (e) => {
            const target = e.target;
            if (target.classList && target.classList.contains('quote-highlight')) {
                const highlightId = target.getAttribute('data-highlight-id');
                console.log('🎯 鼠标悬停在高亮文本上:', {
                    highlightId,
                    text: target.textContent.substring(0, 20) + '...',
                    rect: target.getBoundingClientRect()
                });
            }
        });

        document.addEventListener('mouseout', (e) => {
            const target = e.target;
            if (target.classList && target.classList.contains('quote-highlight')) {
                console.log('👋 鼠标离开高亮文本');
            }
        });
    </script>
</body>
</html>
