# 高亮采集系统调试指南

## 🔧 调试步骤

### 1. 加载扩展
1. 打开 Chrome 浏览器
2. 访问 `chrome://extensions/`
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目的 `dist` 目录

### 2. 打开测试页面
访问项目根目录下的 `test-highlight.html` 文件

### 3. 检查控制台日志
1. 按 F12 打开开发者工具
2. 切换到 Console 标签
3. 查看是否有以下日志：

```
[Content Script] 加载完成
[HighlightSystem] 开始初始化高亮系统
[HighlightSystem] 当前站点支持高亮系统: 
[HighlightSystem] 创建HighlightSystem实例...
[HighlightSystem] 初始化完成
```

### 4. 测试文本选择
1. 选中页面中的任意文本
2. 查看控制台是否有以下日志：

```
[HighlightSystem] 鼠标抬起，检查选择: {hasSelection: true, isCollapsed: false, selectedText: "..."}
[HighlightSystem] 显示工具栏: {selectedText: "...", position: {x: ..., y: ...}}
[HighlightSystem] showToolbar 调用: {...}
[HighlightSystem] renderToolbar 调用: {...}
[HighlightSystem] 工具栏渲染完成
```

### 5. 检查DOM元素
在控制台中运行以下命令检查容器是否创建：

```javascript
// 检查工具栏容器
document.getElementById('quote-selection-toolbar-container')

// 检查通知栏容器  
document.getElementById('quote-notification-container')

// 检查样式是否注入
document.getElementById('quote-highlight-styles')
```

## 🐛 常见问题排查

### 问题1: 没有初始化日志
**可能原因**: 
- 扩展未正确加载
- Content Script 未执行

**解决方案**:
1. 重新加载扩展
2. 刷新测试页面
3. 检查 `chrome://extensions/` 中是否有错误

### 问题2: 初始化成功但选择文本无反应
**可能原因**:
- 事件监听器未绑定
- React 组件渲染失败

**解决方案**:
1. 检查控制台是否有 React 相关错误
2. 确认 DOM 容器是否创建成功

### 问题3: 工具栏不显示
**可能原因**:
- CSS 样式未正确注入
- 位置计算错误
- z-index 层级问题

**解决方案**:
1. 检查样式元素是否存在
2. 查看工具栏元素的 style 属性
3. 调整 z-index 值

## 📋 调试检查清单

- [ ] 扩展已正确加载到 Chrome
- [ ] 测试页面已打开
- [ ] 控制台显示初始化成功日志
- [ ] DOM 容器元素已创建
- [ ] 样式已正确注入
- [ ] 选择文本时有相应日志
- [ ] 工具栏元素已渲染到 DOM

## 🔍 高级调试

### 手动触发工具栏显示
在控制台中运行：

```javascript
// 获取高亮系统实例
const highlightSystem = window.highlightSystemInstance;

// 手动显示工具栏
if (highlightSystem) {
  highlightSystem.showToolbar({x: 100, y: 100}, 'add');
}
```

### 检查 React 组件状态
```javascript
// 检查工具栏容器内容
const container = document.getElementById('quote-selection-toolbar-container');
console.log('工具栏容器内容:', container.innerHTML);
```

## 📞 获取帮助

如果以上步骤都无法解决问题，请提供：
1. 控制台完整日志
2. 扩展加载状态截图
3. DOM 检查结果
4. 浏览器版本信息
