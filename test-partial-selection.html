<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>部分文本选择测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }
        .test-case {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .problem-demo {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .remio-example {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 4px;
            padding: 15px;
            margin: 20px 0;
        }
        .debug-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 部分文本选择问题分析</h1>
        
        <div class="problem-demo">
            <h3>❌ 当前问题</h3>
            <p><strong>现象</strong>：选择一段话中的部分文本时，无法创建高亮元素</p>
            <p><strong>测试文本</strong>：请尝试只选择下面这段话中的部分文字（不要选择整段）</p>
            <p>criminal; civil public interest litigation case incidental to criminal proceedings; crime of illegal mining; cross-administrative divisions; designated jurisdiction; restoration of the affected area</p>
        </div>

        <div class="remio-example">
            <h3>✅ Remio的正确实现</h3>
            <p><strong>效果</strong>：可以选择任意部分文本并成功高亮</p>
            <p><strong>示例HTML</strong>：</p>
            <div class="debug-info">
&lt;li&gt;criminal; civil public interest litigation case incidental to criminal proceedings; crime of illegal m&lt;span class="remio-ai-plugin-highlight" data-id="3508B2C4-E344-4CA3-955F-F3C7B164773D"&gt;ining; cross-adm&lt;/span&gt;inistrative divisions; designated jurisdiction; restoration of the affected area&lt;/li&gt;
            </div>
        </div>

        <div class="test-case">
            <h3>🧪 测试案例</h3>
            
            <h4>案例1: 单个文本节点内的部分选择</h4>
            <p>这是一段连续的文本内容，请尝试只选择其中的一部分，比如"连续的文本"这几个字。</p>
            
            <h4>案例2: 跨越HTML元素的部分选择</h4>
            <p>这段文本包含<strong>粗体部分</strong>和<em>斜体部分</em>，请尝试选择跨越这些元素的部分文字。</p>
            
            <h4>案例3: 列表项中的部分选择</h4>
            <ul>
                <li>criminal; civil public interest litigation case incidental to criminal proceedings; crime of illegal mining; cross-administrative divisions; designated jurisdiction; restoration of the affected area</li>
                <li>另一个列表项，包含更多的文本内容用于测试部分选择功能</li>
            </ul>
        </div>

        <div id="debug-output" class="debug-info">
            <strong>调试信息：</strong><br>
            等待文本选择...
        </div>
    </div>

    <script>
        // 监听文本选择事件
        document.addEventListener('mouseup', () => {
            const selection = window.getSelection();
            const debugOutput = document.getElementById('debug-output');
            
            if (selection && !selection.isCollapsed) {
                const selectedText = selection.toString();
                const range = selection.getRangeAt(0);
                
                // 分析选择范围
                const rangeInfo = {
                    selectedText: selectedText,
                    textLength: selectedText.length,
                    startContainer: range.startContainer.nodeName,
                    endContainer: range.endContainer.nodeName,
                    startOffset: range.startOffset,
                    endOffset: range.endOffset,
                    commonAncestor: range.commonAncestorContainer.nodeName,
                    isPartialSelection: isPartialTextSelection(range)
                };
                
                debugOutput.innerHTML = `
                    <strong>选择分析：</strong><br>
                    文本: "${selectedText}"<br>
                    长度: ${rangeInfo.textLength}<br>
                    起始容器: ${rangeInfo.startContainer}<br>
                    结束容器: ${rangeInfo.endContainer}<br>
                    起始偏移: ${rangeInfo.startOffset}<br>
                    结束偏移: ${rangeInfo.endOffset}<br>
                    公共祖先: ${rangeInfo.commonAncestor}<br>
                    是否部分选择: ${rangeInfo.isPartialSelection ? '是' : '否'}
                `;
                
                console.log('文本选择详情:', rangeInfo);
                
                // 模拟TextHighlighter的处理过程
                simulateTextHighlighter(range);
            } else {
                debugOutput.innerHTML = '<strong>调试信息：</strong><br>等待文本选择...';
            }
        });
        
        function isPartialTextSelection(range) {
            // 检查是否为部分文本选择
            if (range.startContainer === range.endContainer && 
                range.startContainer.nodeType === Node.TEXT_NODE) {
                const textNode = range.startContainer;
                const fullLength = textNode.textContent.length;
                return range.startOffset > 0 || range.endOffset < fullLength;
            }
            return true; // 跨容器的选择通常是部分选择
        }
        
        function simulateTextHighlighter(range) {
            console.log('=== 模拟TextHighlighter处理过程 ===');
            
            // 模拟getTextFragments方法
            const walker = document.createTreeWalker(
                range.commonAncestorContainer,
                NodeFilter.SHOW_TEXT,
                {
                    acceptNode: (node) => {
                        const textNode = node;
                        
                        // 排除空白字符节点
                        if (!textNode.textContent?.trim()) {
                            return NodeFilter.FILTER_REJECT;
                        }
                        
                        // 检查是否与选择范围相交
                        return range.intersectsNode(textNode) 
                            ? NodeFilter.FILTER_ACCEPT 
                            : NodeFilter.FILTER_REJECT;
                    }
                }
            );
            
            const textNodes = [];
            let node;
            while (node = walker.nextNode()) {
                textNodes.push(node);
            }
            
            console.log('找到的文本节点数量:', textNodes.length);
            
            textNodes.forEach((textNode, index) => {
                console.log(`文本节点 ${index}:`, {
                    content: textNode.textContent,
                    length: textNode.textContent.length,
                    parent: textNode.parentElement.tagName
                });
                
                // 模拟calculateTextFragment
                const fragment = calculateTextFragment(range, textNode);
                if (fragment) {
                    console.log(`片段 ${index}:`, fragment);
                } else {
                    console.log(`片段 ${index}: null (被过滤)`);
                }
            });
        }
        
        function calculateTextFragment(range, textNode) {
            try {
                let startOffset = 0;
                let endOffset = textNode.textContent?.length || 0;
                
                // 如果选择范围的起点在这个文本节点内
                if (range.startContainer === textNode) {
                    startOffset = Math.max(startOffset, range.startOffset);
                } else if (range.comparePoint(textNode, 0) > 0) {
                    return null;
                }
                
                // 如果选择范围的终点在这个文本节点内
                if (range.endContainer === textNode) {
                    endOffset = Math.min(endOffset, range.endOffset);
                } else if (range.comparePoint(textNode, textNode.textContent?.length || 0) < 0) {
                    return null;
                }
                
                // 验证范围有效性
                if (startOffset >= endOffset) {
                    return null;
                }
                
                return {
                    textNode,
                    startOffset,
                    endOffset,
                    text: textNode.textContent.substring(startOffset, endOffset)
                };
            } catch (error) {
                console.warn('计算文本片段失败:', error);
                return null;
            }
        }
    </script>
</body>
</html>
