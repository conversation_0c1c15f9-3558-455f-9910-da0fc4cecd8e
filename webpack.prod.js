const { merge } = require("webpack-merge");
const common = require("./webpack.common.js");
const TerserPlugin = require("terser-webpack-plugin");

// 根据 NODE_ENV 决定是否压缩
const isProduction = process.env.NODE_ENV === "production";

module.exports = merge(common, {
  mode: isProduction ? "production" : "development",
  devtool: isProduction ? false : "source-map",
  optimization: {
    minimize: isProduction,
    minimizer: isProduction
      ? [
          new TerserPlugin({
            extractComments: false,
            terserOptions: {
              compress: {
                drop_console: true,
              },
              // 保留环境变量相关的字符串，避免过度压缩
              mangle: {
                reserved: ["process", "env"],
              },
            },
          }),
        ]
      : [],
  },
});
