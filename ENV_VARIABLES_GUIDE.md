# 环境变量配置指南

## 🎯 概述

本项目使用 Webpack 在构建阶段注入环境变量，确保敏感信息不会被打包进最终的插件文件中。这种方式比运行时读取 .env 文件更安全，也符合浏览器扩展的最佳实践。

## 🔧 工作原理

### 构建时注入 vs 运行时读取

**✅ 构建时注入（当前方案）**

- 环境变量在 Webpack 构建时被替换为字面量
- 最终打包文件中不包含 .env 文件
- 更安全，性能更好
- 符合浏览器扩展安全策略

**❌ 运行时读取（不推荐）**

- 需要将 .env 文件打包进插件
- 存在安全风险
- 可能违反浏览器扩展商店政策

### 环境变量处理流程

```
1. 读取 .env 文件 → 2. 读取 .env.{NODE_ENV} 文件 → 3. 合并系统环境变量
                                                    ↓
4. 过滤 NEXT_PUBLIC_ 前缀变量 ← 5. 验证必需变量 ← 6. 注入到代码中
```

## 📁 文件结构

```
project/
├── .env.example          # 环境变量模板
├── .env                  # 本地开发环境变量（不提交）
├── .env.development      # 开发环境特定变量（可选）
├── .env.staging          # 测试环境特定变量（可选）
├── .env.production       # 生产环境特定变量（可选）
├── webpack.env.js        # 环境变量处理模块
└── webpack.common.js     # Webpack 主配置
```

## 🔑 环境变量类型

### 必需变量（Required）

这些变量必须设置，否则构建会失败：

```bash
# 认证服务 API URL
NEXT_PUBLIC_AUTH_SERVICE_API_URL=https://k.framesound.tech

# 文件服务 API URL
API_BASE_URL=https://f.framesound.tech:8000

# 默认项目 ID（UUID 格式，请填入您的实际项目ID）
NEXT_PUBLIC_DEFAULT_PROJECT_ID=your-project-id-uuid
```

### 可选变量（Optional）

这些变量有默认值，可以不设置：

```bash
# 请求超时时间（默认：15000ms）
NEXT_PUBLIC_REQUEST_TIMEOUT=15000

# 认证服务重定向 URL
NEXT_PUBLIC_AUTH_SERVICE_REDIRECT_URL=https://auth.quote.framesound.tech:3010

# 认证服务域名
NEXT_PUBLIC_AUTH_DOMAIN=quote.framesound.tech
```

## 🚀 使用方法

### 1. 初始设置

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑 .env 文件，填入实际值
vim .env
```

### 2. 验证环境变量

```bash
# 检查当前环境变量配置
npm run env:check

# 验证特定环境的配置
NODE_ENV=production npm run env:validate
```

### 3. 构建项目

```bash
# 开发环境构建
npm run dev

# 生产环境构建
npm run build

# 特定环境构建
npm run build:staging
```

## 🛡️ 安全最佳实践

### 1. 环境变量命名规范

- **客户端变量**：使用 `NEXT_PUBLIC_` 前缀
- **服务端变量**：不使用前缀（不会被注入）
- **敏感信息**：永远不要使用 `NEXT_PUBLIC_` 前缀

### 2. 文件管理

```bash
# ✅ 应该提交的文件
.env.example        # 环境变量模板
webpack.env.js      # 环境变量处理逻辑

# ❌ 不应该提交的文件
.env               # 包含真实值
.env.local         # 本地覆盖
.env.*.local       # 本地环境特定覆盖
```

### 3. 生产环境部署

```bash
# 方式1：使用环境文件
echo "NEXT_PUBLIC_AUTH_SERVICE_API_URL=https://prod-api.example.com" > .env.production
npm run build

# 方式2：使用系统环境变量
export NEXT_PUBLIC_AUTH_SERVICE_API_URL=https://prod-api.example.com
npm run build

# 方式3：CI/CD 中设置
# 在 GitHub Actions、Jenkins 等 CI/CD 系统中设置环境变量
```

## 🔍 故障排除

### 常见错误

**1. 缺少必需环境变量**

```
❌ 缺少必需的环境变量:
   - NEXT_PUBLIC_AUTH_SERVICE_API_URL
```

**解决方案**：检查 .env 文件，确保所有必需变量都已设置

**2. 环境变量格式错误**

```
❌ 环境变量验证失败:
   - NEXT_PUBLIC_AUTH_SERVICE_API_URL 必须是有效的 HTTP/HTTPS URL
```

**解决方案**：检查 URL 格式，确保以 `http://` 或 `https://` 开头

**3. 构建时变量未注入**

```javascript
// 错误：运行时 process.env 为空
console.log(process.env.NEXT_PUBLIC_API_URL); // undefined
```

**解决方案**：确保变量名有 `NEXT_PUBLIC_` 前缀，重新构建项目

### 调试技巧

```bash
# 1. 检查环境变量加载
npm run env:check

# 2. 查看构建日志
npm run build -- --verbose

# 3. 检查打包后的代码
# 在 dist/ 目录中搜索环境变量值
grep -r "your-api-url" dist/
```

## 📚 相关文件

- `webpack.env.js` - 环境变量处理核心逻辑
- `webpack.common.js` - Webpack 主配置
- `src/config/api.ts` - API 配置使用环境变量
- `.env.example` - 环境变量模板

## 🔄 更新环境变量

当需要添加新的环境变量时：

1. 更新 `webpack.env.js` 中的 `REQUIRED_ENV_VARS` 或 `OPTIONAL_ENV_VARS`
2. 更新 `.env.example` 文件
3. 更新 `src/config/api.ts` 中的使用逻辑
4. 更新本文档

---

**⚠️ 重要提醒**：

- 永远不要提交包含真实值的 .env 文件
- 定期检查 .gitignore 确保 .env\* 文件被忽略
- 在生产环境中使用强密码和安全的 API 端点
